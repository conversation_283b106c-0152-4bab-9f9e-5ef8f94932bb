.import-template-dialog {
  padding: 16px;
  max-width: 800px;

  .template-table {
    margin: 16px 0;
    max-height: 300px;
    overflow-y: auto;
  }

  .example-section {
    margin: 16px 0;

    .example-code {
      background-color: #f5f5f5;
      padding: 10px;
      border-radius: 4px;
      font-family: monospace;
      overflow-x: auto;
    }
  }

  .currency-explanation {
    margin: 16px 0;
    padding: 12px;
    background-color: #e3f2fd;
    border-left: 4px solid #2196F3;
    border-radius: 4px;

    h3 {
      color: #0d47a1;
      margin-top: 0;
    }

    ul {
      columns: 2;
      column-gap: 24px;

      @media (max-width: 600px) {
        columns: 1;
      }

      li {
        margin-bottom: 4px;
      }
    }
  }

  .note {
    font-style: italic;
    color: #666;
    margin-top: 12px;
  }
}

table {
  width: 100%;
}

mat-dialog-actions {
  margin-top: 20px;
  padding: 0;
}
