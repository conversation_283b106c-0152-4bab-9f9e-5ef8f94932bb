<ng-container *ngIf="compareWith">
  <mat-form-field class="w-100" [appearance]="appearance" *ngIf="control">
    <mat-label class="matLabel">{{placeholder}}</mat-label>
    <app-fiscomm-tooltip [text]="tooltip"></app-fiscomm-tooltip>
    <mat-select [formControl]="control" [disabled]="disabled"
        [compareWith]="compareWith"
    >
      <mat-option *ngIf="enableNone">{{placeholder}}</mat-option>
      <mat-option [disabled]="option.disabled || false" [matTooltip]="option.tooltip || ''"
      *ngFor="let option of options" [value]="option.value">
        {{option.label}}
      </mat-option>
    </mat-select>
  </mat-form-field>
</ng-container>

<ng-container *ngIf="!compareWith">
  <mat-form-field class="w-100" [appearance]="appearance" *ngIf="control">
    <mat-label class="matLabel">{{placeholder}}</mat-label>
    <app-fiscomm-tooltip [text]="tooltip"></app-fiscomm-tooltip>
    <mat-select [formControl]="control" [disabled]="disabled">
      <mat-option *ngIf="enableNone">{{placeholder}}</mat-option>
      <mat-option [disabled]="option.disabled || false" [matTooltip]="option.tooltip || ''" *ngFor="let option of options"
        [value]="option.value">
        {{option.label}}
      </mat-option>
    </mat-select>
  </mat-form-field>
</ng-container>