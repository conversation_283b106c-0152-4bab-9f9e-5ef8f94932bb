export const environment = {
  firebase: {
    projectId: 'fiscal-38558',
    appId: '1:837025331153:web:71b02a1f8f3858410ad2f0',
    storageBucket: 'fiscal-38558.appspot.com',
    locationId: 'europe-central2',
    apiKey: 'AIzaSyAnuQTQWRgxeXxiewjG3CUI6j_BjXNX3UM',
    authDomain: 'fiscal-38558.firebaseapp.com',
    messagingSenderId: '837025331153',
  },

  eInvoices: {
    baseUrl: 'https://efakture.fiscomm.rs',
  },

  main: {
    // baseUrl: 'https://europe-central2-fiscomm-travelagency.cloudfunctions.net/api-1',
    baseUrl: 'https://sandbox.api.fiscomm.rs/api',
    // baseUrl:'http:///localhost:3000/api'
    // baseUrl: 'http://localhost:5001/fiscomm-travelagency/us-central1/api',
  },

  mainMerchantPro: {
    baseUrl: 'https://api.merchant-pro.fiscomm.rs/api',

    //baseUrl:'https://us-central1-fiscal-38558.cloudfunctions.net'
    // baseUrl: 'http://localhost:3100/api'
  },

  mainSelfcare: {
    baseUrl: 'https://api.user-selfcare.fiscomm.rs/api',
    // baseUrl: 'https://staging.api.user-selfcare.fiscomm.rs/api'
    // baseUrl: 'http://localhost:3010/api'
  },

  mainFiscomm: {
    // baseUrl: 'https://us-central1-fiscomm-travelagency.cloudfunctions.net/api',
    baseUrlMain: 'https://us-central1-fiscal-38558.cloudfunctions.net/api',
    baseUrl: 'https://sandbox.api.fiscomm.rs/api',
  },
  mainFiscommTravelagency: {
    // baseUrl: 'https://us-central1-fiscomm-travelagency.cloudfunctions.net/api',
    baseUrl: 'https://europe-west3-fiscal-38558.cloudfunctions.net/v2Premium',
    // baseUrl: 'https://us-central1-fiscal-38558.cloudfunctions.net/api',
    // baseUrl:'https://sandbox.api.fiscomm.rs/api'
  },

  admin: {
    baseUrl: 'https://api.admin.fiscomm.rs',
  },

  authToken:
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhcGlLZXlOYW1lIjoiS29yaXN0aSBvdmFqIEFwaSBLbGp1YyIsInVpZCI6InJ3aWhPVnoyeWFTTHNzamRTSHkxRThDcVdKSDIiLCJ0aW1lc3RhbXBDcmVhdGVkIjoxNjQ2NjY3NTcwMTkzLCJ0aW1lc3RhbXBFeHBpcmVzIjoxNjQ2NjY3NTcwMTkzLCJpYXQiOjE2NDY2Njc1NzB9.gaovV9IjQ8OHbMFgJk9GlnGtrfT0mUov9fA552tN5s4',
  production: true,
};
