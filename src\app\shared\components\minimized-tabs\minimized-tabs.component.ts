import { Component, OnInit } from '@angular/core';
import { MinimizeService } from '../../services/minimize.service';
import {
  trigger,
  state,
  style,
  transition,
  animate,
} from '@angular/animations';

@Component({
  selector: 'app-minimized-tabs',
  templateUrl: './minimized-tabs.component.html',
  styleUrls: ['./minimized-tabs.component.scss'],
  animations: [
    trigger('slideInOut', [
      state(
        'void',
        style({
          transform: 'translateY(100%)',
          opacity: 0,
        })
      ),
      transition(':enter, :leave', [
        animate('0.3s ease-in-out'), // Adjust the duration and easing as needed
      ]),
    ]),
  ],
})
export class MinimizedTabsComponent implements OnInit {
  minimizedModals: any[] = [];
  modalNameCountArr: number[] = [];

  constructor(private minimizeService: MinimizeService) {}

  ngOnInit(): void {
    this.minimizeService.getMinimizedObservable().subscribe((response) => {
      this.minimizedModals = response;
      this.modalNameCountArr = this.countOccurrences(this.minimizedModals);
    });
  }

  openModal(id: string) {
    this.minimizeService.openMinimized(id);
  }

  deleteModal(id: string) {
    this.minimizeService.deleteMinimized(id);
  }

  emptyTabs() {
    this.minimizeService.emptyMinimized();
  }

  countOccurrences(arr: any[]): number[] {
    const countMap: Record<string, number> = {};
    const result: number[] = [];

    for (let i = 0; i < arr.length; i++) {
      const tab = arr[i];
      countMap[tab.name] = (countMap[tab.name] || 0) + 1;
      result.push(countMap[tab.name] - 1);
    }

    return result;
  }
}
