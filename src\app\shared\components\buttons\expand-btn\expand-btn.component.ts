import { Component, Input, OnInit } from '@angular/core';

@Component({
  selector: 'app-expand-btn',
  templateUrl: './expand-btn.component.html',
  styleUrls: ['./expand-btn.component.scss']
})
export class ExpandBtnComponent implements OnInit {

  @Input() menuItems: FunctionItem[] = [];
  @Input() disabled: boolean = false;
  @Input() text: string = 'Akcije';
  constructor() { }

  ngOnInit(): void {
  }

}

interface FunctionItem{
  label: string;
  enabled: boolean;
  action: () => any;
}