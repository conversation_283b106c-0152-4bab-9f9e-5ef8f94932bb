.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  background-color: #fff;
  padding: 1rem;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.title {
  h2 {
    font-size: 1.5rem;
    margin: 0;
    font-weight: 500;
  }

  p {
    color: #666;
    margin: 0.5rem 0 0;
  }
}

.actions {
  button {
    mat-icon {
      margin-right: 0.5rem;
    }
  }
}

@media (max-width: 768px) {
  .header-container {
    flex-direction: column;
    align-items: flex-start;
  }

  .title {
    margin-bottom: 1rem;
  }
}

.container-fluid {
  padding-bottom: 20px;

  button {
    margin-right: 10px;
  }
}
