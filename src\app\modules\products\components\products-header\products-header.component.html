<div class="container-fluid" *transloco="let t">
    <div class="d-flex gap-3">
        <div><app-success-btn text="{{t('items.add')}}" (click)="actionEmitter.emit({action:'add'})"></app-success-btn></div>
        <div>
            <input type="file" #fileInput (change)="onFileSelected($event)" accept=".xlsx,.xls,.tsv,.csv" style="display: none">
            <app-success-btn text="{{t('items.import')}}" (click)="fileInput.click()"></app-success-btn>
        </div>
        <div>
            <button mat-raised-button color="primary" (click)="showImportTemplate()">
              <mat-icon>help_outline</mat-icon>
              {{ t('items.import_template_title') }}
            </button>
        </div>
    </div>
</div>
