import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Routes } from '@angular/router';
import { SharedModule } from 'src/app/shared/shared.module';
import { LoginPageComponent } from './containers/login-page/login-page.component';
import { LoginPageGuard } from 'src/app/core/guards/login-page.guard';
import { LoginFormComponent } from './components/login-form/login-form.component';
import { ResetFormComponent } from './components/reset-form/reset-form.component';

const routes: Routes = [
  { path: '', redirectTo: 'login', pathMatch: 'full' },
  {
    path: '',
    component: LoginPageComponent,
    children: [
      {
        path: 'login',
        canActivate:[LoginPageGuard],
        component: LoginFormComponent
      },
      {
        path: 'resetovanje-lozinke',
        canActivate:[LoginPageGuard],
        component: ResetFormComponent
      },
    ]
  },

];

@NgModule({
  declarations: [
    LoginPageComponent,
    LoginFormComponent,
    ResetFormComponent
  ],
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    SharedModule
  ]
})
export class LoginModule { }
