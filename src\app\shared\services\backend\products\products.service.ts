import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { AngularFireAuth } from '@angular/fire/compat/auth';
import { environment } from 'src/environments/environment';
import { lastValueFrom, Observable, shareReplay } from 'rxjs';
import { CommonFirestoreService } from '../../firestore/common-firestore.service';

@Injectable({
  providedIn: 'root',
})
export class ProductsService {
  baseUrl = environment.mainSelfcare.baseUrl;
  headers: any;
  private cachedProducts$?: Observable<any[]>;

  constructor(private auth: AngularFireAuth, private http: HttpClient,

  ) {
    auth.onAuthStateChanged(async (user) => {
      this.headers = {
        Authorization: `Bearer ${user?.getIdToken()}`,
      };
      if (user) {
        this.cachedProducts$ = undefined;
        this.cachedProducts$ = this.getAllProducts();
      }
    });
  }

  getAllProducts(cached: boolean = true): Observable<any> {
    if (!this.cachedProducts$ || cached == false)
      this.cachedProducts$ = this.http
        .get<any[]>(`${this.baseUrl}/products`, { headers: this.headers })
        .pipe(shareReplay({ bufferSize: 1, refCount: true }));
    return this.cachedProducts$;
  }

  addNewProduct(product: any): Observable<any> {
    const productData = {
      ...product,
      createdAt: new Date()
    };
    return this.http.post(`${this.baseUrl}/products`, productData, {
      headers: {
        ...this.headers,
        'intercept-msg': encodeURIComponent('Proizvod uspešno dodat.'),
      },
    });
  }
  deleteProduct(product: any): Observable<any> {
    return this.http.delete(`${this.baseUrl}/products/${product._id}`, {
      headers: {
        ...this.headers,
        'intercept-msg': encodeURIComponent('Proizvod uspešno obrisan.'),
      },
    });
  }
  updateProduct(product: any): Observable<any> {
    return this.http.post(`${this.baseUrl}/products/${product._id}/update`, product, {
      headers: {
        ...this.headers,
        'intercept-msg': encodeURIComponent('Proizvod uspešno ažuriran.'),
      },
    });
  }


}
