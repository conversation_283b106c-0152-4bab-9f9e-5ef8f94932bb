
<div class="invoices-bulk" *transloco="let t">
    <div class="title">
      <h1 mat-dialog-title>{{t('orders')}}</h1>
    </div>
    <button class="x-button" mat-dialog-close>
      <mat-icon>close</mat-icon>
    </button>
    <div mat-dialog-content>
      <div class="filter-group">

      </div>
        <table class="bulk-table" id="rowInfo">
        <thead>
            <th>{{t('receipts.invoice_number_pos')}}</th>
            <th>{{t('customer')}}</th>
            <th>{{t('amount')}}</th>
            <th>  <div [class]="
              'status ' +
              (allDone=== 'waiting'
              ? 'red'
              : allDone === 'done'
              ? 'green'
              : '')">
              {{ t(allDone) }}
          </div></th>
        </thead>
        <tbody>
            <tr *ngFor="let order of orders;let i=index;">
                <td>{{order.id}}</td>
                <td>{{order.shipping_name}}</td>
                <td>{{order.total_amount | rsdCurrency:'RSD ':2:'.':',':3 }}</td>
                <td>
                    <div [class]="
                        'status ' +
                        (orderStatus[i] === 'waiting'
                        ? 'red'
                        : orderStatus[i] === 'done'
                        ? 'green'
                        : '')">
                        {{ t(orderStatus[i]) }}
                    </div>
                </td>
            </tr>
        </table>
    </div>
    <div class="space"></div>
    <div class="modal-buttons border-top" mat-dialog-actions>
      <app-danger-btn text="{{t('cancel')}}" mat-dialog-close=""></app-danger-btn>
      <app-success-btn text="{{t('start')}}" (click)="startFiscalization()"></app-success-btn>
    </div>
  </div>
  