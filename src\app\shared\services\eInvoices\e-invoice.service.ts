import { HttpHeaders, HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from 'src/environments/environment';
import { InvoiceRequestDto } from './types/einvoice.dto';

@Injectable({
  providedIn: 'root',
})
export class EInvoiceService {
  baseURL: string = environment.eInvoices.baseUrl;

  constructor(private http: HttpClient) {}

  createInvoice(invoice: InvoiceRequestDto) {
    return this.http.post(`${this.baseURL}/invoices`, invoice);
  }

  getInvoice(invoiceId: string) {
    return this.http.get(`${this.baseURL}/invoices/${invoiceId}`);
  }

  getInvoices(params?: { invoiceNumber?: string }) {
    let queryString = '';
    if (params) queryString = '?' + new URLSearchParams(params).toString();

    return this.http.get<any>(`${this.baseURL}/invoices/my${queryString}`);
  }

  getSalesInvoices(filters?: {
    dateFrom?: string;
    dateTo?: string;
    status?: string;
  }) {
    if (filters) {
      return this.http.get<any>(
        `${this.baseURL}/invoices/sales?dateFrom=${filters.dateFrom}&dateTo=${filters.dateTo}&status=Sent`
      );
    }

    return this.http.get<any>(`${this.baseURL}/invoices/sales?status=Sent`);
  }

  getSaleById(id: any) {
    return this.http.get(`${this.baseURL}/invoices/sales/${id}`);
  }

  getTaxCategories() {
    return this.http.get(`${this.baseURL}/helpers/tax-categories`);
  }

  getUnitCodes() {
    return this.http.get(`${this.baseURL}/helpers/unit-codes`);
  }

  getInvoicePeriods() {
    return this.http.get(`${this.baseURL}/helpers/invoice-periods`);
  }

  getTaxExemptionReasons() {
    return this.http.get<any[]>(
      `${this.baseURL}/helpers/tax-exemption-reasons`
    );
  }

  companies() {
    return this.http.get(`${this.baseURL}/helpers/companies`);
  }

  getCompanyData(vatRegNum: string) {
    return this.http.get(`${this.baseURL}/helpers/companies/${vatRegNum}`);
  }

  getCustomerByVatRegNum(vatRegNum: string) {
    return this.http.get(`${this.baseURL}/helpers/customers/${vatRegNum}`);
  }

  getInvoiceTypes() {
    return this.http.get(`${this.baseURL}/helpers/invoice-types`);
  }
}

export const INVOICE_STATUSES = [
  'New',
  'Draft',
  'Sent',
  'Mistake',
  'Sending',
  'Approved',
  'Rejected',
  'Cancelled',
  'Storno',
];
