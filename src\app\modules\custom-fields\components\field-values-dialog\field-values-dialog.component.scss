.field-values-dialog {
  min-width: 400px;
  max-width: 100%;
  height: 100%;
  position: relative;
}

.search-field {
  width: 100%;
  margin-bottom: 16px;
}

.close-button {
  top: 0;
  right: 0;
  position: absolute;
}

.values-container {
  max-height: 500px;
  overflow-y: auto;
  position: relative;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px;

  p {
    margin-top: 16px;
    color: rgba(0, 0, 0, 0.6);
  }
}

.no-values {
  padding: 16px;
  text-align: center;
  color: rgba(0, 0, 0, 0.6);
}

.created-at {
  color: rgba(0, 0, 0, 0.54);
  font-size: 12px;
}

.value-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;

  .value-text {
    font-weight: 500;
  }

  .toggle-button {
    transition: transform 0.3s ease;

    &:hover {
      background-color: rgba(0, 0, 0, 0.04);
    }
  }

}



.receipts-container {
  margin-left: 28px;
  margin-bottom: 16px;
  margin-right: 8px;
  transform-origin: top;
  overflow: hidden;
  position: relative;
  will-change: height, opacity;
}

.receipts-list {
  background-color: #f5f5f5;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  overflow: hidden;
  margin: 0;
}

.receipts-title {
  font-size: 14px;
  padding: 8px;
  background-color: #eee;
}

.receipt-links {
  margin-top: 4px;

  a {
    margin-right: 8px;
    font-size: 12px;
    line-height: 24px;
    height: 24px;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: rgba(0, 0, 0, 0.04);
    }

    .link-icon {
      font-size: 16px;
      height: 16px;
      width: 16px;
      margin-right: 4px;
    }
  }
}

::ng-deep .mat-list, ::ng-deep .mat-nav-list {
  padding-top: 0 !important;
}

.load-more-container {
  display: flex;
  justify-content: center;
  padding: 16px 0;
  border-top: 1px solid #eee;
  margin-top: 8px;

  button {
    min-width: 150px;
  }
}
