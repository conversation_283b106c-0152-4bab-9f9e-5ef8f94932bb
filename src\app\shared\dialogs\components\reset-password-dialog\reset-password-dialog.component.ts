import { Component, OnInit } from '@angular/core';
import { FormControl } from '@angular/forms';
import { translate } from '@ngneat/transloco';
import { AuthService } from 'src/app/core/services/auth.service';

@Component({
  selector: 'app-reset-password-dialog',
  templateUrl: './reset-password-dialog.component.html',
  styleUrls: ['./reset-password-dialog.component.scss']
})
export class ResetPasswordDialogComponent implements OnInit {

  constructor(private authService: AuthService) { }
  
  emailControl: FormControl = new FormControl('');
  error: boolean = false;
  success: boolean = false;
  buttonText: string = 'POSALJI';

  disabled: boolean = false;
  count: number = 10;

  ngOnInit(): void {
  }

  resetPassword() {
    this.authService.resetPassword(this.emailControl.value).subscribe({
      next: () => {
        this.disabled = true;
        this.success = true;
        this.error = false;
        this.disabledCountdown(10);
      },
      error: (err) => {
        this.error = true;
        this.success = false;
        this.buttonText = translate('login_page.send_again');
      },
    })
  }

  disabledCountdown(seconds: number) {
    this.count = seconds;
    const timer = setInterval(() => {
      this.buttonText = translate('login_page.send_again') + this.count;
      if (this.count > 0) {
        this.count--;
      }
      else if (this.count == 0) {
        this.buttonText = translate('login_page.send_again');
        this.disabled = false;
        clearInterval(timer);
      }
    }, 1000);
  }



}
