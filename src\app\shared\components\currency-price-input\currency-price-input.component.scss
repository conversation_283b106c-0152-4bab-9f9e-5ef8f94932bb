.currency-prices-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  margin-bottom: 1.5rem;
}

.currency-prices-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
}

.currency-price-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 14px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  background-color: #f9f9f9;
  min-height: 48px;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #f0f0f0;
  }
}

.currency-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.currency-symbol {
  font-weight: bold;
  color: #333;
  min-width: 20px;
}

.currency-code {
  color: #666;
  font-weight: 500;
  width: 60px;
}

.currency-price {
  font-weight: 500;
  font-size: 1rem;
  color: #212121;
}

.remove-button {
  margin: -8px;
  opacity: 0.7;
  transition: opacity 0.2s ease;

  &:hover {
    opacity: 1;
  }
}

.add-currency-form {
  width: 100%;
}

.form-row {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.currency-select, .price-input {
  flex: 1;
}

.add-button {
  margin-top: 4px;
}

.no-currencies-message {
  color: #757575;
  font-style: italic;
  text-align: center;
  padding: 16px 0;
  border-radius: 4px;
  background-color: #f5f5f5;
  margin-top: 8px;
}

::ng-deep {
  .currency-select, .price-input {
    .mat-form-field-wrapper {
      padding-bottom: 1.25em;
    }

    .mat-form-field-infix {
      padding: 0.75em 0;
      width: 100%;
    }

    .mat-form-field-label-wrapper {
      top: -1.05em;
    }

    &.mat-form-field-appearance-outline .mat-form-field-outline {
      color: #e0e0e0;
    }

    &.mat-form-field-appearance-outline.mat-focused .mat-form-field-outline-thick {
      color: var(--primary-color, #3f51b5);
    }

    &.mat-form-field-invalid .mat-form-field-outline {
      color: var(--warn-color, #f44336);
    }
  }
}
