import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { TranslocoService } from '@ngneat/transloco';
import { CURRENCY_ORDER } from 'src/app/shared/constants/available_currencies.const';

interface DuplicateProduct {
  name: string;
  price: number;
  gtin?: string;
  productCode?: string;
  prices?: {[key: string]: number};
  createdAt?: Date;
  existingProduct: {
    name: string;
    price: number;
    gtin?: string;
    productCode?: string;
    prices?: {[key: string]: number};
    createdAt?: Date;
  };
  conflictType: 'gtin' | 'productCode' | 'name';
}

@Component({
  selector: 'app-duplicate-products-dialog',
  templateUrl: './duplicate-products-dialog.component.html',
  styleUrls: ['./duplicate-products-dialog.component.scss']
})
export class DuplicateProductsDialogComponent implements OnInit {
  duplicateProducts: DuplicateProduct[] = [];
  selectedProducts: Set<number> = new Set();
  selectAll = false;
  supportedCurrencies = CURRENCY_ORDER.filter(currency => currency !== 'RSD');

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: {
      duplicates: DuplicateProduct[],
      totalProducts: number
    },
    private dialogRef: MatDialogRef<DuplicateProductsDialogComponent>,
    private translocoService: TranslocoService
  ) {
    this.duplicateProducts = data.duplicates;
  }

  ngOnInit(): void {
  }

  toggleSelectAll(): void {
    this.selectAll = !this.selectAll;
    if (this.selectAll) {
      this.duplicateProducts.forEach((_, index) => this.selectedProducts.add(index));
    } else {
      this.selectedProducts.clear();
    }
  }

  toggleProduct(index: number): void {
    if (this.selectedProducts.has(index)) {
      this.selectedProducts.delete(index);
    } else {
      this.selectedProducts.add(index);
    }
    this.selectAll = this.selectedProducts.size === this.duplicateProducts.length;
  }

  confirm(): void {
    const selectedProducts = this.duplicateProducts.filter((_, index) => this.selectedProducts.has(index));
    this.dialogRef.close(selectedProducts);
  }

  cancel(): void {
    this.dialogRef.close(undefined);
  }

  getConflictTypeLabel(type: 'gtin' | 'productCode' | 'name'): string {
    if (type === 'gtin') return 'GTIN';
    if (type === 'productCode') return this.translocoService.translate('items.product_code');
    return this.translocoService.translate('items.name');
  }

  getCurrencyPrice(product: any, currency: string): string {
    if (!product.prices || !product.prices[currency]) {
      return '-';
    }

    // Format the number with 2 decimal places
    const price = parseFloat(product.prices[currency]);
    return price.toFixed(2);
  }

  hasCurrencyPrices(product: any): boolean {
    if (!product || !product.prices) return false;

    // Check if there's at least one currency with a value
    return Object.keys(product.prices).length > 0 &&
           Object.values(product.prices).some(value => value !== null && value !== undefined);
  }
}
