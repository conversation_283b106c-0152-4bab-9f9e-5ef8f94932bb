.tab-container {
  background-color: #e8edff;
  border-radius: 4px;
  padding: 10px;
  position: fixed;

  display: flex;
  bottom: 0;
  left: 20%;
  width: 60%;
}

.tab {
  padding: 8px;
  border-radius: 4px;
  background-color: white;
  margin-right: 10px;
  p {
    padding: 0;
    margin: 0;
  }
  cursor: pointer;
  transition: all 0.6s linear;
}

.tab:hover {
  background-color: #f1f2f5;
}

.icon {
  text-align: center;
  width: 20px;
  height: 20px;
  font-size: 20px;
  padding-top: 5px;
}

.end {
  position: absolute;
  right: 12px;
}
