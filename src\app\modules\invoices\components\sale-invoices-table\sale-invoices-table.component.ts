import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { translate } from '@ngneat/transloco';
import { DialogService } from 'src/app/shared/dialogs/dialog.service';
import { BulkActionsService } from 'src/app/shared/services/bulk-actions.service';

@Component({
  selector: 'app-sale-invoices-table',
  templateUrl: './sale-invoices-table.component.html',
  styleUrls: ['./sale-invoices-table.component.scss'],
})
export class SaleInvoicesTableComponent implements OnInit {
  @Input() data: any[] = [];
  @Input() systemColumns: string[] = [];

  @Output() actionEmitter: EventEmitter<any> = new EventEmitter();

  checkboxStates: Map<string, boolean> = new Map();
  isMasterSelected: boolean = true;

  constructor(
    private dialogService: DialogService,
    private bulkActionService: BulkActionsService
  ) {}

  ngOnInit(): void {
    this.initalizeCheckboxes();
  }

  openDetailsDialog(element: any) {
    this.dialogService.openBillingDetailsDialog(
      { width: '90%', height: '85%' },
      element
    );
  }

  getStatus(status: string) {
    switch (status.toLowerCase()) {
      case 'sent':
        return { text: translate('billing.sent'), color: 'success' };
        break;
      case 'unsent':
        return { text: translate('billing.un_sent'), color: 'warning' };
        break;
      case 'not_created':
        return { text: translate('billing.not_created'), color: 'danger' };
        break;
      case 'paid':
        return { text: translate('billing.paid'), color: 'success' };
        break;
      default:
        return { text: translate('billing.not_created'), color: 'danger' };
        break;
    }
  }

  //checkboxes for bulk actions
  private initalizeCheckboxes() {
    this.data.forEach((receipt) => {
      this.checkboxStates.set(receipt.invoice_number, false);
    });
  }

  getCheckbox(invoice_number: any) {
    return this.checkboxStates.get(invoice_number);
  }

  checkUncheckAll() {
    this.bulkActionService.emptyAllElements();
    this.isMasterSelected = !this.isMasterSelected;
    this.checkboxStates.forEach((value, key) =>
      this.checkboxStates.set(key, this.isMasterSelected)
    );
    const checkboxes = document.querySelectorAll('.checkbox');
    checkboxes.forEach((checkbox) => {
      checkbox.dispatchEvent(new Event('change'));
    });
  }

  toggleElement(element: any) {
    let currVal = this.getCheckbox(element.invoice_number);
    this.checkboxStates.set(element.invoice_number, !currVal);
    if (this.getCheckbox(element.invoice_number))
      this.bulkActionService.addSelectedElement(element);
    else this.bulkActionService.removeSelectedElement(element);
  }
}
