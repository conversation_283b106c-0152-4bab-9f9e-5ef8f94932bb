import { Component, OnInit,Input } from '@angular/core';

@Component({
  selector: 'app-payment-slip',
  templateUrl: './payment-slip.component.html',
  styleUrls: ['./payment-slip.component.scss']
})
export class PaymentSlipComponent implements OnInit {

  @Input() data?:PaymentSlip;

  constructor() { }

  ngOnInit(): void {
  }


}

export interface PaymentSlip{
  payer: string;
  paymentPurpouse: string;
  recipient: string;
  payerSignature: string;
  paymentPass: string;
  currency: string;
  amount: string;
  recipientAcc: string;
  model: string;
  referanceNumber: string;
  receptionDate: string;
  currencyDate: string;
}