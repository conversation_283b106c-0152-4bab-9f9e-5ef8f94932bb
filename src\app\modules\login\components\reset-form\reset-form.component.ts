import { Component, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { AuthService } from 'src/app/core/services/auth.service';

@Component({
  selector: 'app-reset-form',
  templateUrl: './reset-form.component.html',
  styleUrls: ['./reset-form.component.scss']
})
export class ResetFormComponent implements OnInit {

  resetForm: FormGroup;
  constructor(private authService:AuthService,private router:Router) {
    this.resetForm = new FormGroup({
      password: new FormControl('', [Validators.required,Validators.minLength(6)]),
      confirm_password: new FormControl('', [Validators.required,Validators.minLength(6)]),
    });
  }

  ngOnInit(): void {
  }

  getPasswordControl() : FormControl {
    return this.resetForm.get('password') as FormControl;
  }

  getConfirmPasswordControl() : FormControl {
    return this.resetForm.get('confirm_password') as FormControl;
  }


  isDisabled(): boolean {
    return this.resetForm.valid && this.doPasswordsMatch() ? false : true;
  }
  
  doPasswordsMatch() : boolean {
    return this.getPasswordControl().value == this.getConfirmPasswordControl().value;
  }


  reset() {
    const oobCode = this.extractOobCodeFromUrl(window.location.href);
    const url = this.extractContinueUrl(window.location.href);
    this.authService.confirmResetPassword(oobCode, this.getConfirmPasswordControl().value)
      .then(res => window.location.href=url);
  }

  private extractOobCodeFromUrl(url:string):string {
    const urlObj = new URL(url);
    const searchParams = new URLSearchParams(urlObj.search);
    return searchParams.get('oobCode') as string; 
  }

  private extractContinueUrl(url:string):string {
    const urlObj = new URL(url);
    const searchParams = new URLSearchParams(urlObj.search);
    return searchParams.get('continueUrl') as string; 
  }

}
