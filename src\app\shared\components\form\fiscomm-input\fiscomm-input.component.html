<mat-form-field
  [appearance]="appearance"
  *ngIf="control"
  [ngClass]="readOnly ? 'readonly' : ''"
>
  <mat-label class="matLabel">{{ placeholder }}</mat-label>
  <app-fiscomm-tooltip [text]="tooltip"></app-fiscomm-tooltip>

  <div class="inputcontainer">
    <input
      *ngIf="autoComplete && control && type != 'image'"
      matInput
      [type]="type"
      [placeholder]="placeholder || ''"
      [formControl]="control"
      [appDecimalPlacesLimit]="decimals"
      [matAutocomplete]="autoComplete"
      [readonly]="readOnly"
      min="0"
      [max]="max"
    />

    <input
      *ngIf="!autoComplete && control && type != 'image'"
      matInput
      [type]="type"
      [appDecimalPlacesLimit]="decimals"
      [placeholder]="placeholder || ''"
      [formControl]="control"
      [readonly]="readOnly"
      min="0"
      [max]="max"
    />

    <input
      *ngIf="control && type == 'image'"
      (change)="imageChange.emit($event)"
      matInput
      type="file"
      [placeholder]="placeholder || ''"
      [formControl]="control"
      [readonly]="readOnly"
      accept="image/*"
    />
    <div class="icon-container" *ngIf="loading">
      <i class="loader"></i>
    </div>
  </div>

  <span matPrefix *ngIf="prefix">{{ prefix }}</span>
  <span matSuffix *ngIf="suffix">{{ suffix }}</span>
  <span matSuffix *ngIf="suffixIcon"
    ><mat-icon>{{ suffixIcon }}</mat-icon></span
  >
</mat-form-field>
