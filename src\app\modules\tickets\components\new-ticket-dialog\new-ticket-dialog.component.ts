import {
  Component,
  EventEmitter,
  Inject,
  OnInit,
  Output,
  inject,
} from '@angular/core';
import { FormArray, FormControl, FormGroup, Validators } from '@angular/forms';
import { TicketService } from 'src/app/shared/services/backend/tickets/ticket.service';
import { AngularFireAuth } from '@angular/fire/compat/auth';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
@Component({
  selector: 'app-new-ticket-dialog',
  templateUrl: './new-ticket-dialog.component.html',
  styleUrls: ['./new-ticket-dialog.component.scss'],
})
export class NewTicketDialogComponent implements OnInit {
  @Output() newTicketEmitter: EventEmitter<boolean> =
    new EventEmitter<boolean>();

  createTicketForm: FormGroup = new FormGroup({
    name: new FormControl(''),
    email: new FormControl(''),
    priority: new FormControl(1),
    category: new FormControl(''),
    title: new FormControl('', Validators.required),
    body: new FormControl('', Validators.required),
    files: new FormArray([]),
  });

  priorityOptions: any = [
    {
      label: 'LOW',
      value: 1,
    },
    {
      label: 'NORMAL',
      value: 2,
    },
    {
      label: 'HIGH',
      value: 3,
    },
    {
      label: 'BLOCKER',
      value: 4,
    },
  ];

  constructor(
    private ticketService: TicketService,
    private _auth: AngularFireAuth,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private dialogRef: MatDialogRef<NewTicketDialogComponent>
  ) {}

  ngOnInit(): void {
    this.addFile();
    this._auth.onAuthStateChanged((user) => {
      this.getControl('name').setValue(user?.displayName);
      this.getControl('email').setValue(user?.email);
    });
    if (this.data) {
      this.getControl('title').setValue(this.data.title);
      this.getControl('body').setValue(this.data.body);
    }
  }

  getControl(name: string) {
    return this.createTicketForm.get(name) as FormControl;
  }

  getFilesArray() {
    return this.createTicketForm.get('files') as FormArray;
  }

  addFile() {
    this.getFilesArray().push(new FormControl(''));
  }

  removeFile(index: number) {
    this.getFilesArray().controls.splice(index, 1);
  }

  getFileControl(i: number) {
    return this.getFilesArray().controls[i] as FormControl;
  }

  fileSelected($event: any, i: number) {
    const selectedFile = $event.target.files[0];
    if (selectedFile) {
      const maxFileSize = 24 * 1024 * 1024; // Set your maximum file size in bytes (e.g., 10MB)
      if (selectedFile.size > maxFileSize) {
        console.log('File size exceeds the maximum allowed size.');
        $event.target.value = null;
      } else {
        this.setFileName($event, i);
      }
    }
  }

  private setFileName(event: any, index: number) {
    this.getFilesArray().controls[index].setValue(
      event.target.value.split('\\').pop()
    );
  }

  createTicket() {
    let newTitle = this.getControl('title').value;
    if (this.getControl('category').value)
      newTitle = this.getControl('category').value + ' - ' + newTitle;
    let newTicket = {
      ...this.createTicketForm.value,
      title: newTitle,
    };

    this.ticketService.postTicket(newTicket).subscribe(() => {
      this.newTicketEmitter.emit(newTicket);
      this.dialogRef.close();
    });
  }
}
