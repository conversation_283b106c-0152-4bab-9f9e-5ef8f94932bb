import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-mp-navbar',
  templateUrl: './mp-navbar.component.html',
  styleUrls: ['./mp-navbar.component.scss']
})
export class MpNavbarComponent implements OnInit {

  activeLink: string = '';

  constructor(private activeRoute: ActivatedRoute) { }

  ngOnInit(): void {
    this.activeRoute.url.subscribe(url => {
      if(url[0])
        this.activeLink = url[0].path;
    });
  }

}
