import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'filter'
})
export class FilterPipe implements PipeTransform {

  transform(value: any[], filterArray: any[], fieldOne: string, fieldTwo:string): any[] {

    if (!value || !filterArray || !fieldOne || !fieldTwo) return value;

    return value.filter((item: any) => {
      return !filterArray.find((filterItem: any) => {
        return filterItem[fieldTwo] === item[fieldOne];
      });
    });

  }

}
