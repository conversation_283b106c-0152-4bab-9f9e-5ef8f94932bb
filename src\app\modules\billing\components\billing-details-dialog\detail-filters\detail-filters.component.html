<div class="row filtersDetails" *transloco="let t">
    <div class="col-md-3">
        <app-fiscomm-input 
        [control]="getControl('searchName')" 
        placeholder="{{t('billing.search.by_name')}}" suffixIcon="search"></app-fiscomm-input>
    </div>
    <div class="col-md-3">
        <app-fiscomm-input 
        [control]="getControl('searchQuantity')" 
        type="number"
        placeholder="{{t('billing.search.by_quantity')}}" suffixIcon="search"></app-fiscomm-input>
    </div>
    <div class="col-md-3">
        <app-fiscomm-input 
        [control]="getControl('searchPrice')" 
        type="number"
        placeholder="{{t('billing.search.by_price')}}" suffixIcon="search"></app-fiscomm-input>
    </div>
</div>