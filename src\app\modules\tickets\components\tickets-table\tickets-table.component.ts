import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';
import { MatTableDataSource } from '@angular/material/table';
import { DialogService } from 'src/app/shared/dialogs/dialog.service';

@Component({
  selector: 'app-tickets-table',
  templateUrl: './tickets-table.component.html',
  styleUrls: ['./tickets-table.component.scss'],
})
export class TicketsTableComponent implements OnInit {
  @Input() data: any[] = [];
  @Output() actionEmitter: EventEmitter<any> = new EventEmitter<any>();

  @Input() displayColumns: string[] = [];

  constructor(private dialogService: DialogService) {}

  ngOnInit(): void {}

  emitAction(value: any) {
    this.actionEmitter.emit(value);
  }

  getPriorityColor(status: number) {
    switch (status) {
      case 1:
        return 'success';
        break;
      case 2:
        return 'yellow';
        break;
      case 3:
        return 'warning';
        break;
      case 4:
        return 'danger';
        break;
      default:
        return 'success';
        break;
    }
  }

  getStatusColor(status: number) {
    switch (status) {
      case 1:
        return 'primary';
        break;
      case 2:
        return 'danger';
        break;
      case 3:
        return 'success';
        break;
      default:
        return 'success';
        break;
    }
  }

  openDetailsDialog(element: any) {
    this.dialogService.openTicketDetailsDialog(
      { width: '650px', height: '90%' },
      element
    );
  }
}
