import { Injectable } from '@angular/core';
import { Receipts } from 'src/app/shared/services/backend/receipts/types/receipts-response.dto';
import { Observable, BehaviorSubject, map } from 'rxjs';
@Injectable({
  providedIn: 'root',
})
export class BulkActionsService {
  private selectedElements: any[] = [];
  private selectedElementsSubject = new BehaviorSubject<Receipts[]>([]);
  selectedElements$: Observable<Receipts[]> =
    this.selectedElementsSubject.asObservable();

  constructor() {}

  hasRefundElementSelected$(): Observable<boolean> {
    return this.selectedElements$.pipe(
      map((receipts) =>
        receipts.some(
          (el: any) => el.transaction_type.transaction_type == 'Refund'
        )
      )
    );
  }

  hasFiscalizedElement$(): Observable<boolean> {
    return this.selectedElements$.pipe(
      map((receipts) => {
        return receipts.some(
          (el: any) => el.payment_substatus_text === 'Fiskalizovan'
        );
      })
    );
  }

  hasNotFiscalizedElement$(): Observable<boolean> {
    return this.selectedElements$.pipe(
      map((receipts) => {
        return receipts.some(
          (el: any) => el.payment_substatus_text !== 'Fiskalizovan'
        );
      })
    );
  }

  addSelectedElement(element: any) {
    this.selectedElements.push(element);
    this.selectedElementsSubject.next(this.selectedElements);
  }

  removeSelectedElement(element: any) {
    const index = this.selectedElements.indexOf(element);
    if (index !== -1) {
      this.selectedElements.splice(index, 1);
    }
    this.selectedElementsSubject.next(this.selectedElements);
  }

  getSelectedElements() {
    return this.selectedElements;
  }

  getSelectetElementsObservable() {
    return this.selectedElements$;
  }

  getSelectedNotFiscalizedElements() {
    return this.selectedElements.filter(
      (el) => el.payment_substatus_text != 'Fiskalizovan'
    );
  }

  getIsEnoughNotFiscalizedElements() {
    return this.getSelectedNotFiscalizedElements().length >= 2;
  }

  getIsEnoughSelected() {
    return this.selectedElements.length >= 2;
  }

  emptyAllElements() {
    this.selectedElements = [];
    this.selectedElementsSubject = new BehaviorSubject<Receipts[]>([]);
  }
}
