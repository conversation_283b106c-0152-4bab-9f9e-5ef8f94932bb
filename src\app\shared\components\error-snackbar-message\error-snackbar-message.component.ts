import { Clipboard } from '@angular/cdk/clipboard';
import { Component, Inject, OnInit } from '@angular/core';
import { MAT_SNACK_BAR_DATA } from '@angular/material/snack-bar';
import { Router } from '@angular/router';
import { DialogService } from '../../dialogs/dialog.service';

@Component({
  selector: 'app-error-snackbar-message',
  templateUrl: './error-snackbar-message.component.html',
  styleUrls: ['./error-snackbar-message.component.scss'],
})
export class ErrorSnackbarMessageComponent implements OnInit {
  constructor(
    @Inject(MAT_SNACK_BAR_DATA) public data: any,
    private clipboard: Clipboard,
    private router: Router,
    private dialogService: DialogService
  ) {}

  ngOnInit(): void {}

  copy() {
    this.clipboard.copy(this.data.requestId);
  }

  makeTicket() {
    this.router.navigate(['/tiketi']).then(() => {
      this.dialogService.openNewTicketDialog(
        {
          width: '480px',
          height: '90%',
        },
        {
          title: 'Greška prilikom korišćenja portala',
          body:
            '<PERSON><PERSON><PERSON>, Prilikom korišćenja portala, desila se greška sa kodom ' +
            this.data.requestId,
        }
      );
    });
  }
}
