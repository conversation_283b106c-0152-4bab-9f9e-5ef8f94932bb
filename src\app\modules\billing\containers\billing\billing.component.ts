import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { AuthService } from 'src/app/core/services/auth.service';
import { PaginatorEvent } from 'src/app/shared/components/fiscomm-paginator/fiscomm-paginator.component';
import { InvoiceService } from 'src/app/shared/services/backend/invoices/invoice-service.service';
import { BulkActionsService } from 'src/app/shared/services/bulk-actions.service';

@Component({
  selector: 'app-billing',
  templateUrl: './billing.component.html',
  styleUrls: ['./billing.component.scss'],
})
export class BillingComponent implements OnInit {
  constructor(
    private http: HttpClient,
    private invoiceService: InvoiceService,
    private authService: AuthService,
    private bulkActionService: BulkActionsService
  ) {}

  columns = [
    'checkbox',
    'Broj fakture',
    'Mesec',
    '<PERSON><PERSON>',
    'Iznos',
    'Due date',
    'Pdf Link',
    'Status',
    'Akcija',
    'Details',
  ];

  data: any[] = [];

  //paging info
  perPage: number = 10;
  pageIndex: number = 0;
  pageSizeOptions = [10, 15, 20];
  to: number = 10;

  params: any = {
    page: this.pageIndex + 1,
    perPage: this.perPage,
  };

  uid: string = '';

  ngOnInit(): void {
    this.bulkActionService.emptyAllElements();
    this.authService.getUserId().subscribe((id: any) => {
      this.uid = id;
      this.getInvoices(id);
    });
  }

  getInvoices(uid: string) {
    this.invoiceService
      .getClientInvoices(uid, this.params)
      .subscribe((response: any) => {
        this.data = response.data;
      });
  }

  handlePageEvent(e: PaginatorEvent) {
    this.params.page = e.page;
    this.params.perPage = e.perPage;
    this.getInvoices(this.uid);
  }

  handleActions(event: any) {
    switch (event.action.toLowerCase()) {
      case 'download':
        this.downloadLink(event.data);
        break;
    }
  }

  handleBulkActions(event: any) {
    switch (event.action.toLowerCase()) {
      case 'bulk-download':
        this.bulkDownload();
        break;
    }
  }

  private bulkDownload() {
    let ids = this.bulkActionService
      .getSelectedElements()
      .map((element) => element.id);
    this.invoiceService.bulkDownloadInvoice(ids);
  }

  downloadLink(element: any) {
    const link = document.createElement('a');
    this.http
      .get(element.pdfUrl, {
        responseType: 'blob',
        headers: {
          'Content-Type': 'application/pdf',
          'Access-Control-Allow-Origin': '*',
        },
      })
      .subscribe({
        next: (response) => {
          const blob = new Blob([response], { type: 'application/pdf' });
          const blobUrl = window.URL.createObjectURL(blob);
          link.href = blobUrl;
          link.download = element.missing.invoiceNumber + '.pdf';
          link.click();
          window.URL.revokeObjectURL(blobUrl);
        },
        error: (response) => {
          link.target = '_blank';
          link.href = element.pdfUrl;
          link.click();
        },
      });
  }
}
