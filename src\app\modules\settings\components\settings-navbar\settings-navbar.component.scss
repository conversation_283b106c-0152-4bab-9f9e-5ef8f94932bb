.container-fluid {
  padding: 0;
}

.mat-tab-nav-bar {
  background-color: white;
  border-top-right-radius: 4px;
  border-top-left-radius: 4px;
  text-align: center;
}
.mat-tab-link {
  font-size: 19px;
  padding: 28px 24px;
  transition: ease-in 0.2s;
}

.centered-links {
  display: flex;
  justify-content: center;
}

.centered-links a {
  width: 50%;
  text-align: center;
}

.mat-tab-label-active {
  color: #0099b7;
  font-size: 21px;
  opacity: 1;
}

::ng-deep {
  .mat-tab-group.mat-primary .mat-ink-bar,
  .mat-tab-nav-bar.mat-primary .mat-ink-bar {
    background-color: #2cafc9 !important;
  }
  .mat-form-field {
    margin-bottom: 8px;
  }
}

@media only screen and (max-width: 768px) {
  .mat-tab-link {
    font-size: 17px;
    padding: 28px 18px;
  }
}

@media only screen and (max-width: 576px) {
  .mat-tab-link {
    font-size: 15px;
    padding: 20px 8px;
  }
}
