.container-fluid {
  padding: 0;
}

// Desktop Navigation Styles
.desktop-nav {
  .mat-tab-nav-bar {
    background-color: white;
    border-top-right-radius: 4px;
    border-top-left-radius: 4px;
    text-align: center;
  }

  .mat-tab-link {
    font-size: 19px;
    padding: 28px 24px;
    transition: ease-in 0.2s;
  }

  &.centered-links {
    display: flex;
    justify-content: center;
  }

  &.centered-links a {
    width: 50%;
    text-align: center;
  }
}

// Mobile Navigation Styles
.mobile-nav {
  background-color: white;
  border-top-right-radius: 4px;
  border-top-left-radius: 4px;
  position: relative;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .mobile-nav-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    cursor: pointer;
    background-color: white;
    border-top-right-radius: 4px;
    border-top-left-radius: 4px;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: #f5f5f5;
    }

    .active-item {
      display: flex;
      align-items: center;
      gap: 12px;
      flex: 1;

      .nav-icon {
        color: #2cafc9;
        font-size: 20px;
        width: 20px;
        height: 20px;
      }

      .nav-text {
        font-size: 16px;
        font-weight: 500;
        color: #333;
        font-family: "Inter", sans-serif;
      }
    }

    .dropdown-arrow {
      color: #666;
      transition: transform 0.3s ease;
      font-size: 24px;

      &.rotated {
        transform: rotate(180deg);
      }
    }
  }

  .mobile-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: white;
    border-radius: 0 0 4px 4px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease, opacity 0.3s ease;
    opacity: 0;

    &.open {
      max-height: 400px;
      opacity: 1;
    }

    .dropdown-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 16px 20px;
      text-decoration: none;
      color: #333;
      transition: background-color 0.2s ease;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
        border-radius: 0 0 4px 4px;
      }

      &:hover {
        background-color: #f8f9fa;
      }

      &.active {
        background-color: #e3f2fd;
        color: #2cafc9;

        .item-icon {
          color: #2cafc9;
        }
      }

      .item-icon {
        color: #666;
        font-size: 18px;
        width: 18px;
        height: 18px;
      }

      .item-text {
        font-size: 14px;
        font-weight: 400;
        font-family: "Inter", sans-serif;
      }
    }
  }
}

// Legacy styles for backward compatibility
.mat-tab-nav-bar {
  background-color: white;
  border-top-right-radius: 4px;
  border-top-left-radius: 4px;
  text-align: center;
}

.mat-tab-link {
  font-size: 19px;
  padding: 28px 24px;
  transition: ease-in 0.2s;
}

.centered-links {
  display: flex;
  justify-content: center;
}

.centered-links a {
  width: 50%;
  text-align: center;
}

.mat-tab-label-active {
  color: #0099b7;
  font-size: 21px;
  opacity: 1;
}

::ng-deep {
  .mat-tab-group.mat-primary .mat-ink-bar,
  .mat-tab-nav-bar.mat-primary .mat-ink-bar {
    background-color: #2cafc9 !important;
  }
  .mat-form-field {
    margin-bottom: 8px;
  }
}

// Enhanced responsive breakpoints
@media only screen and (max-width: 992px) {
  .desktop-nav .mat-tab-link {
    font-size: 16px;
    padding: 24px 16px;
  }
}

@media only screen and (max-width: 768px) {
  .desktop-nav .mat-tab-link {
    font-size: 14px;
    padding: 20px 12px;
  }

  // Legacy support
  .mat-tab-link {
    font-size: 17px;
    padding: 28px 18px;
  }
}

@media only screen and (max-width: 576px) {
  .desktop-nav .mat-tab-link {
    font-size: 12px;
    padding: 16px 8px;
  }

  // Legacy support
  .mat-tab-link {
    font-size: 15px;
    padding: 20px 8px;
  }
}

// Extra small screens
@media only screen and (max-width: 480px) {
  .mobile-nav {
    .mobile-nav-header {
      padding: 14px 16px;

      .active-item {
        gap: 10px;

        .nav-icon {
          font-size: 18px;
          width: 18px;
          height: 18px;
        }

        .nav-text {
          font-size: 14px;
        }
      }
    }

    .mobile-dropdown .dropdown-item {
      padding: 14px 16px;
      gap: 10px;

      .item-icon {
        font-size: 16px;
        width: 16px;
        height: 16px;
      }

      .item-text {
        font-size: 13px;
      }
    }
  }
}
