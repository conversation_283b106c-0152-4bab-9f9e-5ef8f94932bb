{"dashboard": {"home": "Home page", "base": "General", "receipts_page": "Receipt overview", "reports": "Reports", "payments": "Payment", "products": "Products", "settings": "Settings", "e_invoices": "E-invoices", "log_out": "Log out", "receipts": "Receipts", "create_receipt": "Create receipt", "cashiers": "Cashiers", "custom_fields": "Custom fields"}, "settings_page": {"key": "key", "receipt_customization": "Receipt Customization", "receipt_width": "Receipt width", "receipt_text": "Text below receipt", "default_payment_method": "Default payment method", "default_cashier": "De<PERSON>ult cashier", "password_settings": "Password Settings", "current_password": "Current Password", "new_password": "New Password", "confirm_password": "Confirm Password", "change_password": "CHANGE PASSWORD", "password_changed": "Password successfully changed!", "wrong_password": "Current password is incorrect", "password_error": "Error changing password. Please try again.", "ident_number": "Identity number", "company_name": "Company Name", "monthly_price": "Monthly price", "email_adress": "E-mail address", "auto_reports": "Auto reports", "base_settings": "General Settings", "activate_integration": "Activate integration", "activate_email": "Activate email", "store_name": "Enter store name", "credential_settings": "Credential Settings", "fiscalization_settings": "Fiscalization settings", "email_settings": "<PERSON><PERSON>s", "additional_email": "Enter an additional email address", "save_changes": "SAVE CHANGES", "input": {"base_link": "Enter base link", "api_key": "Enter your API key", "api_secret": "Enter your API secret", "fiscalisation_trigger": "Choose a fiscalisation trigger", "adress": "Address", "municipality": "Municipality", "postal_code": "Postal Code", "daily_reports": "I want to receive daily reports", "weekly_reports": "I want to receive weekly reports", "monthly_reports": "I want to receive monthly reports", "receipt_text": "Enter the text you want to appear below the receipt", "add_receipt_order": "Add order number below invoice"}}, "base": "General", "base_information": "General Information", "customer": "Customer", "change": "Change", "CHANGE": "CHANGE", "name": "Name", "account": "Account", "history": "History", "items_menu": "Items", "settings": "Settings", "orders": "Orders", "training_mode": "Training mode", "page_num": "Page number", "per_page": "Number per page", "search": "Search", "actions": "Actions", "group_actions": "Group Actions", "updated": "Updated", "required_info": "Fields marked with an asterisk ('*') are required", "amount": "Amount", "amounts": "Price", "tax": "Tax", "tax_type": "Tax type", "price": "Price", "price_discounted": "Price (after discount)", "total_price": "Total price", "total": "Total", "total_paid": "Total paid", "advance_amount": "Advance Amount", "total_sum": "Total amount", "paid": "Paid", "paid_advance": "Paid in advance", "left": "Remaining", "discount": "Discount", "from_wallet": "From wallet", "shipping": "Delivery", "create": "Create", "time_created": "Time of creation", "give_up": "Give up", "confirm": "Confirm", "save": "SAVE", "cancel": "CANCEL", "start": "START", "waiting": "Waiting", "progress": "In process", "done": "Done", "fiscalization": "Fiscalization", "fiscalized": "Fiscalized", "menu": {"advance": "Add new advance", "finalize": "Finalize", "refund": "Refund", "fiscalize": "Fiscalize", "copy": "Copy", "download": "Download", "pay": "Pay", "contact": "Contact Support", "send_to_email": "Send to email", "create_receipt": "Create receipt"}, "home": {"title": "Welcome to the Fiscomm platform!", "company_info": "Company Information", "company_name": "Company Name", "ident_number": "Identity number", "pib": "PIB", "address": "Address", "email_adress": "E-mail address"}, "input": {"tax_type": "Select a tax rate", "date_from_to": "Date from - to"}, "receipts": {"name": "Receipts", "items": "Receipt items", "currency_info": "Currency information", "currency_info_notice": "Currency is {{currency}} and date is {{currencyDate}}. Total amount is {{totalAmount}} RSD.", "currency_info_notice_title": "Currency Conversion Notice", "currency_info_notice_highlight": "All values will be automatically converted to RSD during submission.", "base_data": "Base data", "receipt_number": "Contract number", "receipt_type": "Choose a receipt type", "transaction_type": "Transaction type", "payment": "Payment", "customer_iden": "Customer identification", "customer_iden_required": "Customer identification is only required for receipts with an amount over 500,000 RSD", "optional_customer": "Optional customer fields", "travel_info": "Travel information", "custom_fields": "Custom Fields", "receipt_referent": "Enter reference receipt number", "receipt_refund": "Enter the refunded invoice number", "document_type": "Select a document type", "document_number": "Enter document number", "item_name": "Item name", "additonal_filters": "Additional filters", "new_receipt": "Add new receipt", "invoice_number": "Invoice number", "invoice_number_pos": "Order number", "invoice_type": "Invoice type", "invoice_pdf_url": "Journal", "sdc_date_time": "Fisk time.", "total_amount": "Amount", "finalized": "Finalized", "not_finalized": "Not finalized", "customer_name": "Customer name/surname", "add_payment_type": "Add payment method", "different_sum": "Enable an amount different from the total price", "cashier": "Cashier", "send_to_email": "Send to email", "input": {"transaction_type": "Select a transaction type", "payment_type": "Choose a payment type", "date_from_to": "Date from - to", "currency": "<PERSON><PERSON><PERSON><PERSON>", "currency_date": "Currency date", "exchange_rate": "Exchange rate"}, "referent_required": "*The reference receipt number must be the same as the receipt number on the previous advance receipt"}, "items": {"title": "Products", "name": "Product name", "add": "Add product", "new": "New product", "edit": "Edit product", "import": "Import Products", "import_error": "No valid products found in the file. Please check the file format.", "import_success": "Successfully imported {{success}} products. {{error}} products failed to import.", "import_cancelled": "Import cancelled", "import_template_title": "Product Import Format", "import_template_description": "To import products, prepare a TSV (Tab-Separated Values) file with the following columns:", "import_template_currency_title": "Pricing in Different Currencies", "import_template_currency_description": "You can define prices in different currencies by adding columns with the prefix 'price_' followed by the currency code:", "import_template_currency_note": "Note: For any missing currency price columns, the product will use 0 as the default price for that currency.", "import_template_columns": {"name": "Product name (required)", "price": "Product price in RSD (required)", "gtin": "Global Trade Item Number (optional)", "productCode": "Your internal product code (optional)", "price_currency": "Product price in {{currency}} (optional)"}, "import_template_note": "Note: Products with the same GTIN, product code, or product name will be considered duplicates. Only one product with the same identifier will be kept.", "input": {"name": "Enter the product name", "price": "Enter the price of the product"}, "additional_prices": "Additional Currency Prices", "no_additional_currencies": "No additional currencies added yet", "duplicate_products_title": "Duplicate Products Found", "duplicate_products_description": "Found {{total}} products to import. Some products have duplicate GTIN or product codes. Please select which products you want to import:", "new_product": "New Product", "existing_product": "Existing Product", "conflict_type": "Conflict Type", "product_code": "Product Code", "delete_confirm_multiple": "Are you sure you want to delete {{count}} products?", "delete_success_multiple": "Successfully deleted {{success}} products. {{error}} products failed to delete.", "example": "Example:", "close": "Close", "description": "Description"}, "reports": {"report_type": "Select a report type", "report_name": "Enter the report name", "download": "Download here", "add_new": "GENERATE NEW REPORT", "generated": "generated", "report": "Report", "report_orders": "Report on orders", "report_products": "Products Report", "report_payments": "Payment Report"}, "buyer_id_types": {"PIB_home": "PIB customer", "JMBG_home": "JMBG", "PIB_JBKJS": "PIB and JBKJS", "personal_ID": "ID number", "pensioner_card": "Pensioner card code", "PIB_company": "PIB - Legal entity, agricultural holding, identified by Tax Identification Number", "JMBG_company": "JMBG - Domestic natural person, agricultural holding, performing independent activity identified by JMBG", "BPG": "BPG", "refugee_ID": "Refugee ID number", "EBS": "EBS", "passport": "Passport number", "foreign_passport": "Passport number (Foreign natural person)", "LK": "Diplomatic identification number/LK", "personal_ID_MKD": "Personal ID number MKD", "personal_ID_MNE": "Personal ID number MNE", "personal_ID_ALB": "Personal ID number ALB", "personal_ID_BIH": "Personal ID number BIH", "personal_ID_Decision": "ID number by Decision", "TIN": "Foreign Tax ID (TIN)"}, "buyer_cost_id_types": {"SNPDV": "SNPDV number", "LNPDV": "LNPDV number", "PPO_PDV": "PPO-VAT number", "ZPPO_PDV": "ZPPO-PDV number", "MPPO_PDV": "MPPO-VAT number", "IPPO_VAT": "IPPO-VAT number", "corp_card_number": "Corporate card number", "time_period": "Time period"}, "invoice_types": {"normal": "Fiscal receipt", "proforma": "Estimated invoice", "copy": "Copy", "training": "Training", "advance": "Advance"}, "transaction_types": {"sale": "Sale", "refund": "Refund", "finalize": "Finalize"}, "payment_types": {"other": "Other non-cash payment", "cash": "Cash", "card": "Card", "check": "Check", "wireTransfer": "Transfer to account", "voucher": "Voucher", "mobileMoney": "Instant payment"}, "orders_page": {"search_by_order_number": "Search by order number", "order_number": "Order number"}, "billing": {"invoice_number": "Invoice number", "month": "Month", "year": "Year", "due_date": "Payment due date", "payment_date": "Payment Date", "not_created": "Not created", "paid": "Paid", "sent": "<PERSON><PERSON>", "un_sent": "Not Sent", "instructions": "Payment instructions", "no_items": "No items", "invoice_link": "Link to invoice", "information": "Basic information", "instruction_info": "Example of correctly completed receipt", "search": {"by_price": "Search by price", "by_quantity": "Search by quantity", "by_name": "Search by name"}}, "login_page": {"send": "SEND", "send_again": "SEND AGAIN", "pass_reset": "Reset password", "user_not_found": "User not found", "email_sent": "E-mail succesfuly sent", "input": {"email": "Enter e-mail"}}, "common": {"email": "Email", "email_required": "Email is required", "invalid_email": "Invalid email address", "send": "Send", "cancel": "Cancel"}, "validation": {"required": "This field is required", "password_length": "Password must be at least 6 characters long", "password_mismatch": "Passwords do not match"}, "cashiers": {"title": "Cashiers", "description": "Manage cashiers that can be selected when creating receipts", "add_cashier": "Add Cashier", "edit_cashier": "<PERSON> Cashier", "name": "Name", "name_required": "Name is required", "cashier_code": "Cashier Code", "notes": "Notes", "status": "Status", "active": "Active", "inactive": "Inactive", "created_at": "Created At", "no_cashiers": "No cashiers found. Add your first cashier."}, "demo_config_name": "Demo Version", "loading": "Loading...", "daily_report": "Daily report", "period_report": "Period report", "open_cash_drawer": "Open cash drawer", "printer_settings": "Printer settings", "terminal_settings": "Fiscal printer settings", "fiscal_printer": "Fiscal printer", "account_settings": "Account settings", "store_settings": "Store settings", "products_settings": "Product catalog", "tax_rate_settings": "Tax rates", "categories_settings": "Categories", "custom_fields": {"title": "Custom Fields", "description": "Manage custom fields that can be used in receipts", "add": "Add Custom Field", "edit": "Edit Custom Field", "delete": "Delete Custom Field", "delete_all": "Delete All Custom Fields", "delete_confirm": "Are you sure you want to delete this custom field?", "delete_all_confirm": "Are you sure you want to delete all custom fields?", "cannot_delete": "Cannot Delete Field", "cannot_delete_message": "This field has been used in receipts and cannot be deleted to maintain data integrity.", "delete_failed": "Delete Failed", "field_name": "Field Name", "field_description": "Description", "field_type": "Field Type", "possible_values": "Possible Values", "default_value": "Default Value", "required_field": "Required Field", "status": "Status", "active": "Active", "inactive": "Inactive", "required": "Required", "optional": "Optional", "add_option": "Add Option", "enter_field_name": "Enter field name", "enter_description": "Enter description", "enter_value": "Enter value", "value": "Value", "order": "Order", "name": "Name", "type": "Type", "values": "Values", "actions": "Actions", "search_values": "Search values", "type_to_search": "Type to search...", "loading_values": "Loading values...", "showing_results": "Showing {{count}} results", "no_values": "No custom fields found", "used_in_receipts": "Used in receipts", "view_receipts": "View receipts", "save": "Save", "update": "Update", "cancel": "Cancel", "field_type_text": "Text", "field_type_select": "Select", "field_type_number": "Number", "field_type_date": "Date", "field_type_boolean": "Yes/No", "yes": "Yes", "no": "No", "load_more": "Load More", "drag_to_reorder": "Drag to reorder"}, "advanced_search": "Advanced search", "add": "Add", "currency": "<PERSON><PERSON><PERSON><PERSON>", "currencies": {"eur": "Euro", "usd": "US Dollar", "chf": "Swiss Franc", "gbp": "British Pound", "aud": "Australian Dollar", "cad": "Canadian Dollar", "sek": "Swedish Krona", "dkk": "Danish Krone", "nok": "Norwegian Krone", "jpy": "Japanese Yen", "hrk": "Croatian Kuna", "kwd": "<PERSON><PERSON>", "pln": "Polish Zloty", "czk": "Czech Koruna", "skk": "Slovak Koruna"}}