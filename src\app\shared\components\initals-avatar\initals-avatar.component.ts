import { Component, Input, OnInit } from '@angular/core';

@Component({
  selector: 'app-initals-avatar',
  templateUrl: './initals-avatar.component.html',
  styleUrls: ['./initals-avatar.component.scss'],
})
export class InitalsAvatarComponent implements OnInit {
  @Input() name: string = '';
  bgColor: string = '';
  textColor: string = '';

  constructor() {}

  ngOnInit(): void {
    this.generateRandomColor(this.name);
  }

  generateRandomColor(name: string): any {
    let hashCode = 0;
    for (let i = 0; i < name.length; i++) {
      hashCode = name.charCodeAt(i) + ((hashCode << 5) - hashCode);
    }

    // Convert the hash code to a hexadecimal color code
    let backgroundColor =
      '#' + (hashCode & 0x00ffffff).toString(16).toUpperCase().padStart(6, '0');

    // Calculate the brightness of the background color (YIQ formula)
    const r = parseInt(backgroundColor.slice(1, 3), 16);
    const g = parseInt(backgroundColor.slice(3, 5), 16);
    const b = parseInt(backgroundColor.slice(5, 7), 16);
    const backgroundBrightness = (r * 299 + g * 587 + b * 114) / 1000;

    // Determine whether to use black or white text based on background luminance
    const textBrightnessThreshold = 128;
    const textColor =
      backgroundBrightness < textBrightnessThreshold ? '#FFFFFF' : '#000000';

    this.bgColor = backgroundColor;
    this.textColor = textColor;
  }

  getInitals() {
    const [first, last] = this.name.split(' ');
    if (last == undefined) return first.charAt(0);
    return first.charAt(0) + last.charAt(0);
  }
}
