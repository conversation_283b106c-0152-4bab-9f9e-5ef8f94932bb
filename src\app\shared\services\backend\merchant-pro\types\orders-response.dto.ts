import { Receipts } from '../../receipts/types/receipts-response.dto';

export interface OrdersDto {
  orders: Order[];
  total: number;
  current: number;
}

export interface Order {
  [key: string]: any;
  sys_id: number;
  id: string;
  payment_status: string;
  payment_status_text: string;
  payment_method_code: string;
  payment_method_name: string;
  shipping_status: string;
  shipping_status_text: string;
  shipping_method_id: number;
  shipping_method_name: string;
  fulfillment_info?: any;
  parcels?: any;
  shipping_amount?: number;
  shipping_tax_amount?: number;
  shipping_cod_amount: number;
  subtotal_including_tax: number;
  subtotal_excluding_tax: number;
  tax_amount: number;
  subtotal_amount: number;
  total_amount: number;
  paid_amount: number;
  currency: string;
  customer_id?: number;
  customer_email: string;
  customer_ip_address?: string;
  customer_ip_country?: string;
  customer_device?: string;
  customer_lang: string;
  customer_note?: any;
  customer_notification?: any;
  date_created: string;
  date_modified: string;
  payment_date?: string;
  created_by: string;
  traffic_source?: any;
  tags?: Tag[];
  billing_type: string;
  billing_name: string;
  billing_company_name?: string;
  billing_company_number?: string;
  billing_company_vat?: string;
  billing_country_code: string;
  billing_country_name: string;
  billing_state: string;
  billing_city: string;
  billing_address: string;
  billing_postal_code?: string;
  billing_full_address: string;
  billing_phone: string;
  shipping_name: string;
  shipping_country_code: string;
  shipping_country_name: string;
  shipping_state: string;
  shipping_city: string;
  shipping_address: string;
  shipping_postal_code?: string;
  shipping_phone: string;
  shipping_full_address: string;
  shipping_address_id?: number;
  bank_name?: any;
  bank_account?: any;
  billing_company_bank_name?: any;
  billing_company_bank_account?: any;
  billing_address_id?: number;
  line_items: Lineitem[];
  info_url: string;
  proforma_url: string;
  payment_url?: any;
  has_duplicates: boolean;
  uid: string;
  mpCustomer?: MpCustomer;
  payment_substatus_id?: number;
  payment_substatus_text?: string;
  wallet_amount?: number;
  coupon_code?: string;
  internal_notes?: Internalnote[];
  date_delivered?: string;
  receipts?: Receipts[];
}

interface Internalnote {
  id: number;
  text: string;
}

interface MpCustomer {
  id: number;
  email: string;
  billing_address_id: number;
  billing_type: string;
  billing_name: string;
  billing_company_name: string;
  billing_company_number: string;
  billing_company_vat: string;
  billing_country_code: string;
  billing_country_name: string;
  billing_state: string;
  billing_city: string;
  billing_address: string;
  billing_address_1: string;
  billing_postal_code: string;
  billing_phone: string;
  shipping_as_billing: boolean;
  shipping_address_id: number;
  shipping_name: string;
  shipping_country_code: string;
  shipping_country_name: string;
  shipping_state: string;
  shipping_city: string;
  shipping_address: string;
  shipping_address_1: string;
  shipping_postal_code: string;
  shipping_phone: string;
  bank_name?: any;
  bank_account?: any;
  billing_company_bank_name?: any;
  billing_company_bank_account?: any;
  date_created: string;
  date_modified: string;
  date_login?: any;
  status: string;
  orders_count: number;
  orders_amount: number;
  abc_class: string;
  wallet?: any;
  newsletter_subscriber?: any;
  created_by: string;
  internal_notes?: any;
  meta_fields: Metafield | any[];
  erp_customer_id?: any;
}

interface Metafield {
  jbkjs: string;
}

export interface Lineitem {
  item_type: string;
  product_id: number;
  product_sku?: any;
  product_ean?: any;
  product_ext_ref?: any;
  product_name: string;
  product_url?: string;
  product_image_url: string;
  product_tax_name: string;
  product_tax_percent: number;
  category_id?: number;
  category_name?: string;
  manufacturer_id?: number;
  manufacturer_name?: string;
  product_availability_id?: number;
  quantity: number;
  unit_price_net: number;
  unit_tax_amount: number;
  unit_price_gross: number;
  line_subtotal_net: number;
  line_tax_amount: number;
  line_subtotal_gross: number;
  name?: string;
  unitPrice: number;
  unitPriceGrossWithDiscount: number;
  totalAmountWithDiscount: number;
  totalAmount: number;
  variant_id?: number;
  variant_name?: string;
  variant_options?: Variantoption[];
  family_sku?: any;
  product_details?: string;
  unit_price_regular?: number;
  unit_price_discounted: number;
}

interface Variantoption {
  id: number;
  name: string;
  value: string;
}

interface Tag {
  id: number;
  name: string;
}

export interface OrderField {
  key: string;
  friendlyName: string;
}
