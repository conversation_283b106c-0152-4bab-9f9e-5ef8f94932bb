<div class="table-container" *ngIf="systemColumns">
  <table
    mat-table
    *transloco="let t"
    [dataSource]="data"
    multiTemplateDataRows
    class="billing-table"
  >
    <ng-container matColumnDef="checkbox">
      <th mat-header-cell *matHeaderCellDef>
        <input type="checkbox" (change)="checkUncheckAll()" />
      </th>
      <td mat-cell *matCellDef="let element">
        <input
          type="checkbox"
          class="checkbox"
          [checked]="getCheckbox(element.invoice_number)"
          (change)="toggleElement(element)"
        />
      </td>
    </ng-container>
    <ng-container matColumnDef="Broj fakture">
      <th mat-header-cell *matHeaderCellDef>
        {{ t("billing.invoice_number") }}
      </th>
      <td mat-cell *matCellDef="let element">{{ element.id }}</td>
    </ng-container>
    <ng-container matColumnDef="Tip fakture">
      <th mat-header-cell *matHeaderCellDef>Tip fakture</th>
      <td mat-cell *matCellDef="let element">{{ element }}</td>
    </ng-container>
    <ng-container matColumnDef="Klijent">
      <th mat-header-cell *matHeaderCellDef>Klijent</th>
      <td mat-cell *matCellDef="let element">{{ element }}</td>
    </ng-container>
    <ng-container matColumnDef="Iznos">
      <th mat-header-cell *matHeaderCellDef>{{ t("amount") }}</th>
      <td mat-cell *matCellDef="let element">
        <ng-container *ngIf="element.total"
          >{{ element.total | rsdCurrency : "RSD " : 2 : "." : "," : 3 }}
        </ng-container>
        <ng-container *ngIf="!element.total"> \ </ng-container>
      </td>
    </ng-container>
    <ng-container matColumnDef="Datum fakture">
      <th mat-header-cell *matHeaderCellDef>Datum fakture</th>
      <td mat-cell *matCellDef="let element">
        {{ element | fiscommDate }}
      </td>
    </ng-container>
    <ng-container matColumnDef="Datum slanja">
      <th mat-header-cell *matHeaderCellDef>Datum slanja</th>
      <td mat-cell *matCellDef="let element">
        {{ element | fiscommDate }}
      </td>
    </ng-container>
    <ng-container matColumnDef="Status">
      <th mat-header-cell *matHeaderCellDef>Status</th>
      <td mat-cell *matCellDef="let element">
        <app-pill
          [text]="getStatus(element.status).text"
          [color]="getStatus(element.status).color"
        ></app-pill>
      </td>
    </ng-container>
    <ng-container matColumnDef="Akcija" stickyEnd>
      <th class="actions" mat-header-cell *matHeaderCellDef>
        {{ t("actions") }}
      </th>
      <td class="actions" mat-cell *matCellDef="let element">
        <mat-icon [matMenuTriggerFor]="menu">more_vert</mat-icon>
        <mat-menu #menu="matMenu">
          <button
            mat-menu-item
            (click)="
              actionEmitter.emit({ action: 'Download-XML', data: element })
            "
          >
            Prezumi XML
          </button>
          <button
            mat-menu-item
            [disabled]="true"
            (click)="
              actionEmitter.emit({ action: 'Download-PDF', data: element })
            "
          >
            Preuzmi PDF
          </button>
        </mat-menu>
      </td>
    </ng-container>
    <ng-container matColumnDef="Details" stickyEnd>
      <th mat-header-cell *matHeaderCellDef class="actions"></th>
      <td mat-cell *matCellDef="let element" class="actions">
        <mat-icon (click)="openDetailsDialog(element)">open_in_new</mat-icon>
      </td>
    </ng-container>

    <tr mat-header-row *matHeaderRowDef="systemColumns"></tr>
    <tr
      mat-row
      *matRowDef="let element; columns: systemColumns"
      class="element-row"
    ></tr>
  </table>
</div>
