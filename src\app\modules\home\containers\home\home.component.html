<ng-container *transloco="let t">

  <div id="homepage" class="p-4">

    <!-- Company information -->

    <div class="row gx-3 mb-3 mt-3">

      <div class="col-12 gx-3">
        <div class="border-warp">
          <div class="welcome col">
            <h1>{{ t("home.title") }}</h1>
          </div>
          <div class="p-md-3 p-sm-2 box company-details">
            <h4>{{ t("home.company_info") }}</h4>
            <div class="row">
              <div class="col-md-6">
                <div class="info-item">
                  <strong>{{ t("home.company_name") }}:</strong>
                  <span>{{ (clientData$ | async)?.billing?.customerName }}</span>
                </div>
                <div class="info-item">
                  <strong>{{ t("home.ident_number") }}:</strong>
                  <span>{{ (clientData$ | async)?.billing?.customerMB }}</span>
                </div>
                <div class="info-item">
                  <strong>{{ t("home.pib") }}:</strong>
                  <span>{{ (clientData$ | async)?.billing?.customerPIB }}</span>
                </div>
              </div>
              <div class="col-md-6">
                <div class="info-item" *ngIf="(clientData$ | async)?.billing?.customerBillingDistrict">
                  <strong>{{ t("home.address") }}:</strong>
                  <span>{{ (clientData$ | async)?.billing?.customerBillingDistrict }}, {{ (clientData$ | async)?.billing?.customerAddress
                    }}</span>
                </div>
                <div class="info-item" *ngIf="(clientData$ | async)?.email">
                  <strong>{{ t("home.email_adress") }}:</strong>
                  <span>{{ (clientData$ | async)?.email }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="row gx-3 mb-3 mt-3">
      <div class="col-lg-6 mb-3 mb-lg-0">
        <div class="border-warp">
          <div class="p-md-3 p-sm-2 box">
            <h5>Izveštaji</h5>
            <p>
              Generišite izveštaje brzo i lako uz pomoć naše napredne
              funkcionalnosti. Pružamo vam mogućnost generisanja izveštaja kako po
              porudžbini, tako i po proizvodu. Otkrijte sve informacije koje vam
              trebaju sa samo nekoliko klikova.
            </p>
            <div class="d-flex justify-content-end">
              <button routerLink="/izvestaji" class="btn btn-outline-primary align-items-end">
                Generiši izveštaj
              </button>
            </div>
          </div>
        </div>
      </div>

      <div class="col-lg-6">
        <div class="border-warp">
          <div class="p-md-3 p-sm-2 box">
            <h5>Pregled računa</h5>
            <p>
              Upravljajte svojim finansijama na jednostavan način. Pregledajte
              postojeće račune ili izdajte nove sa samo nekoliko koraka. Imate
              potpunu kontrolu nad svojim finansijskim transakcijama.
            </p>
            <div class="d-flex justify-content-end">
              <button routerLink="/pregled-racuna" class="btn btn-outline-primary align-items-end">
                Pregled računa
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="row gx-3 mb-3">
      <div class="col-lg-6 mb-3 mb-lg-0">
        <div class="border-warp">
          <div class="p-md-3 p-sm-2 box">
            <h5>Uvoz proizvoda</h5>
            <p>
              Brzo i efikasno unesite veći broj proizvoda odjednom koristeći TSV format.
              Jednostavno pripremite datoteku u Excel-u, sačuvajte kao TSV i uvezite
              kompletan katalog proizvoda sa samo nekoliko klikova.
            </p>
            <div class="d-flex justify-content-end">
              <button routerLink="/proizvodi" class="btn btn-outline-primary align-items-end">
                Upravljanje proizvodima
              </button>
            </div>
          </div>
        </div>
      </div>
      <div class="col-lg-6">
        <div class="border-warp">
          <div class="p-md-3 p-sm-2 box">
            <h5>Prilagođena polja</h5>
            <p>
              Kreirajte prilagođena polja za vaše račune prema specifičnim potrebama vašeg poslovanja.
              Definišite izborne opcije, postavite obavezna polja i organizujte podatke na način koji
              vam najviše odgovara za bolje izveštavanje i analizu.
            </p>
            <div class="d-flex justify-content-end">
              <button routerLink="/podesavanja" class="btn btn-outline-primary align-items-end">
                Podešavanja polja
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="row gx-3">
      <div class="col-lg-6 mb-3 mb-lg-0">
        <div class="border-warp">
          <div class="p-md-3 p-sm-2 box">
            <h5>Podrazumevani kasir</h5>
            <p>
              Postavite podrazumevanog kasira za brže kreiranje računa. Ova funkcionalnost
              smanjuje mogućnost ljudske greške i pojednostavljuje tok rada tokom smena
              sa jednim aktivnim kasirom.
            </p>
            <div class="d-flex justify-content-end">
              <button routerLink="/kasiri" class="btn btn-outline-primary align-items-end">
                Upravljanje kasirima
              </button>
            </div>
          </div>
        </div>
      </div>

      <div class="col-lg-6">
        <div class="border-warp">
          <div class="p-md-3 p-sm-2 box">
            <h5>Podešavanja</h5>
            <p>
              Stojimo vam na raspolaganju za svaki izazov koji možete imati.
              Možete pronaći opcije za podešavanja direktno na našoj platformi.
              Naš cilj je da vam pružimo podršku u vezi sa svim pitanjima i
              zahtevima vezanim za naš proizvod.
            </p>
            <div class="d-flex justify-content-end">
              <button [routerLink]="'/podesavanja'" class="btn btn-outline-primary">
                Podešavanja
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</ng-container>