import { Component, Input, OnInit, ViewChild, ElementRef, AfterViewInit } from '@angular/core';
import { FormControl } from '@angular/forms';
import { Observable, debounceTime, distinctUntilChanged, map, of, startWith, switchMap } from 'rxjs';
import { Cashier } from 'src/app/models/cashier.model';
import { CashierService } from 'src/app/shared/services/firestore/cashier.service';
import { MatAutocompleteTrigger } from '@angular/material/autocomplete';

@Component({
  selector: 'app-cashier-autocomplete',
  templateUrl: './cashier-autocomplete.component.html',
  styleUrls: ['./cashier-autocomplete.component.scss']
})
export class CashierAutocompleteComponent implements OnInit, AfterViewInit {
  @Input() control!: FormControl;
  @Input() placeholder: string = 'Cashier';
  @ViewChild(MatAutocompleteTrigger) autocompleteTrigger?: MatAutocompleteTrigger;

  filteredOptions$!: Observable<Cashier[]>;
  filteredCashiers: Cashier[] = [];
  allCashiers: Cashier[] = [];
  isLoading = false;

  constructor(private cashierService: CashierService) {
    this.cashierService.getActiveCashiers().subscribe(cashiers => {
      this.allCashiers = cashiers;
      this.filteredCashiers = cashiers;
    });
  }

  ngOnInit() {
    // Convert any object to string format on initialization if needed
    this.ensureStringValue();

    this.control.valueChanges.subscribe(value => {
      this.filterCashiers(value);
    });
  }

  // Ensure the initial value is in string format
  private ensureStringValue() {
    setTimeout(() => {
      const value = this.control.value;
      if (value && typeof value === 'object') {
        // Convert object to string format
        const stringValue = this.getDisplayString(value);
        console.log('Converting cashier object to string:', stringValue);
        this.control.setValue(stringValue, { emitEvent: false });
      }
    }, 0);
  }

  ngAfterViewInit() {
    // This ensures the dropdown opens when clicking the field
    setTimeout(() => {
      this.control.valueChanges.pipe(
        startWith(this.control.value)
      ).subscribe(() => {
        const inputElement = this.getInputElement();
        if (inputElement && document.activeElement === inputElement && this.autocompleteTrigger) {
          try {
            this.autocompleteTrigger.openPanel();
          } catch (e) {
            console.log('Could not open autocomplete panel');
          }
        }
      });
    }, 500);
  }

  // Function for handling the click event in the template
  showAllSuggestions() {
    // Clear the input to show all suggestions
    if (this.control.value && typeof this.control.value === 'object') {
      // Preserve the object if it's already selected
      return;
    }

    // Open the dropdown
    setTimeout(() => {
      if (this.autocompleteTrigger) {
        try {
          this.autocompleteTrigger.openPanel();
        } catch (e) {
          console.log('Could not open autocomplete panel');
        }
      }
    }, 300);
  }

  // Get all active cashiers
  private getAllActiveCashiers(): Observable<Cashier[]> {
    this.isLoading = true;
    return this.cashierService.getActiveCashiers().pipe(
      map(cashiers => {
        this.isLoading = false;
        return cashiers;
      })
    );
  }

  filterCashiers(value: Cashier | string) {
    if (!value) {
      this.filteredCashiers = this.allCashiers;
      return;
    }

    if (typeof value === 'string' && value.trim() === '') {
      this.filteredCashiers = this.allCashiers;
      return;
    }

    this.filteredCashiers = this.allCashiers.filter(cashier => {
      let searchValue = this.getDisplayString(cashier);

      if (typeof value === 'string') {
        return searchValue.toLowerCase().includes(value.toLowerCase());
      } else {
        const objDisplayValue = this.getDisplayString(value);
        return searchValue.toLowerCase().includes(objDisplayValue.toLowerCase());
      }
    });
  }

  // Helper to get the input element
  private getInputElement(): HTMLElement | null {
    return document.querySelector('app-cashier-autocomplete app-fiscomm-input input');
  }

  // Get standardized display string for a cashier
  private getDisplayString(cashier: Cashier): string {
    if (!cashier) return '';
    return  cashier.name;
  }

  // Display function for the autocomplete
  displayFn = (cashier: Cashier | string): string => {
    if (!cashier) return '';

    // If it's already a string, return it directly
    if (typeof cashier === 'string') {
      return cashier;
    }

    // Return the formatted string for a cashier object
    return this.getDisplayString(cashier);
  }

  // Handle the option selection to convert to string format
  optionSelected(event: any) {
    const cashier = event.option.value;
    if (cashier && typeof cashier === 'object') {
      // Convert to string format when selecting
      setTimeout(() => {
        const stringValue = this.getDisplayString(cashier);
        this.control.setValue(stringValue);
      }, 0);
    }
  }
}
