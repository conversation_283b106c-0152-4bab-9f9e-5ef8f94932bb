.mat-form-field {
  font-size: 12px;
  font-style: italic;
  font-weight: 500;
  width: 100%;
}

.matLabel {
  color: #044962a1;
}

::ng-deep .mat-form-field-suffix {
  color: #044962a1;
}

::ng-deep {
  .readonly {
    .mat-form-field-outline {
      background-color: #01202c09 !important;
    }
  }
}

.inputcontainer {
  position: relative;
  width: 100%;
}

input {
  width: 100%;
  box-sizing: border-box;
}

.icon-container {
  position: absolute;
  right: 10px;
  top: calc(50% - 12px);
}
.loader {
  position: relative;
  height: 20px;
  width: 20px;
  display: inline-block;
  animation: around 5.4s infinite;
}

@keyframes around {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loader::after,
.loader::before {
  content: "";
  background: transparent;
  position: absolute;
  display: inline-block;
  width: 100%;
  height: 100%;
  border-width: 2px;
  border-color: #333 #333 transparent transparent;
  border-style: solid;
  border-radius: 20px;
  box-sizing: border-box;
  top: 0;
  left: 0;
  animation: around 0.7s ease-in-out infinite;
}

.loader::after {
  animation: around 0.7s ease-in-out 0.1s infinite;
  background: transparent;
}
