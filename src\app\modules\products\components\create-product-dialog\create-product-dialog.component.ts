import { Component, OnInit } from '@angular/core';
import { ProductsService } from 'src/app/shared/services/backend/products/products.service';
import { FormControl, Validators } from '@angular/forms';
import { Product } from 'src/app/shared/models/product.model';
import { TaxService } from 'src/app/shared/services/backend/system/tax.service';

@Component({
  selector: 'app-create-product-dialog',
  templateUrl: './create-product-dialog.component.html',
  styleUrls: ['./create-product-dialog.component.scss']
})
export class CreateProductDialogComponent implements OnInit {

  constructor(
    private productService: ProductsService,
    private taxService: TaxService
  ) { }

  name: FormControl = new FormControl('', Validators.required);
  price: FormControl = new FormControl('', Validators.required);
  gtin: FormControl = new FormControl('');
  productCode: FormControl = new FormControl('');
  taxLabel: FormControl = new FormControl('');

  // Store multi-currency prices
  prices: { [currencyCode: string]: number } = {};

  // Tax rate options for the dropdown
  taxRateOptions: any[] = [];

  ngOnInit(): void {
    // Get tax rate options
    this.taxService.getTaxRates$().subscribe(taxRates => {
      this.taxRateOptions = taxRates;
    });
  }

  addProduct() {
    const product: Product = {
      name: this.name.value,
      price: this.price.value,
      gtin: this.gtin.value,
      productCode: this.productCode.value,
      taxLabel: this.taxLabel.value,
      createdAt: new Date(),
      prices: this.prices
    };

    this.productService.addNewProduct(product).subscribe();
  }

  onPricesChanged(prices: { [currencyCode: string]: number }): void {
    this.prices = prices;
  }
}
