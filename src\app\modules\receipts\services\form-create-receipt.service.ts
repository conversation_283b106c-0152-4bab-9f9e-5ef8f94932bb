import { ChangeDetectorRef, Injectable, NgZone } from '@angular/core';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
  FormArray,
  ValidatorFn,
  ValidationErrors,
  AbstractControl,
  AsyncValidatorFn,
} from '@angular/forms';
import {
  Item,
  Receipts,
} from 'src/app/shared/services/backend/receipts/types/receipts-response.dto';
import { CreateReceiptValidators } from '../validators/create-receipt-validators';
import { FiscommUtils } from 'src/app/shared/utils/fiscomm-utils';
import { DatePipe } from '@angular/common';
import { T } from '@angular/cdk/keycodes';
import { ReceiptsService } from 'src/app/shared/services/backend/receipts/receipts.service';
import {
  Observable,
  Subject,
  debounceTime,
  distinctUntilChanged,
  map,
  of,
  switchMap,
  take,
} from 'rxjs';
import { ClientService } from 'src/app/shared/services/backend/clients/client.service';
import { AuthService } from 'src/app/core/services/auth.service';
import { UserSettingsService, ExchangeRateResponse } from '../../../shared/services/backend/user/user-settings.service';

@Injectable({
  providedIn: 'root',
})
export class FormCreateReceiptService {
  private receiptForm: FormGroup = new FormGroup({});

  mainTaxLabel: FormControl = new FormControl('');
  settingsTaxLabel: string = '';
  footerText: string = '';
  loadingRefDoc: Subject<boolean> = new Subject<boolean>();
  badRequestAllowed: boolean = false;
  exchangeRate: ExchangeRateResponse | null = null;

  openInNew: FormControl = new FormControl(true);

  badRequest: boolean = false;

  uid: any;
  defaultSettings: any;
  isClientEmailActive: boolean = false;

  constructor(
    private fb: FormBuilder,
    private datePipe: DatePipe,
    private receiptService: ReceiptsService,
    private authService: AuthService,
    private clientService: ClientService,
    private userSettingsService: UserSettingsService
  ) {
    this.initForm();
    this.mainTaxLabel.valueChanges.subscribe((value) => {
      this.updateEmptyLabels(value);
    });
    this.getCurrencyControl().valueChanges.subscribe((value) => {
      this.updateExchangeRate();
    });
    this.getCurrencyDateControl().valueChanges.subscribe((value) => {
      this.updateExchangeRate();
    });
  }

  private updateEmptyLabels(value: string) {
    this.getItemsFormArray().controls.forEach((control) => {
      if (
        control.get('label')?.value == '' ||
        control.get('label')?.value == undefined
      ) {
        control.get('label')?.setValue(value);
      }
    });
  }

  getUserDataAndSetDefualtSettings() {
    this.getUserData();
  }

  private getUserData() {
    this.clientService.getClient().subscribe((client) => {
      this.defaultSettings = client.defaultSettings;
      this.isClientEmailActive = client.receipts?.isEmailActive;
      this.footerText = client.defaultSettings?.receiptFooter || '';
      this.setDefaultFormData(this.defaultSettings);
    });
  }

  private setDefaultFormData(data: any) {
    if (!this.getInvoiceType()?.value) {
      this.getInvoiceType().setValue(data.invoiceType);
    }
    if (!this.getTransactionType()?.value) {
      this.getTransactionType().setValue(data.transactionType);
    }
    this.settingsTaxLabel = data.taxLabel;
    if (this.mainTaxLabel.value == '')
      this.mainTaxLabel.setValue(data.taxLabel);

    if (this.footerText)
      this.getControl('textFooter').setValue(this.footerText);

    if (this.defaultSettings?.defaultPaymentMethod) {
      this.getPaymentFormArray().controls[0].get('paymentType')?.setValue(this.defaultSettings.defaultPaymentMethod);
      this.getPaymentFormArray().controls[0].get('amount')?.setValue(null);
      this.getPaymentFormArray().controls[0].get('paidByAdvance')?.setValue(null);
    }

    // Set default cashier if available
    if (this.defaultSettings?.defaultCashier) {
      console.log('Setting default cashier', this.defaultSettings.defaultCashier.name);
      this.getControl('cashier').setValue(this.defaultSettings.defaultCashier);
    }
  }

  getMainTax() {
    return this.mainTaxLabel as FormControl;
  }

  loadFormData(data: any) {

    this.getInvoiceType().setValue(data.invoiceType);
    this.getTransactionType().setValue(data.transactionType);
    this.getInvoiceNumberPos().setValue(data.invoiceNumberPos);
    this.getReferentDocumentNumber().setValue(data.referentDocumentNumber);
    this.getReferentDocumentDT().setValue(data.referentDocumentDT);

    this.getBuyerIDPrefix().setValue(data.buyerIDPrefix);
    this.getBuyerId().setValue(data.buyerId);

    this.getPrefixBuyerCostCenterId().setValue(data.prefixBuyerCostCenterId);
    this.getSufixBuyerCostCenterId().setValue(data.sufixBuyerCostCenterId);
    this.getEnableDifferentSums().setValue(data.enableDifferentSums);

    this.mainTaxLabel.setValue(data.mainTaxLabel);

    //load items
    let tempArray: any = this.fb.array([]);
    data.items.forEach((item: any) => {
      tempArray.push(this.fb.group(item));
    });

    const itemsFormArray = this.receiptForm.get('items') as FormArray;
    itemsFormArray.clear();

    tempArray.controls.forEach((control: any) => {
      itemsFormArray.push(control);
    });

    //load payments
    tempArray = this.fb.array([]);
    data.payment.forEach((item: any) => {
      tempArray.push(this.fb.group(item));
    });

    const paymentFormArray = this.receiptForm.get('payment') as FormArray;
    paymentFormArray.clear();

    tempArray.controls.forEach((control: any) => {
      paymentFormArray.push(control);
    });

    //load metafileds
    tempArray = this.fb.array([]);
    data.metaFields.forEach((item: any) => {
      tempArray.push(this.fb.group(item));
    });

    const metaFormArray = this.receiptForm.get('metaFields') as FormArray;
    metaFormArray.clear();

    tempArray.controls.forEach((control: any) => {
      metaFormArray.push(control);
    });

    // load cashier if available
    console.log('Setting cashier', data.cashier);
    if (data.cashier) {
      console.log('Setting cashier', data.cashier);
      this.getControl('cashier').setValue(data.cashier);
    }
  }

  private initForm() {
    this.receiptForm = this.fb.group({
      invoiceType: ['', Validators.required],
      transactionType: ['', Validators.required],
      invoiceNumberPos: [''],
      referentDocumentNumber: [
        '',
        [this.refDocFormatValidator(), this.refDTBadRequestValidator()],
      ],
      referentDocumentDT: [''],
      dateAndTimeOfIssue: '',

      buyerIDPrefix: '',
      buyerId: '',
      customerName: '',

      prefixBuyerCostCenterId: '',
      sufixBuyerCostCenterId: '',
      enableDifferentSums: false,
      currency: ['RSD'],
      currencyDate: [new Date().toISOString().slice(0, 10)],
      exchangeRate: 1,
      exchangeRateDisplay: '1 RSD',

      cashier: '',

      payment: this.fb.array(
        [],
        [CreateReceiptValidators.atLeastOneRowRequired]
      ),

      metaFields: this.fb.array([]),
      items: this.fb.array([], [CreateReceiptValidators.atLeastOneRowRequired]),

      toEmail: ['', Validators.email],
      textHeader: '',
      textFooter: '',
    });
    this.receiptForm.setValidators([
      CreateReceiptValidators.validateAmountExceptAdvance,
      this.sumComparisonValidator,
      CreateReceiptValidators.referentDocumentNumberRequired,
      CreateReceiptValidators.customerIndetificationRequired,
      CreateReceiptValidators.buyerIdRequired,
      CreateReceiptValidators.buyerCostIdRequired,
      CreateReceiptValidators.refDocDtRequired,
      CreateReceiptValidators.requiredMetaFieldsValidator,
    ]);

    this.addPaymentRow();
    // Only add a single item row by default
    this.addFirstItemRow(); // Add default item row
    this.detectFiledsToEmpty();
    this.checkDT();

    this.receiptForm.valueChanges
      .pipe(
        distinctUntilChanged(
          (prev, curr) => JSON.stringify(prev) === JSON.stringify(curr)
        )
      )
      .subscribe(() => {
        this.updateRefDocValidators();
        this.updateRefDocDtValidators();
        this.updateCustomerIdenValidators();
      });

    // Subscribe to changes in the items FormArray
    const itemsArray = this.getItemsFormArray();
    itemsArray.valueChanges.subscribe(() => {
      this.updateFirstPaymentAmount();
    });

    // Subscribe to the payment array for structural changes (add/remove rows)
    const paymentArray = this.getPaymentFormArray();
    paymentArray.valueChanges.subscribe(() => {
      // When any payment values change, update the first payment
      this.updateFirstPaymentAmount();
    });

    // Also subscribe to enableDifferentSums changes to update payment as needed
    this.getEnableDifferentSums().valueChanges.subscribe(isDifferent => {
      if (!isDifferent) {
        this.updateFirstPaymentAmount();
      }
    });
  }

  updateRefDoc() {
    this.getReferentDocumentNumber().updateValueAndValidity();
  }

  checkDT() {
    this.getReferentDocumentNumber()
      .valueChanges.pipe(distinctUntilChanged(), debounceTime(800))
      .subscribe((value) => {
        if (value != null) {
          this.loadingRefDoc.next(false);
          const transactionType = this.getTransactionType()?.value;
          if (
            this.getReferentDocumentNumber().value != '' &&
            !this.getReferentDocumentNumber().hasError('invalidFormat')
          ) {
            this.loadingRefDoc.next(true);
            this.getReferentDocumentNumber().updateValueAndValidity();
            this.getReferentReceipt(value);
          } else {
            this.getReferentDocumentNumber().updateValueAndValidity();
          }
        }
      });
  }

  private getReferentReceipt(invoiceNumber: any) {
    this.receiptService
      .getReceipts({ invoiceNumber: invoiceNumber })
      .subscribe((refReceipt) => {
        this.loadingRefDoc.next(false);
        if (refReceipt.data.length > 0) {
          this.getReferentDocumentDT().setValue(
            refReceipt.data[0].sdc_date_time.slice(0, 19)
          );
          this.badRequest = false;
          this.getReferentDocumentNumber().updateValueAndValidity();
        } else {
          this.getReferentDocumentNumber().updateValueAndValidity();
          this.getReferentDocumentNumber().setErrors({
            badRequest: true,
          });
          this.badRequest = true;
        }
      });
  }

  refDTBadRequestValidator() {
    return (control: any) => {
      if (
        control.value == null ||
        control.value == undefined ||
        control.value == '' ||
        this.badRequestAllowed
      ) {
        return null;
      }
      return this.badRequest ? { badRequest: this.badRequest } : null;
    };
  }

  private detectFiledsToEmpty() {
    this.getTransactionType().valueChanges.subscribe((type) => {
      if (type != 'Finalize') this.getReferentDocumentDT().setValue('');
      if (type != 'Sale') this.getDateControl().setValue('');
    });

    this.getInvoiceType().valueChanges.subscribe((type) => {
      this.getTransactionType().setValue('');
    });
  }

  refDocFormatValidator() {
    return (control: any) => {
      if (!control.value) {
        if (this.getTransactionType()?.value == 'Refund')
          return { invalidFormat: true };
        return null;
      }
      const validFormat = /^[\w\d]+-[\w\d]+-[\d]+$/.test(control.value);

      return validFormat ? null : { invalidFormat: true };
    };
  }

  getReceiptForm(): FormGroup {
    this.getItemsFormArray().controls.forEach((control) => {
      this.updateTotalAmount(control as FormControl);
    });
    return this.receiptForm;
  }

  isValid() {
    return this.receiptForm.valid;
  }

  getControl(key: string) {
    return this.receiptForm.get(key) as FormControl;
  }

  createControl(key: string, value: any, validators?: ValidatorFn[]) {
    if (this.receiptForm.contains(key)) {
      this.receiptForm.setControl(key, new FormControl(value[0], validators));
    } else {
      this.receiptForm.addControl(key, new FormControl(value[0], validators));
    }
    return this.receiptForm.get(key) as FormControl;
  }

  setReceiptFormData(
    data: Receipts,
    type?: string,
    transactionType?: string,
    disabled?: any
  ) {
    if (disabled) this.disableControls(disabled);
    if (type) this.getInvoiceType().setValue(type);
    else this.getInvoiceType().setValue(data.invoice_type.invoice_type);

    if (transactionType) this.getTransactionType().setValue(transactionType);
    else
      this.getTransactionType().setValue(
        data.transaction_type.transaction_type
      );

    this.getInvoiceNumberPos().setValue(data.invoice_number_pos);
    this.getReferentDocumentNumber().setValue(data.referent_document_number);
    if (
      transactionType == 'Refund' ||
      data.invoice_type.invoice_type == 'Advance'
    )
      this.getReferentDocumentNumber().setValue(data.invoice_number);

    if (transactionType == 'Finalize') {
      this.getReferentDocumentDT().setValue(new Date(data.sdc_date_time));
    }

    this.splitAndSetBuyerId(data.buyer_id);
    this.splitAndSetBuyerCostCenterId(data.buyer_cost_center_id);

    this.emptyItems();
    this.setPayments(data.payments);
    data.requestItems
      ? this.setItems(data.requestItems)
      : this.setItems(data.items);

    this.getControl('cashier').setValue(data.cashier);

    // Set meta fields if available
    if (data.meta_fields && data.meta_fields.length > 0) {
      this.setMetaFields(data.meta_fields);
    }
  }

  private disableControls(disabled: any) {
    Object.keys(disabled).forEach((key) => {
      if (disabled[key]) this.getControl(key).disable();
    });
  }

  private splitAndSetBuyerId(buyer_id: string) {
    if (buyer_id) {
      let parts = buyer_id.split(':');
      this.getBuyerIDPrefix().setValue(parts[0]);
      this.getBuyerId().setValue(parts[1]);
    }
  }

  private splitAndSetBuyerCostCenterId(buyer_cost_center_id: string) {
    if (buyer_cost_center_id) {
      let parts = buyer_cost_center_id.split(':');
      this.getPrefixBuyerCostCenterId().setValue(parts[0]);
      this.getSufixBuyerCostCenterId().setValue(parts[1]);
    }
  }

  private setPayments(payments: any[]) {
    payments.forEach((payment) => {
      this.addPaymentRow(payment);
    });
  }

  private setItems(items: any[]) {
    items.forEach((item) => {
      this.addItemRow(item);
    });
  }

  private emptyItems() {
    this.getPaymentFormArray().controls = [];
    this.getItemsFormArray().controls = [];
    this.getMetaFieldFormArray().controls = [];
  }

  private setMetaFields(metaFields: any[]) {
    if (!metaFields || !Array.isArray(metaFields)) return;

    // Get existing meta fields array
    const metaFieldsArray = this.getMetaFieldFormArray();

    // Iterate through the receipt's meta fields
    metaFields.forEach(field => {
      // Try to find existing control with the same key
      const existingControl = metaFieldsArray.controls.find((control: any) =>
        control.get('key')?.value === field.key
      );

      if (existingControl) {
        // If exists, just update the value
        existingControl.get('value')?.setValue(field.value || '');
      } else {
        // If doesn't exist, create and add a new form group
        const metaFieldGroup = this.fb.group({
          key: field.key,
          value: field.value || ''
        });
        metaFieldsArray.push(metaFieldGroup);
      }
    });
  }

  resetForm() {
    // First clear all form arrays
    this.getPaymentFormArray().clear();
    this.getItemsFormArray().clear();
    this.getMetaFieldFormArray().clear();

    // Reset the entire form
    this.receiptForm.reset();
    this.mainTaxLabel.setValue('');

    // Add just one payment row and one item row
    this.addPaymentRow();
    this.addFirstItemRow();
    // reset currency and currency date
    this.getCurrencyControl().setValue('RSD');
    this.getCurrencyDateControl().setValue(new Date().toISOString().slice(0, 10));
  }

  getPreparedReceiptData() {
    let data = { ...this.receiptForm.getRawValue() };
    FiscommUtils.removeEmpty(data);
    let { items, enableDifferentSums, metaFields, ...rest } = data;

    if (data.transaction_type != '')
      metaFields = this.prepeareMetaFields(metaFields);
    if (!metaFields) metaFields = [];

    if (data.requestItems) items = data.requestItems;

    // Get currency and exchange rate information
    const currency = this.getCurrencyControl().value;
    const exchangeRate = this.getExchangeRateControl().value || 1;
    const isNonRSD = currency !== 'RSD';

    // Helper function to round to 2 decimal places
    const roundToTwo = (num: number): number => {
      return Math.round((Number(num) + Number.EPSILON) * 100) / 100;
    };

    let result = {
      ...rest,
      items: items.map((el: any) => {
        el.labels = new Array(1).fill(el.label);
        if (el.gtin) el.name = `${el.gtin} ${el.name}`;

        // Convert price to RSD if using non-RSD currency
        if (isNonRSD) {
          el.unitPrice = roundToTwo(el.unitPrice * exchangeRate);
        } else {
          el.unitPrice = roundToTwo(el.unitPrice);
        }

        // if discount exists, we need to subtract discount amount from the unitPrice of the item,
        // so that quantity * unitPrice = totalAmount
        if (el.discount) {
          el.unitPrice = roundToTwo(el.unitPrice - (el.unitPrice * el.discount / 100));
        }
        el.totalAmount = roundToTwo(el.unitPrice * el.quantity);

        return el;
      }),
      metaFields: metaFields,
      buyerId: this.prepeareBuyerId(data),
      buyerCostCenterId: this.prepeareBuyerCostId(data),
      currency: 'RSD', // Always submit as RSD
      originalCurrency: isNonRSD ? currency : undefined, // Store original currency if not RSD
      originalExchangeRate: isNonRSD ? exchangeRate : undefined, // Store exchange rate if not RSD
    };

    // Convert payment amounts to RSD if using non-RSD currency
    if (result.payment) {
      result.payment.forEach((payment: any) => {
        if (isNonRSD) {
          payment.amount = roundToTwo(payment.amount * exchangeRate);

          if (payment.paidByAdvance) {
            payment.paidByAdvance = roundToTwo(payment.paidByAdvance * exchangeRate);
          }
        } else {
          payment.amount = roundToTwo(payment.amount);

          if (payment.paidByAdvance) {
            payment.paidByAdvance = roundToTwo(payment.paidByAdvance);
          }
        }
      });
    }

    return result;
  }

  private prepeareBuyerId(data: any) {
    if (data.buyerIDPrefix != undefined)
      return `${data.buyerIDPrefix}:${data.buyerId}`;
    return null;
  }

  private prepeareBuyerCostId(data: any) {
    if (data.prefixBuyerCostCenterId != undefined)
      return `${data.prefixBuyerCostCenterId}:${data.sufixBuyerCostCenterId}`;
    return null;
  }

  private prepeareMetaFields(data: any) {
    return data.filter((field: any) => {
      if (field.value != undefined && field.value != null) return true;
      return false;
    });
  }

  markFormGroupTouched(formGroup: FormGroup) {
    Object.values(formGroup.controls).forEach((control) => {
      if (control instanceof FormArray) {
        this.markFormArrayAndGroupsTouched(
          this.receiptForm.get('payment') as FormArray
        );
        this.markFormArrayAndGroupsTouched(
          this.receiptForm.get('items') as FormArray
        );
      } else {
        control.markAsTouched();
      }
    });
  }

  private markFormArrayAndGroupsTouched(formArray: FormArray) {
    formArray.controls.forEach((control: any) => {
      control.markAsTouched();
      this.markFormGroupTouched(control);
    });
  }

  hasError(value: string) {
    return this.receiptForm.hasError(value);
  }

  private isUpdatingRefDoc = false;
  private updateRefDocValidators() {
    if (!this.isUpdatingRefDoc) {
      this.isUpdatingRefDoc = true;
      if (this.hasError('referentDocumentNumberRequired'))
        this.getReferentDocumentNumber().addValidators(Validators.required);
      else
        this.getReferentDocumentNumber().removeValidators(Validators.required);
      this.getReferentDocumentNumber().updateValueAndValidity();
      this.isUpdatingRefDoc = false;
    }
  }

  private isUpdatingRefDocDT = false;
  private updateRefDocDtValidators() {
    if (!this.isUpdatingRefDocDT) {
      this.isUpdatingRefDocDT = true;
      if (this.hasError('refDocDtRequired'))
        this.getReferentDocumentDT().addValidators(Validators.required);
      else this.getReferentDocumentDT().removeValidators(Validators.required);
      this.getReferentDocumentDT().updateValueAndValidity();
      this.isUpdatingRefDocDT = false;
    }
  }

  private isUpdatingCustomerIden = false;
  private updateCustomerIdenValidators() {
    if (!this.isUpdatingCustomerIden) {
      this.isUpdatingCustomerIden = true;
      const buyerIdControl = this.getBuyerId();
      const buyerIDPrefixControl = this.getBuyerIDPrefix();
      const buyerCostIdControl = this.getPrefixBuyerCostCenterId();
      const buyerCostIdSufixControl = this.getSufixBuyerCostCenterId();

      // Apply validators directly instead of calling methods that could cause recursion
      // Buyer ID validation
      if (this.hasError('customerIndetificationRequired') || this.hasError('buyerIdRequired')) {
        this.setValidator(
          [buyerIdControl, buyerIDPrefixControl],
          Validators.required
        );
      } else {
        this.removeValidator(
          [buyerIdControl, buyerIDPrefixControl],
          Validators.required
        );
      }

      // Buyer Cost ID validation
      if (this.hasError('buyerCostIdRequired')) {
        this.setValidator(
          [buyerCostIdControl, buyerCostIdSufixControl],
          Validators.required
        );
      } else {
        this.removeValidator(
          [buyerCostIdControl, buyerCostIdSufixControl],
          Validators.required
        );
      }

      // Update validity without triggering additional events
      this.updateValueAndValidity([
        buyerIdControl,
        buyerIDPrefixControl,
        buyerCostIdControl,
        buyerCostIdSufixControl,
      ]);

      this.isUpdatingCustomerIden = false;
    }
  }

  private setValidator(controls: FormControl[], validator: ValidatorFn) {
    controls.forEach((control) => control.setValidators(validator));
  }
  private removeValidator(controls: FormControl[], validator: ValidatorFn) {
    controls.forEach((control) => control.removeValidators(validator));
  }
  private updateValueAndValidity(controls: FormControl[]) {
    // Update without triggering valueChanges events
    controls.forEach((control) => control.updateValueAndValidity({emitEvent: false}));
  }

  //date
  getDateControl(): FormControl {
    return this.receiptForm.get('dateAndTimeOfIssue') as FormControl;
  }

  dateNeeded() {
    return (
      this.getInvoiceType().value == 'Advance' &&
      this.getTransactionType().value == 'Sale'
    );
  }

  //meta fields

  getMetaFieldFormArray() {
    return this.receiptForm.get('metaFields') as FormArray;
  }

  addMetaFieldControl(key: string, isRequired: boolean = false) {
    const field = this.fb.group({
      key: key,
      value: [''],
      isRequired: isRequired
    });
    this.getMetaFieldFormArray().push(field);
  }

  getMetaFieldControl(indexOrKey: number | string) {
    const metaFields = this.getMetaFieldFormArray();

    // If it's a string, find the control by key
    if (typeof indexOrKey === 'string') {
      const fieldGroup = metaFields.controls.find((control: any) =>
        control.get('key')?.value === indexOrKey
      );
      return fieldGroup ? fieldGroup.get('value') as FormControl : new FormControl('');
    }

    // If it's a number, use as index (legacy support)
    return metaFields.controls[indexOrKey]?.get('value') as FormControl || new FormControl('');
  }

  //itmems
  getItemsFormArray() {
    return this.receiptForm.get('items') as FormArray;
  }

  addItemRow(item?: any) {
    let itemRow = this.fb.group({
      unitPrice: ['', [Validators.required, Validators.min(1)]],
      totalAmount: ['', [Validators.required, Validators.min(1)]],
      quantity: ['', [Validators.required, Validators.min(0.1)]],
      name: ['', Validators.required],
      label: [this.mainTaxLabel.value, Validators.required],
      gtin: ['', [Validators.pattern('^[0-9]{8,13}$')]],
      discount: ['', [Validators.min(0), Validators.max(99)]],
    });

    if (item) {
      itemRow.controls.unitPrice.setValue(
        item.unitPrice.toString().replace(/,/g, '')
      );
      itemRow.controls.totalAmount.setValue(item.totalAmount.toString());
      itemRow.controls.quantity.setValue(item.quantity.toString());
      itemRow.controls.name.setValue(item.name.toString());
      if (item.label) itemRow.controls.label.setValue(item.label.toString());
      else itemRow.controls.label.setValue(item.labels[0].toString());
      if (item.gtin) itemRow.controls.gtin.setValue(item.gtin.toString());
      if (item.discount) itemRow.controls.discount.setValue(item.discount.toString());
    }

    // Listen for changes to any value in this row
    const controls = ['quantity', 'unitPrice', 'discount'];
    controls.forEach(controlName => {
      const control = itemRow.get(controlName);
      control?.valueChanges.subscribe(() => {
        this.updateTotalAmount(itemRow as any);
      });
    });

    this.updateTotalAmount(itemRow as any);
    this.getItemsFormArray().push(itemRow);
  }

  addFirstItemRow() {
    let itemRow = this.fb.group({
      unitPrice: ['', [Validators.required, Validators.min(1)]],
      totalAmount: ['', [Validators.required, Validators.min(1)]],
      quantity: ['', [Validators.required, Validators.min(0.1)]],
      name: ['', Validators.required],
      label: [this.settingsTaxLabel, Validators.required],
      discount: ['', [Validators.min(0), Validators.max(99)]],
      gtin: ['', [Validators.pattern('^[0-9]{8,13}$')]],
    });
    this.getItemsFormArray().push(itemRow);
  }

  private updateTotalAmount(itemRow: FormControl) {
    const unitPrice = itemRow.get('unitPrice')?.value;
    const quantity = itemRow.get('quantity')?.value;
    const discount = itemRow.get('discount')?.value || 0;

    // Calculate total amount considering discount
    let totalAmount = unitPrice * quantity;

    // Apply discount if present
    if (discount > 0) {
      const discountAmount = totalAmount * (discount / 100);
      totalAmount = totalAmount - discountAmount;
    }

    // Round to 2 decimal places
    totalAmount = Math.round((totalAmount + Number.EPSILON) * 100) / 100;
    itemRow.get('totalAmount')?.setValue(totalAmount);
  }

  getItemControl(index: number, name: string) {
    const temp = this.getItemsFormArray().controls[index];
    if (temp) return temp.get(name) as FormControl;
    return new FormControl();
  }

  removeItemRow(index: number) {
    this.getItemsFormArray().controls.splice(index, 1);
  }

  getItemsAmountSum() {
    let sum = 0;
    this.getItemsFormArray().controls.forEach(
      (item) => (sum += item.get('totalAmount')?.value as number)
    );
    return sum;
  }

  changeAllItemsTax(label: string) {
    this.getItemsFormArray().controls.forEach((item) => {
      const tmp = item as FormGroup;
      if (!tmp.get('label')?.value) tmp.get('label')?.setValue(label);
    });
  }

  //payments
  getPaymentFormArray() {
    return this.receiptForm.get('payment') as FormArray;
  }

  getPaymentControl(index: number, name: string) {
    return this.getPaymentFormArray().controls[index].get(name) as FormControl;
  }

  addPaymentRow(payment?: any) {
    const itemRow = this.fb.group({
      amount: ['', Validators.required],
      paymentType: ['', Validators.required],
      paidByAdvance: '',
    });
    if (payment) {
      itemRow.controls.amount.setValue(payment.amount?.toString() || 0);
      itemRow.controls.paymentType.setValue(payment.payment_type.payment_type);
      if (payment.paidByAdvance)
        itemRow.controls.paidByAdvance.setValue(
          payment.paidByAdvance.toString()
        );
    }

    // Get the index of where this row will be added
    const index = this.getPaymentFormArray().length;

    // Add listener for payment type changes
    const paymentTypeControl = itemRow.get('paymentType') as FormControl;
    paymentTypeControl.valueChanges.subscribe(() => {
      // Only for the first payment row
      if (index === 0 && !this.getEnableDifferentSums().value) {
        this.updateFirstPaymentAmount();
      }
    });

    // Add the payment row to the form array
    this.getPaymentFormArray().push(itemRow);

    // Set up amount change listener for non-first payment rows
    this.setupPaymentAmountListener(itemRow, index);
  }

  advanceColumnsNeeded() {
    return (
      this.getInvoiceType().value == 'Advance' &&
      this.getTransactionType().value == 'Finalize'
    );
  }

  removePaymentRow(index: number) {
    this.getPaymentFormArray().controls.splice(index, 1);

    // After removing a payment row, recalculate the first payment amount
    if (index > 0) {
      this.updateFirstPaymentAmount();
    }
  }

  getPaymentSum() {
    let sum: number = 0;
    this.getPaymentFormArray().controls.forEach((item) => {
      const pay = parseFloat(item.get('amount')?.value);
      if (!isNaN(pay)) sum += pay;
    });
    return sum;
  }

  getPayByAdvanceSum() {
    let sum: number = 0;
    this.getPaymentFormArray().controls.forEach((item) => {
      const payAdvance = parseFloat(item.get('paidByAdvance')?.value);
      if (!isNaN(payAdvance)) sum += payAdvance;
    });
    return sum;
  }

  getTotalSum() {
    return this.getPaymentSum() + this.getPayByAdvanceSum();
  }

  getTotalSumInRSD() {
    return this.getTotalSum() * this.getExchangeRateControl().value;
  }

  getTotalSumInRSDDisplay() {
    return this.getTotalSumInRSD().toFixed(2);
  }

  getEnableDifferentSums(): FormControl {
    return this.receiptForm.get('enableDifferentSums') as FormControl;
  }

  //base data------------------------------------
  getInvoiceType(): FormControl {
    return this.receiptForm.get('invoiceType') as FormControl;
  }

  getTransactionType(): FormControl {
    return this.receiptForm.get('transactionType') as FormControl;
  }

  getInvoiceNumberPos(): FormControl {
    return this.receiptForm.get('invoiceNumberPos') as FormControl;
  }

  getReferentDocumentNumber(): FormControl {
    return this.receiptForm.get('referentDocumentNumber') as FormControl;
  }

  getReferentDocumentDT(): FormControl {
    return this.receiptForm.get('referentDocumentDT') as FormControl;
  }

  //customer identifiation---------------------------
  getBuyerIDPrefix(): FormControl {
    return this.receiptForm.get('buyerIDPrefix') as FormControl;
  }

  getBuyerId(): FormControl {
    return this.receiptForm.get('buyerId') as FormControl;
  }

  //optionalIdentification----------------------------------------
  getPrefixBuyerCostCenterId(): FormControl {
    return this.receiptForm.get('prefixBuyerCostCenterId') as FormControl;
  }
  getSufixBuyerCostCenterId(): FormControl {
    return this.receiptForm.get('sufixBuyerCostCenterId') as FormControl;
  }

  private sumComparisonValidator: ValidatorFn = (): ValidationErrors | null => {
    if (this.getEnableDifferentSums().value) return null;
    const sumTotal = this.getTotalSum() - this.getItemsAmountSum();
    if (sumTotal != 0) return { sumComparisonValidator: true };

    return null;
  };

  // New method to update the first payment amount based on item total
  private isUpdatingFirstPayment = false;
  private updateFirstPaymentAmount() {
    // Prevent recursive calls
    if (this.isUpdatingFirstPayment) {
      return;
    }

    // Only auto-update if different sums not enabled
    if (this.getEnableDifferentSums().value) {
      return;
    }

    this.isUpdatingFirstPayment = true;
    try {
      // Get the first payment row if it exists
      if (this.getPaymentFormArray().length > 0) {
        const firstPaymentRow = this.getPaymentFormArray().at(0);
        const amountControl = firstPaymentRow.get('amount');

        // Only update if we have a selected payment type
        if (firstPaymentRow.get('paymentType')?.value) {
          const totalAmount = this.getItemsAmountSum();

          // Calculate total of all other payment methods (excluding the first one)
          let otherPaymentsTotal = 0;
          const paymentArray = this.getPaymentFormArray();

          for (let i = 1; i < paymentArray.length; i++) {
            const paymentAmount = paymentArray.at(i).get('amount')?.value;
            if (paymentAmount && !isNaN(Number(paymentAmount))) {
              otherPaymentsTotal += Number(paymentAmount);
            }
          }

          // Calculate remaining amount (total - other payments)
          const remainingAmount = Math.max(0, totalAmount - otherPaymentsTotal);

          // Update first payment method with remaining amount - prevent value change event
          amountControl?.setValue(remainingAmount.toString(), {emitEvent: false});
        }
      }
    } finally {
      this.isUpdatingFirstPayment = false;
    }
  }

  // Add a method to handle other payment row amount changes
  private setupPaymentAmountListener(paymentRow: FormGroup, index: number) {
    if (index > 0) { // Only for non-first payment rows
      const amountControl = paymentRow.get('amount');
      amountControl?.valueChanges.subscribe(() => {
        // When any other payment amount changes, update the first payment
        this.updateFirstPaymentAmount();
      });
    }
  }

  getCurrencyControl(): FormControl {
    return this.receiptForm.get('currency') as FormControl;
  }

  getCurrencyDateControl(): FormControl {
    return this.receiptForm.get('currencyDate') as FormControl;
  }

  getExchangeRateControl(): FormControl {
    return this.receiptForm.get('exchangeRate') as FormControl;
  }

  getExchangeRateDisplayControl(): FormControl {
    return this.receiptForm.get('exchangeRateDisplay') as FormControl;
  }

  updateExchangeRate() {
    const currency = this.getCurrencyControl().value;
    const currencyDate = this.getCurrencyDateControl().value;

    // Skip update if it's RSD (default currency)
    if (currency === 'RSD') {
      this.exchangeRate = null;
      this.getExchangeRateControl().setValue(1);
      this.getExchangeRateDisplayControl().setValue('1 RSD');
      // Update product prices when currency changes
      this.updateAllProductPrices();
      return;
    }

    if (currency && currencyDate) {
      const exchangeRateRequest = {
        currency: currency,
        date: currencyDate,
        amount: 1
      };

      this.userSettingsService.getExchangeRate(exchangeRateRequest).subscribe(
        (response) => {
          this.exchangeRate = response;
          this.getExchangeRateControl().setValue(response.convertedAmount);
          this.getExchangeRateDisplayControl().setValue(`1 ${currency} = ${response.convertedAmount} RSD`);
          // Update product prices when currency changes
          this.updateAllProductPrices();
        },
        (error) => {
          console.error('Error fetching exchange rate:', error);
          // Set default values if exchange rate fetch fails
          this.exchangeRate = null;
          this.getExchangeRateControl().setValue(1);
          this.getExchangeRateDisplayControl().setValue('1 RSD');
        }
      );
    }
  }

  // Helper method to update all product prices when currency changes
  private updateAllProductPrices() {
    // We don't need implementation here as it's handled in the component
    // This method is just a hook for the component to react to currency changes
    // console.log()
    // get all items value here
    this.getItemsFormArray().controls.forEach((item) => {
      const unitPrice = item.get('unitPrice')?.value;
      const quantity = item.get('quantity')?.value;
      const discount = item.get('discount')?.value;
      const totalAmount = unitPrice * quantity;
    });
  }
}
