import { Component, EventEmitter, OnInit, Output, ViewChild, ElementRef } from '@angular/core';
import * as XLSX from 'xlsx';
import { DialogService } from '../../../../shared/dialogs/dialog.service';
@Component({
  selector: 'app-products-header',
  templateUrl: './products-header.component.html',
  styleUrls: ['./products-header.component.scss']
})
export class ProductsHeaderComponent implements OnInit {

  @Output() actionEmitter: EventEmitter<any> = new EventEmitter();
  @ViewChild('fileInput') fileInput!: ElementRef;

  constructor(private dialogService: DialogService) { }

  ngOnInit(): void {
  }

  onFileSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e: any) => {
        const data = new Uint8Array(e.target.result);
        const workbook = XLSX.read(data, { type: 'array' });
        const firstSheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[firstSheetName];
        const jsonData = XLSX.utils.sheet_to_json(worksheet);

        // Emit the imported data
        this.actionEmitter.emit({
          action: 'import',
          data: jsonData
        });

        // Reset file input
        this.fileInput.nativeElement.value = '';
      };
      reader.readAsArrayBuffer(file);
    }
  }

  showImportTemplate(): void {
    this.dialogService.openImportTemplateDialog();
  }

}
