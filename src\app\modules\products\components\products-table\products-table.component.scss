.table-container {
  position: relative;
  margin: 20px;
  overflow: auto;
}

.bulk-actions {
  position: sticky;
  top: 0;
  z-index: 1;
  background: white;
  padding: 8px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
  display: flex;
  justify-content: flex-end;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.products-table {
  width: 100%;
}

.mat-column-select {
  width: 48px;
  text-align: center;
}

.mat-column-Akcija {
  width: 100px;
  text-align: center;
}

.element-row:hover {
  background-color: #f5f5f5;
}

mat-icon {
  cursor: pointer;
  color: #666;

  &:hover {
    color: #333;
  }
}

.mat-table {
  table-layout: fixed;
  width: 100%;
  min-width: 300px;
}

.mat-icon {
  cursor: pointer;

}

.mat-icon:hover {
  background-color:rgba(233, 150, 122, 0.623);
  border-radius: 50%;
}

.additional-currencies {
  margin-top: 4px;
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.currency-badge {
  display: inline-block;
  background-color: #f0f0f0;
  border-radius: 12px;
  padding: 2px 8px;
  font-size: 0.75rem;
  color: #555;
}
