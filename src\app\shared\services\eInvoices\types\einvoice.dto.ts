export interface InvoiceRequestDto {
  clientTo: ClientTo;
  prepaymentData?: PrepaymentDatum[];
  invoiceItems: InvoiceItem[];
  taxExemptions?: TaxExemption[];
  taxDiscounts?: TaxDiscount[];
  invoice: Invoice;
  bankAccount: BankAccount;
}

interface BankAccount {
  checkingAccountNumber: string;
}

interface Invoice {
  number: string;
  issueDate?: string;
  deliveryDate?: string;
  dueDate?: string;
  currency?: string;
  invoiceTypeCode: string;
  buyerReference?: string;
  orderReference?: string;
  invoicePeriodCode: string;
  contractDocumentReference?: string;
  originatorDocumentReference?: string;
  additionalDocumentReference?: string;
  paymentMeansCode: string;
  paymentModelCode: string;
  paymentModelNumber: string;
}

interface TaxDiscount {
  taxCategoryId: string;
  amount: number;
}

interface TaxExemption {
  taxCategoryId: string;
  taxExemptionReasonCode: string;
}

export interface InvoiceItem {
  quantity: number;
  item: Item;
  unitCode: string;
  totalAmount?: number;
}

export interface Item {
  basePrice: number;
  taxCategoryId: string;
  itemName: string;
}

interface PrepaymentDatum {
  prepaymentInvoiceNumber: string;
  taxInfos: TaxInfo[];
}

interface TaxInfo {
  taxableAmount: number;
  taxCategoryId: string;
}

interface ClientTo {
  city: string;
  address: string;
  countryIdentificationCode: string;
  postalCode: string;
  email: string;
  vatRegNum: string;
}
