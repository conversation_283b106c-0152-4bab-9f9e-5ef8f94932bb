<div
  class="container-fluid p-lg-5 p-md-3"
  *ngIf="metaFields$ | async as metaFields"
>
  <app-page-title
    *transloco="let t"
    text="{{ t('dashboard.receipts') }}"
  ></app-page-title>
  <div class="row">
    <app-receipts-header
      [refreshTime]="refreshTime"
      [isSearchChanged]="isSearchChanged"
      [receiptsCount]="receiptsCount"
      (actionEmitter)="handleActions($event)"
      (bulkActionEmitter)="handleBulkActions($event)"
    ></app-receipts-header>
  </div>
  <div class="row" *ngIf="userColumns$ | async as columns">
    <div class="col-lg-12" *ngIf="receipts != undefined; else load">
      <app-receipts-table
        [systemColumns]="columns"
        [data]="receipts"
        [metaFields]="metaFields"
        (sortEvent)="sortData($event)"
        (actionEmitter)="handleActions($event)"
      ></app-receipts-table>
    </div>
    <ng-template #load>
      <div class="col-lg-12">
        <app-table-skeleton
          [columns]="columns"
          [rowsNumber]="perPage"
        ></app-table-skeleton>
      </div>
    </ng-template>
    <div class="col-12">
      <app-fiscomm-paginator
        [startPage]="params.page || 1"
        [startPerPage]="params.perPage || 10"
        [chunkSize]="receipts == undefined ? 0 : receipts.length"
        [pageSizes]="[10, 15, 20]"
        (pageChange)="handlePageEvent($event)"
      ></app-fiscomm-paginator>
    </div>
  </div>
  <div class="row">
    <app-minimized-tabs></app-minimized-tabs>
  </div>
</div>
