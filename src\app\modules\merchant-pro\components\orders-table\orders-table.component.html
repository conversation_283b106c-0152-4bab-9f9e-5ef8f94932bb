<div class="table-container" *ngIf="displayColumns && data">
  <table
    mat-table
    *transloco="let t"
    [dataSource]="data"
    multiTemplateDataRows
    class="mat-elevation-z8 orders-table"
  >
    <ng-container matColumnDef="Checkbox">
      <th mat-header-cell *matHeaderCellDef>
        <input type="checkbox" (change)="checkUncheckAll()" />
      </th>
      <td mat-cell *matCellDef="let element">
        <input
          type="checkbox"
          class="checkbox"
          [checked]="getCheckbox(element.id)"
          (change)="toggleElement(element)"
        />
      </td>
    </ng-container>
    <ng-container matColumnDef="orderID">
      <th mat-header-cell *matHeaderCellDef>
        {{ t("orders_page.order_number") }}
      </th>
      <td mat-cell *matCellDef="let element">{{ element.id }}</td>
    </ng-container>
    <ng-container matColumnDef="customerName">
      <th mat-header-cell *matHeaderCellDef>{{ t("customer") }}</th>
      <td mat-cell *matCellDef="let element">{{ element.shipping_name }}</td>
    </ng-container>
    <ng-container matColumnDef="customerEmail">
      <th mat-header-cell *matHeaderCellDef>Email</th>
      <td mat-cell *matCellDef="let element">{{ element.customer_email }}</td>
    </ng-container>
    <ng-container matColumnDef="dateCreated">
      <th mat-header-cell *matHeaderCellDef>{{ t("time_created") }}</th>
      <td mat-cell *matCellDef="let element">
        {{ element.payment_date | fiscommDate }}
      </td>
    </ng-container>
    <ng-container matColumnDef="amount">
      <th mat-header-cell *matHeaderCellDef>{{ t("amount") }}</th>
      <td mat-cell *matCellDef="let element">
        {{ element.total_amount | rsdCurrency : "RSD " : 2 : "." : "," : 3 }}
      </td>
    </ng-container>
    <ng-container matColumnDef="mpSubstatus">
      <th mat-header-cell *matHeaderCellDef>{{ t("fiscalization") }}</th>
      <td mat-cell *matCellDef="let element; let i; as: index">
        <app-primary-btn
          [tooltip]="fiscMessage.get(element.id) || ''"
          [text]="t('menu.fiscalize')"
          (click)="emitAction({ action: 'fiscalize', data: element })"
          [disabled]="!allowFiscalization(element)"
        ></app-primary-btn>
      </td>
    </ng-container>
    <ng-container matColumnDef="invoicePdfUrl">
      <th mat-header-cell *matHeaderCellDef>Pdf Link</th>
      <td mat-cell *matCellDef="let element">
        <a *ngIf="getOrderPdfLink(element) as pdfLink" href="{{ pdfLink }}"
          >Link</a
        >
      </td>
    </ng-container>
    <ng-container matColumnDef="verificationURL">
      <th mat-header-cell *matHeaderCellDef>Link</th>
      <td mat-cell *matCellDef="let element">
        <a *ngIf="getOrderVerLink(element) as verLink" href="{{ verLink }}"
          >Link</a
        >
      </td>
    </ng-container>

    <ng-container matColumnDef="Akcije" stickyEnd>
      <th mat-header-cell *matHeaderCellDef>{{ t("actions") }}</th>
      <td mat-cell *matCellDef="let element">
        <mat-icon [matMenuTriggerFor]="menu">more_vert</mat-icon>
        <mat-menu #menu="matMenu">
          <button
            mat-menu-item
            *ngIf="
              !element.payment_substatus_text && allowFiscalization(element)
            "
            (click)="emitAction({ action: 'Fiscalize', data: element })"
          >
            {{ t("menu.fiscalize") }}
          </button>
          <button
            *ngIf="isElementFiscalised(element)"
            mat-menu-item
            (click)="emitAction({ action: 'Refund', data: element })"
          >
            {{ t("menu.refund") }}
          </button>
        </mat-menu>
      </td>
    </ng-container>

    <ng-container matColumnDef="Expand" stickyEnd>
      <th
        mat-header-cell
        *matHeaderCellDef
        aria-label="row actions"
        class="text-center"
      >
        <mat-icon [matMenuTriggerFor]="menu" class="icon">settings</mat-icon>
        <mat-menu #menu="matMenu" xPosition="before">
          <ng-container *ngFor="let col of allColumns">
            <button
              mat-button
              (click)="$event.stopPropagation(); toggleColumn(col)"
            >
              <input type="checkbox" [checked]="col.enabled" />
              <label>{{ col.displayName }}</label>
            </button>
          </ng-container>
        </mat-menu>
      </th>
      <td mat-cell *matCellDef="let element">
        <button
          class="d-flex justify-content-center"
          mat-icon-button
          aria-label="expand row"
          (click)="
            expandedElement = expandedElement === element ? null : element;
            $event.stopPropagation()
          "
        >
          <mat-icon *ngIf="expandedElement !== element"
            >keyboard_arrow_down</mat-icon
          >
          <mat-icon *ngIf="expandedElement === element"
            >keyboard_arrow_up</mat-icon
          >
        </button>
      </td>
    </ng-container>

    <!-- expandable content -->
    <ng-container matColumnDef="expandedDetail">
      <td
        mat-cell
        *matCellDef="let element"
        [attr.colspan]="displayColumns.length"
      >
        <div
          class="element-detail"
          [@detailExpand]="
            element == expandedElement ? 'expanded' : 'collapsed'
          "
        >
          <app-row-expand-details [order]="element"></app-row-expand-details>
        </div>
      </td>
    </ng-container>

    <tr mat-header-row *matHeaderRowDef="displayColumns"></tr>
    <tr
      mat-row
      *matRowDef="let element; columns: displayColumns"
      class="element-row"
      [class.expanded-row]="expandedElement === element"
    ></tr>
    <tr
      mat-row
      *matRowDef="let row; columns: ['expandedDetail']"
      class="detail-row"
    ></tr>
  </table>
</div>
