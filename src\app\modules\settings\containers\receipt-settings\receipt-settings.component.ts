import { Component, OnInit } from '@angular/core';
import { AngularFireAuth } from '@angular/fire/compat/auth';
import { FormControl } from '@angular/forms';
import { DomSanitizer, SafeUrl } from '@angular/platform-browser';
import { take } from 'rxjs';
import { AuthService } from 'src/app/core/services/auth.service';
import { ClientService } from 'src/app/shared/services/backend/clients/client.service';
import { ReceiptsService } from 'src/app/shared/services/backend/receipts/receipts.service';
import { CommonFirestoreService } from 'src/app/shared/services/firestore/common-firestore.service';

@Component({
  selector: 'app-receipt-settings',
  templateUrl: './receipt-settings.component.html',
  styleUrls: ['./receipt-settings.component.scss'],
})
export class ReceiptSettingsComponent implements OnInit {
  receiptsInfo: {
    width: number | null;
    underReceipt: string;
    testMode: boolean;
    orderNumUnderReceipt: boolean;
  } = {
    width: 80,
    underReceipt: '',
    testMode: false,
    orderNumUnderReceipt: false,
  };

  imageControl: FormControl = new FormControl();
  fileSource: FormControl = new FormControl();
  urlPreview: any;
  pdfUrl: SafeUrl = this.sanitazer.bypassSecurityTrustResourceUrl('');

  uid: string = '';
  clientData?: any;

  constructor(
    private clientService: ClientService,
    private receiptService: ReceiptsService,
    private sanitazer: DomSanitizer,
    private commonFirestoreService: CommonFirestoreService,
    private authService: AuthService
  ) {}

  ngOnInit(): void {
    this.authService
      .getUserId()
      .pipe(take(1))
      .subscribe((uid: any) => {
        this.uid = uid;
        this.loadSettings();
      });
  }

  onImageChange(event: Event) {
    const inputElement = event.target as HTMLInputElement;
    if (inputElement && inputElement.files) {
      const file = inputElement.files[0];
      this.fileSource.setValue(file);
      this.urlPreview = this.sanitazer.bypassSecurityTrustResourceUrl(
        URL.createObjectURL(file)
      );
    }
  }

  private loadSettings() {
    this.commonFirestoreService
      .listenToSettingsChange$(this.uid)
      .pipe(take(1))
      .subscribe(() => this.getUserData());
  }

  private getUserData() {
    this.clientService.getClient(this.uid).subscribe((client) => {
      this.initalizeReceiptsSettings(client);
      this.clientData = client;
    });
  }

  resetImageInput() {
    this.imageControl.setValue(null);
  }

  private initalizeReceiptsSettings(data: any) {
    this.receiptsInfo = {
      width: data?.storage?.widthMM || 80,
      underReceipt: data?.receipts?.underReceipt || '',
      testMode: data?.receipts?.testMode || false,
      orderNumUnderReceipt: data?.receipts?.orderNumUnderReceipt || false,
    };
  }

  formatLabel(value: number): string {
    return `${value}`;
  }

  toggleEmail() {
    this.clientData.receipts.isEmailActive =
      !this.clientData.receipts.isEmailActive;
  }

  toggleTestMode() {
    this.receiptsInfo.testMode = !this.receiptsInfo.testMode;
  }
  toggleOrderNumUnderReceipt() {
    this.receiptsInfo.orderNumUnderReceipt =
      !this.receiptsInfo.orderNumUnderReceipt;
  }
  underReceiptChanged(e: any) {
    this.receiptsInfo.underReceipt = e.target.value;
  }

  saveReceiptsSettings() {
    if (this.clientData) {
      this.clientData.storage = {
        ...this.clientData?.storage,
        widthMM: this.receiptsInfo.width as number,
      };
      this.clientData.receipts = {
        ...this.clientData?.receipts,
        underReceipt: this.receiptsInfo.underReceipt,
        testMode: this.receiptsInfo.testMode,
        orderNumUnderReceipt: this.receiptsInfo.orderNumUnderReceipt,
      };
      this.clientService
        .updateClient(
          this.uid,
          this.clientData,
          'Vaša podešavanja su uspešno sačuvana.'
        )
        .subscribe();
    }
  }
}
