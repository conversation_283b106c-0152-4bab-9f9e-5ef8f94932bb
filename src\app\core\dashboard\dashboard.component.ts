import { BreakpointObserver } from '@angular/cdk/layout';
import { Component, HostListener, OnInit } from '@angular/core';
import { Auth } from '@angular/fire/auth';
import { AngularFirestore } from '@angular/fire/compat/firestore';
import { Router } from '@angular/router';
import { signOut } from 'firebase/auth';
import { fromEvent, take } from 'rxjs';
import { MatDialog } from '@angular/material/dialog';
import { CreateReceiptDialogComponent } from 'src/app/modules/receipts/components/create-receipt-dialog/create-receipt-dialog.component';

import { AuthService } from '../services/auth.service';
import { FormControl } from '@angular/forms';
import { TranslocoService } from '@ngneat/transloco';
import { CommonFirestoreService } from 'src/app/shared/services/firestore/common-firestore.service';
import { AngularFireAuth } from '@angular/fire/compat/auth';
import { TicketService } from 'src/app/shared/services/backend/tickets/ticket.service';
import { ClientService } from 'src/app/shared/services/backend/clients/client.service';

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss'],
})
export class DashboardComponent implements OnInit {
  title = 'fiscomm-e-invoices-client';
  isMobile = false;
  sideNavOpened = true;
  userLoggedIn = false;
  currentUser: any;
  sidenavWidth = 15;

  languageControl: FormControl = new FormControl();
  activeLang: string = '';
  availableLangs: any[] = [];

  languages = [
    { label: 'Srpski', value: 'sr' },
    { label: 'English', value: 'en' },
  ];

  userInfo: any;
  ticketCount: number = 0;

  constructor(
    breakpointObserver: BreakpointObserver,
    private firestore: AngularFirestore,
    private _auth: AngularFireAuth,
    private commonFirestoreService: CommonFirestoreService,
    private router: Router,
    private authService: AuthService,
    private translocoService: TranslocoService,
    private ticketService: TicketService,
    private clientService: ClientService,
    private dialog: MatDialog
  ) {
    breakpointObserver.observe(['(max-width: 800px)']).subscribe((result) => {
      if (result.matches) {
        this.isMobile = true;
      } else {
        this.isMobile = false;
      }
    });
  }

  ngOnInit(): void {
    this.loadSettings();
    this.languageControl.valueChanges.subscribe(() => this.toggleLanguage());
    this.getTicketCount();
    this.authService
      .getUserId()
      .subscribe((id) => this.clientService.setUid(id || ''));
  }

  private getTicketCount() {
    this.ticketService
      .getAnsweredTicketCount()
      .subscribe((response: any) => (this.ticketCount = response.count));
  }

  toggleLanguage() {
    this.translocoService.setActiveLang(this.languageControl.value);
    this.activeLang = this.languageControl.value;
    this.saveLanguageSettings(this.languageControl.value);
  }

  setLanguageMenu(value: any) {
    this.languageControl.setValue(value);
    this.toggleLanguage();
  }

  increase() {
    this.sidenavWidth = 15;
  }
  decrease() {
    this.sidenavWidth = 8;
  }

  private loadSettings() {
    this._auth.onAuthStateChanged((user) => {
      if (user) {
        this.commonFirestoreService
          .listenUserData$(user.uid)
          .pipe(take(1))
          .subscribe((data: any) => {
            const dataTmp = data.payload.data() as any;
            this.initalizeUserInfo(data);
            if (!this.userInfo.lang)
              this.activeLang = this.translocoService.getActiveLang();
            else this.activeLang = this.userInfo.lang;
            this.languageControl.setValue(this.activeLang);
          });
      }
    });
  }

  private initalizeUserInfo(data: any) {
    this.userInfo = {
      ...data.payload.data(),
      id: data.payload.id,
    };
    this.currentUser = this.userInfo;
  }

  private saveLanguageSettings(lang: any) {
    this.commonFirestoreService.saveUserData(this.userInfo.id, {
      ...this.userInfo,
      lang: lang,
    });
  }

  toggleSidenav(type?: string) {
    if (type == 'navigation' && !this.isMobile) return;

    if (this.isMobile && type == 'navigation') {
      this.sideNavOpened = !this.sideNavOpened;
      return;
    }
    if (this.isMobile) {
      this.sideNavOpened = !this.sideNavOpened;
      return;
    }

    if (this.sidenavWidth === 8) {
      this.increase();
    } else {
      this.decrease();
    }
  }

  openFiscommLanding() {
    window.open('https://www.fiscomm.rs', '_blank');
  }
  async signOut() {
    await this.authService
      .signOut()
      .then(() => this.router.navigateByUrl('/auth/login'));
  }

  toggleNavigation() {
    this.sideNavOpened = !this.sideNavOpened;
  }

  @HostListener('scroll', ['$event'])
  scrollIt(event: any) {}

  openCreateReceiptDialog() {
    this.dialog.open(CreateReceiptDialogComponent, {
      width: '80%',
      maxWidth: '1200px',
      height: '90%',
      maxHeight: '800px',
      panelClass: 'custom-dialog-container'
    });
  }
}
