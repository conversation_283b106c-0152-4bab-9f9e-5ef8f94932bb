<mat-tab-group *transloco="let t">
  <mat-tab label="{{ t('history') }}" class="tab">
    <table
      mat-table
      *ngIf="receiptsHistory && receiptsHistory.length > 0; else noHistory"
      [dataSource]="receiptsHistory"
      multiTemplateDataRows
      class="table"
    >
      <ng-container matColumnDef="Broj računa">
        <th mat-header-cell *matHeaderCellDef>
          {{ t("receipts.invoice_number") }}
        </th>
        <td mat-cell *matCellDef="let element">{{ element.invoice_number }}</td>
      </ng-container>
      <ng-container matColumnDef="Tip plaćanja">
        <th mat-header-cell *matHeaderCellDef>{{ t("receipts.payment") }}</th>
        <td mat-cell *matCellDef="let element">
          {{
            activeLang == "sr"
              ? (element.payments[0].payment_type.friendly_name
                | cyrillicToLatin)
              : element.payments[0].payment_type.payment_type
          }}
        </td>
      </ng-container>
      <ng-container matColumnDef="Tip računa">
        <th mat-header-cell *matHeaderCellDef>
          {{ t("receipts.invoice_type") }}
        </th>
        <td mat-cell *matCellDef="let element">
          {{
            activeLang == "sr"
              ? (element.invoice_type.friendly_name | cyrillicToLatin)
              : element.invoice_type.invoice_type
          }}
        </td>
      </ng-container>
      <ng-container matColumnDef="Tip transakcije">
        <th mat-header-cell *matHeaderCellDef>
          {{ t("receipts.transaction_type") }}
        </th>
        <td mat-cell *matCellDef="let element">
          {{
            activeLang == "sr"
              ? (element.transaction_type.friendly_name | cyrillicToLatin)
              : element.transaction_type.transaction_type
          }}
        </td>
      </ng-container>
      <ng-container matColumnDef="Žurnal">
        <th mat-header-cell *matHeaderCellDef>
          {{ t("receipts.invoice_pdf_url") }}
        </th>
        <td mat-cell *matCellDef="let element">
          <a href="{{ element.invoice_pdf_url }}">Link</a>
        </td>
      </ng-container>
      <ng-container matColumnDef="Vreme fisk.">
        <th mat-header-cell *matHeaderCellDef>
          {{ t("receipts.sdc_date_time") }}
        </th>
        <td mat-cell *matCellDef="let element">
          {{ element.sdc_date_time | date }}
        </td>
      </ng-container>
      <ng-container matColumnDef="Ukupno">
        <th mat-header-cell *matHeaderCellDef>
          {{ t("receipts.total_amount") }}
        </th>
        <td mat-cell *matCellDef="let element">
          {{ element.total_amount | rsdCurrency : "RSD " : 2 : "." : "," : 3 }}
        </td>
      </ng-container>

      <tr mat-header-row *matHeaderRowDef="historyColumns"></tr>
      <tr mat-row *matRowDef="let element; columns: historyColumns"></tr>
    </table>
    <ng-template #noHistory>
      <p>Nema istoriju</p>
    </ng-template>
  </mat-tab>
  <mat-tab label="{{ t('items_menu') }}" class="tab">
    <table class="table">
      <thead>
        <th>{{ t("name") }}</th>
        <th>{{ t("amounts") }}</th>
        <th>{{ t("price") }}</th>
        <th>{{ t("total_price") }}</th>
      </thead>
      <tbody>
        <tr *ngFor="let el of items">
          <td>{{ el.name }}</td>
          <td>{{ el.quantity }}</td>
          <td>{{ el.unitPrice | rsdCurrency : "RSD " : 2 : "." : "," : 3 }}</td>
          <td>
            {{ el.totalAmount | rsdCurrency : "RSD " : 2 : "." : "," : 3 }}
          </td>
        </tr>
      </tbody>
    </table>
  </mat-tab>
</mat-tab-group>
