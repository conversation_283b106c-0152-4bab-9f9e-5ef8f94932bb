import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { AngularFireAuth } from '@angular/fire/compat/auth';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';

export interface ExchangeRateRequest {
  currency: string;
  amount: number;
  date: string;
}

export interface ExchangeRateResponse {
  fromCurrency: string;
  toCurrency: string;
  amount: number;
  convertedAmount: string;
  date: string;
}
@Injectable({
  providedIn: 'root',
})
export class UserSettingsService {
  baseUrl = environment.mainSelfcare.baseUrl;
  headers = new HttpHeaders().set('Fiscomm-No-Loading', 'true');

  private allColumnsData: TableColumn[] = [
    { key: 'invoice_number', displayName: 'Broj računa', enabled: true },
    {
      key: 'invoice_number_pos',
      displayName: '<PERSON><PERSON><PERSON> porudžbine',
      enabled: true,
    },
    { key: 'payment', displayName: 'Tip plaćanja', enabled: true },
    { key: 'invoice_type', displayName: 'Tip računa', enabled: true },
    { key: 'transaction_type', displayName: 'Tip transakcije', enabled: false },
    { key: 'invoice_pdf_url', displayName: 'Žurnal', enabled: true },
    { key: 'sdc_date_time', displayName: 'Vreme fisk.', enabled: false },
    { key: 'total_amount', displayName: 'Ukupno', enabled: true },
    { key: 'finalized', displayName: 'Finalizovan', enabled: false },
    { key: 'cashier', displayName: 'Kasir', enabled: false },
  ];

  constructor(private auth: AngularFireAuth, private http: HttpClient) {
    auth.onAuthStateChanged(async (user) => {
      this.headers.append('Authorization', `Bearer ${user?.getIdToken()}`);
    });
  }
  updateColumnSettings(columns: string[]) {
    return this.http.post(
      `${this.baseUrl}/settings/receipts/columns`,
      columns,
      { headers: this.headers }
    );
  }
  getColumnSettings(): Observable<string[]> {
    return this.http.get<string[]>(
      `${this.baseUrl}/settings/receipts/columns`,
      { headers: this.headers }
    );
  }

  getAllColumnsData() {
    return Array.from(this.allColumnsData);
  }


  getExchangeRate(data: ExchangeRateRequest): Observable<ExchangeRateResponse> {
    return this.http.post<ExchangeRateResponse>(this.baseUrl + '/exchange/rate', data);
  }
}
export interface TableColumn {
  key: string;
  displayName: string;
  enabled: boolean;
  metaField?: boolean;
}
