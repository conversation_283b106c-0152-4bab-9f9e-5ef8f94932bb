import { ChangeDetectorRef, Component, Input, NgZone, OnInit } from '@angular/core';
import { TooltipPosition } from '@angular/material/tooltip';

@Component({
  selector: 'app-fiscomm-tooltip',
  templateUrl: './fiscomm-tooltip.component.html',
  styleUrls: ['./fiscomm-tooltip.component.scss']
})
export class FiscommTooltipComponent implements OnInit {

  constructor() { }

  @Input() text: string = '';
  @Input() position:TooltipPosition='above';

  ngOnInit(): void {
  }


}
