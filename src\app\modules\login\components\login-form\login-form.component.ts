import { Component, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { AuthService } from 'src/app/core/services/auth.service';
import { DialogService } from 'src/app/shared/dialogs/dialog.service';

@Component({
  selector: 'app-login-form',
  templateUrl: './login-form.component.html',
  styleUrls: ['./login-form.component.scss']
})
export class LoginFormComponent implements OnInit {

  loginForm: FormGroup;
  adminToken: string | null = null;
  constructor(private router: Router,
    private route: ActivatedRoute,
    private authService: AuthService,
    private dialogService: DialogService) {
    this.loginForm = new FormGroup({
      email: new FormControl('', [Validators.required]),
      password: new FormControl('', [Validators.required]),
    });

    const queryParams = this.route.snapshot.queryParams;
    if (queryParams['adminToken']) {
      this.adminToken = queryParams['adminToken'];
      this.authService.signInWithCustomToken(this.adminToken)
        .then((res) => {
          const redirectTo = this.extractRedirect(window.location.href);
          if (redirectTo)
            this.router.navigateByUrl(redirectTo);
          else
            this.router.navigateByUrl('/pocetna');
        })
        .catch((err) => alert('Ne postoji korisnik sa ovom kombinacijom email/lozinka'));
    }

  }

  ngOnInit(): void {
  }

  async login() {
    this.authService.signIn(this.loginForm?.controls["email"].value, this.loginForm?.controls["password"].value)
      .then((res) => {
        const redirectTo = this.extractRedirect(window.location.href);
        if (redirectTo)
          this.router.navigateByUrl(redirectTo);
        else
          this.router.navigateByUrl('/pocetna');
      })
      .catch((err) => alert('Ne postoji korisnik sa ovom kombinacijom email/lozinka'));
  }

  getEmailControl(): FormControl {
    return this.loginForm.get('email') as FormControl;
  }

  getPasswordControl(): FormControl {
    return this.loginForm.get('password') as FormControl;
  }

  openResetDialog() {
    this.dialogService.openResetPasswordDialog({ width: '30%', height: '30%' });
  }

  private extractRedirect(url: string) {
    const urlObj = new URL(url);
    const searchParams = new URLSearchParams(urlObj.search);
    return searchParams.get('redirect_to');
  }

}
