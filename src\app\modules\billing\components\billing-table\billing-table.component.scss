.table-container {
  width: 100%;
  overflow-y: auto;
  overflow-x: auto; /* Add horizontal scrolling */
}

.billing-table {
  margin: 0px !important;
  // tr:nth-last-of-type(even){
  //   background-color: #EDF5FD;
  // }
  th {
    font-family: "Inter" !important;
    font-weight: 600;
    padding-left: 20px;
    font-size: 14px;
    color: #022b3a;
    border-top: 3px solid #e1e5f2;
    border-bottom: 3px solid #e1e5f2;
    width: 250px !important;
    text-wrap: nowrap !important;
  }
  td {
    padding-left: 20px;
    font-size: 13px;
    color: #022b3a;
    border-bottom: 1px solid #e1e5f2;
    width: 250px !important;
    text-wrap: nowrap !important;
  }
  .icons {
    display: flex;
    height: 100%;
    cursor: pointer;
  }
}

tr.detail-row {
  height: 0;
  background-color: #edf5fd;
}

tr.element-row:not(.expanded-row):hover {
  background: #edf5fd;
}

.expanded-row {
  background-color: #edf5fd;
}

tr.element-row:not(.expanded-row):active {
  background: #efefef;
}

.element-row td {
  border-bottom-width: 0;
}

.element-detail {
  overflow: hidden;
  width: 100%;
}

.mat-table {
  table-layout: fixed;
  min-width: 300px;
}

.mat-header-cell.stickyEnd {
  position: sticky;
  left: 0;
  z-index: 1;
  background-color: red;
}

.icon {
  cursor: pointer;
  font-size: 20px;
  margin-top: 9px;
}

.mat-icon {
  cursor: pointer;
}

button {
  width: 100%;
  display: block;
  text-align: left;
}

::ng-deep.mat-menu-panel {
  max-height: 200px !important;
}
