export interface Product {
  _id?: string;
  name: string;
  price: number; // Main price (default currency)
  gtin?: string;
  productCode?: string;
  createdAt?: Date;
  prices?: { [currencyCode: string]: number }; // Additional prices in different currencies
  taxLabel?: { label: string, rate: number }; // Default tax label for the product
}

export interface ProductPrice {
  currencyCode: string;
  price: number;
}
