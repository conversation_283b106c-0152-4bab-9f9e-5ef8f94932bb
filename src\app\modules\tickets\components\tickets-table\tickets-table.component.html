<div class="table-container" *ngIf="displayColumns">
  <table
    mat-table
    [dataSource]="data"
    multiTemplateDataRows
    *transloco="let t"
    class="tickets-table"
  >
    <ng-container matColumnDef="id">
      <th mat-header-cell *matHeaderCellDef>Ticket id</th>
      <td mat-cell *matCellDef="let element">{{ element.id }}</td>
    </ng-container>
    <ng-container matColumnDef="naslov">
      <th mat-header-cell *matHeaderCellDef>Naslov</th>
      <td mat-cell *matCellDef="let element">{{ element.title }}</td>
    </ng-container>
    <ng-container matColumnDef="prioritet">
      <th mat-header-cell *matHeaderCellDef>Prioritet</th>
      <td mat-cell *matCellDef="let element">
        <app-pill
          color="{{ getPriorityColor(element.priority) }}"
          text="{{ element.priority_name.sr }}"
        ></app-pill>
      </td>
    </ng-container>
    <ng-container matColumnDef="status">
      <th mat-header-cell *matHeaderCellDef>Status</th>
      <td mat-cell *matCellDef="let element">
        <app-pill
          color="{{ getStatusColor(element.status) }}"
          text="{{ element.status_name.sr }}"
        ></app-pill>
      </td>
    </ng-container>
    <ng-container matColumnDef="odgovoreno">
      <th mat-header-cell *matHeaderCellDef>Odgovoreno</th>
      <td mat-cell *matCellDef="let element">
        {{ element.updated_at | fiscommDate }}
      </td>
    </ng-container>

    <ng-container matColumnDef="Details" stickyEnd>
      <th class="sticky" mat-header-cell *matHeaderCellDef></th>
      <td class="sticky" mat-cell *matCellDef="let element">
        <mat-icon style="color: #008bff" (click)="openDetailsDialog(element)"
          >open_in_new</mat-icon
        >
      </td>
    </ng-container>

    <tr mat-header-row *matHeaderRowDef="displayColumns"></tr>
    <tr
      (dblclick)="openDetailsDialog(element)"
      mat-row
      *matRowDef="let element; columns: displayColumns"
      [ngClass]="element.status == 'inactive' ? 'inactive' : ''"
    ></tr>
  </table>
</div>
