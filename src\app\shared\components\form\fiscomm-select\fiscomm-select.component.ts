import { Component, Input, OnInit } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MatFormFieldAppearance } from '@angular/material/form-field';

@Component({
  selector: 'app-fiscomm-select',
  templateUrl: './fiscomm-select.component.html',
  styleUrls: ['./fiscomm-select.component.scss']
})
export class FiscommSelectComponent implements OnInit {

  @Input() placeholder : string = "";
  @Input() tooltip : string = "";
  @Input() disabled: boolean = false;
  @Input() options: SelectOption[] = [];
  @Input() control?: FormControl;
  @Input() appearance: MatFormFieldAppearance = "outline";
  @Input() enableNone: boolean = false;
  @Input() compareWith!: (o1: any, o2: any) => boolean;

  constructor() { }

  ngOnInit(): void {
  }

}

type SelectOption = {
  disabled?: boolean;
  value: string;
  label: string;
  tooltip?: string;
};