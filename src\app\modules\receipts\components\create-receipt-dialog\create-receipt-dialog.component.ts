import {
  Component,
  OnInit,
  Inject,
  EventEmitter,
  Output,
  HostListener,
  ElementRef,
  ChangeDetectionStrategy,
  Input,
} from '@angular/core';
import { FormCreateReceiptService } from '../../services/form-create-receipt.service';
import { FormGroup, FormArray, FormControl, AbstractControl } from '@angular/forms';
import { SelectTypesDataService } from 'src/app/shared/services/select-types-data.service';
import { MetaFieldsDto } from 'src/app/shared/services/backend/receipts/types/receipts-response.dto';
import { ReceiptsService } from 'src/app/shared/services/backend/receipts/receipts.service';
import { Observable, forkJoin, map, startWith, take, tap } from 'rxjs';
import { MAT_DIALOG_DATA, MatDialogRef, MatDialog } from '@angular/material/dialog';
import { translate } from '@ngneat/transloco';
import { MinimizeService } from 'src/app/shared/services/minimize.service';
import { FiscommUtils } from 'src/app/shared/utils/fiscomm-utils';
import { AuthService } from 'src/app/core/services/auth.service';
import { ClientService } from 'src/app/shared/services/backend/clients/client.service';
import { CustomFieldsAdapterService } from '../../services/custom-fields-adapter.service';
import { MetaFieldUsageService } from 'src/app/shared/services/firestore/meta-field-usage.service';

@Component({
  selector: 'app-create-receipt-dialog',
  templateUrl: './create-receipt-dialog.component.html',
  styleUrls: ['./create-receipt-dialog.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CreateReceiptDialogComponent implements OnInit {
  receiptsForm: FormGroup = new FormGroup({});
  metaFields$: Observable<MetaFieldsDto[]> = new Observable();
  today: Date = new Date();
  metaFieldMap: Map<string, MetaFieldsDto> = new Map();

  loadingReferentDocNumber: boolean = false;
  outsideCheckbox: FormControl = new FormControl();
  openReceiptTab: FormControl = new FormControl(true);
  showErrors: boolean = false;

  currencies: { value: string; label: string }[] = [];

  buyerDocTypes: any;
  buyerCostTypes: any;

  constructor(
    public formService: FormCreateReceiptService,
    public selectDataService: SelectTypesDataService,
    private receiptsService: ReceiptsService,
    private createFormService: FormCreateReceiptService,
    public dialogRef: MatDialogRef<CreateReceiptDialogComponent>,
    private minimizeService: MinimizeService,
    private customFieldsAdapter: CustomFieldsAdapterService,
    private metaFieldUsageService: MetaFieldUsageService,
    private dialog: MatDialog,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {}

  ngOnInit(): void {
    this.receiptsForm = this.formService.getReceiptForm();

    // Use our custom fields service instead of the backend service
    this.metaFields$ = this.customFieldsAdapter.getMetaFields();

    this.metaFields$.subscribe((fields) => {
      this.metaFieldMap.clear();
      fields.forEach((field) => {
        this.createFormService.addMetaFieldControl(field.key, field.isRequired);
        this.metaFieldMap.set(field.key, field);
      });
    });

    if (this.data) {
      this.formService.loadFormData(this.data);
    }

    this.dialogRef.backdropClick().subscribe(() => {
      this.minimize();
    });
    this.createFormService.loadingRefDoc.subscribe((loading) => {
      this.loadingReferentDocNumber = loading;
    });
    this.outsideCheckbox.valueChanges.subscribe((value) => {
      this.createFormService.badRequestAllowed = value;
      this.createFormService.updateRefDoc();
    });

    this.formService.getUserDataAndSetDefualtSettings();

    this.initSelectTypesWithValue();
  }

  private initSelectTypesWithValue() {
    this.buyerDocTypes = this.selectDataService
      .getBuyerIdDocumentTypes()
      .map((doc: any) => {
        return { label: doc.value + ': ' + doc.label, value: doc.value };
      });

    this.buyerCostTypes = this.selectDataService
      .getBuyerCostCenterIdTypes()
      .map((doc: any) => {
        return { label: doc.value + ': ' + doc.label, value: doc.value };
      });

    this.currencies = this.selectDataService.getBuyerIdDocumentTypes();
  }

  minimize() {
    if (this.data?.minimizedId != undefined) {
      this.minimizeService.minimize(
        this.createModalName(),
        'createReceipt',
        {
          ...this.createFormService.getReceiptForm().value,
          mainTaxLabel: this.createFormService.getMainTax().value,
        },
        this.data.minimizedId
      );
    } else if (this.createFormService.getReceiptForm().dirty) {
      this.minimizeService.minimize(this.createModalName(), 'createReceipt', {
        ...this.createFormService.getReceiptForm().value,
        mainTaxLabel: this.createFormService.getMainTax().value,
      });
    }
    this.createFormService.resetForm();
  }

  private createModalName() {
    let data = this.createFormService.getReceiptForm().value;
    if (data.invoiceNumberPos) return data.invoiceNumberPos;
    if (data.invoiceType && data.transactionType)
      return `${translate('invoice_types.' + data.invoiceType)} - ${translate(
        'transaction_types.' + data.transactionType
      )}`;
    if (data.invoiceType) return translate('invoice_types.' + data.invoiceType);
    if (data.transactionType)
      return translate('transaction_types.' + data.transactionType);

    return 'Dodaj nov racun';
  }

  createReceipt() {
    this.showErrors = true;
    if (!this.receiptsForm.valid) {
      this.formService.markFormGroupTouched(this.receiptsForm);
    } else {
      this.submitData();
    }
  }

  private submitData() {
    const submitValue = this.createFormService.getPreparedReceiptData();
    this.receiptsService
      .createReceipt(submitValue)
      .subscribe((addedReceipt: any) => {
        // Update meta field usage and search values
        this.updateMetaFieldUsage(submitValue.metaFields, addedReceipt.body);

        this.dialogRef.close(
          this.prepareCreatedReceiptData(submitValue, addedReceipt.body)
        );
      });
  }

  /**
   * Update meta field usage tracking and search values when a receipt is created
   */
  private updateMetaFieldUsage(metaFields: any[], _receiptInfo: any) {
    if (!metaFields || !metaFields.length) return;

    // Process each meta field
    const updateObservables = metaFields
      .filter(field => field.key && field.value)
      .map(field => {
        // Get receipt information from the response
        const receiptInfo = {
          invoiceNumber: _receiptInfo.invoiceNumber || '',
          sdcDateTime: _receiptInfo.sdcDateTime || new Date().toISOString(),
          invoicePdfUrl: _receiptInfo.invoicePdfUrl || '',
          verificationUrl: _receiptInfo.verificationUrl || '',
          createdAt: new Date().toISOString(),
        };

        return this.metaFieldUsageService.updateFieldUsage(field.key, field.value, receiptInfo);
      });

    // Execute all updates
    if (updateObservables.length > 0) {
      forkJoin(updateObservables).pipe(take(1)).subscribe();
    }
  }

  private prepareCreatedReceiptData(submitData: any, responseData: any) {
    let data = { ...responseData, ...submitData };
    data.payments = [];

    data.payment.forEach((paymentType: any, index: number) => {
      data.payments[index] = {
        payment_type: {
          payment_type: data.payment[index].paymentType,
          friendly_name: translate(
            'payment_types.' + data.payment[index].paymentType
          ),
        },
        amount: data.payment[index].amount,
      };

      // Preserve original currency amount if it exists
      if (data.payment[index].originalAmount) {
        data.payments[index].originalAmount = data.payment[index].originalAmount;
      }

      // Preserve original advance payment if it exists
      if (data.payment[index].originalPaidByAdvance) {
        data.payments[index].originalPaidByAdvance = data.payment[index].originalPaidByAdvance;
      }
    });

    data.invoice_type = {
      invoice_type: data.invoiceType,
      friendly_name: translate('invoice_types.' + data.invoiceType),
    };

    data.transaction_type = {
      transaction_type: data.transactionType,
      friendly_name: translate('transaction_types.' + data.transactionType),
    };

    data.invoice_number = data.invoiceNumber;
    data.invoice_number_pos = data.invoiceNumberPos;
    data.invoice_pdf_url = data.invoicePdfUrl;
    data.total_amount = data.totalAmount;

    // Preserve original currency information
    if (data.originalCurrency) {
      data.original_currency = data.originalCurrency;
      data.original_exchange_rate = data.originalExchangeRate;
    }

    return data;
  }

  isDisabled() {
    return !this.formService.isValid();
  }

  closeDialog() {
    if (this.data?.minimizedId)
      this.minimizeService.deleteMinimized(this.data.minimizedId);
    this.createFormService.resetForm();
    this.dialogRef.close();
  }

  addPayment() {
    this.formService.addPaymentRow();
  }

  /**
   * Gets a list of error keys from a requiredMetaFields error object
   */
  getErrorKeys(metaFieldErrors: any): string[] {
    return Object.keys(metaFieldErrors);
  }

  /**
   * Gets a friendly name for a meta field based on its key
   */
  getMetaFieldName(key: string): string {
    const field = this.metaFieldMap.get(key);
    return field?.name || key;
  }

  /**
   * Gets a list of errors for items in the form
   */
  getItemsWithErrors(): string[] {
    const errorMessages: string[] = [];
    const itemsArray = this.formService.getItemsFormArray();

    itemsArray.controls.forEach((control: AbstractControl, index: number) => {
      if (control.invalid && (control.touched || this.showErrors)) {
        // Check for specific errors
        const errors = [];

        if (control.get('name')?.hasError('required')) {
          errors.push(translate('error.item_name_required') || 'Name is required');
        }

        if (control.get('unitPrice')?.hasError('min')) {
          errors.push(translate('error.unit_price_min') || 'Unit price must be at least 1');
        }

        if (control.get('quantity')?.hasError('min')) {
          errors.push(translate('error.quantity_min') || 'Quantity must be at least 1');
        }

        if (control.get('discount')?.hasError('max')) {
          errors.push(translate('error.discount_max') || 'Discount cannot exceed 99%');
        }

        if (control.get('gtin')?.hasError('pattern')) {
          errors.push(translate('error.gtin_format') || 'GTIN must be 8-13 digits');
        }

        if (errors.length > 0) {
          errorMessages.push(`Item #${index + 1}: ${errors.join(', ')}`);
        }
      }
    });

    return errorMessages;
  }

  /**
   * Gets a list of errors for payment entries in the form
   */
  getPaymentsWithErrors(): string[] {
    const errorMessages: string[] = [];
    const paymentArray = this.formService.getPaymentFormArray();

    paymentArray.controls.forEach((control: AbstractControl, index: number) => {
      if (control.invalid && (control.touched || this.showErrors)) {
        // Check for specific errors
        const errors = [];

        if (control.get('paymentType')?.hasError('required')) {
          errors.push(translate('error.payment_type_required') || 'Payment type is required');
        }

        if (control.get('amount')?.hasError('required')) {
          errors.push(translate('error.amount_required') || 'Amount is required');
        }

        if (errors.length > 0) {
          errorMessages.push(`Payment #${index + 1}: ${errors.join(', ')}`);
        }
      }
    });

    return errorMessages;
  }

  /**
   * Creates a formatted error message for the tooltip
   */
  get errorTooltipContent(): string {
    if (this.loadingReferentDocNumber) {
      return translate('error.loading_referent_doc') || 'Loading referent document number...';
    }

    const errors: string[] = [];

    // Form-level errors
    if (this.receiptsForm.hasError('validateAmountExceptAdvance')) {
      errors.push(translate('error.invalid_payment_amount') || 'Invalid payment amount');
    }

    if (this.receiptsForm.hasError('sumComparisonValidator')) {
      errors.push(translate('error.payment_items_mismatch') || 'Total payment amount must match total item amount');
    }

    if (this.receiptsForm.hasError('referentDocumentNumberRequired')) {
      errors.push(translate('error.referent_document_required') || 'Referent document number is required');
    }

    if (this.receiptsForm.hasError('refDocDtRequired')) {
      errors.push(translate('error.referent_date_required') || 'Referent document date is required');
    }

    if (this.receiptsForm.hasError('customerIndetificationRequired')) {
      errors.push(translate('error.customer_id_required') || 'Customer identification is required');
    }

    if (this.receiptsForm.hasError('buyerIdRequired')) {
      errors.push(translate('error.buyer_id_required') || 'Buyer ID is required');
    }

    if (this.receiptsForm.hasError('buyerCostIdRequired')) {
      errors.push(translate('error.buyer_cost_id_required') || 'Buyer cost center ID is required');
    }

    // Required fields errors
    const metaFieldErrors = this.receiptsForm.getError('requiredMetaFields');
    if (metaFieldErrors) {
      const missingFields = this.getErrorKeys(metaFieldErrors)
        .map(key => this.getMetaFieldName(key))
        .join(', ');
      errors.push(`${translate('error.required_meta_fields') || 'Required meta fields are missing'}: ${missingFields}`);
    }

    // Item errors
    const itemErrors = this.getItemsWithErrors();
    if (itemErrors.length > 0) {
      errors.push(`${translate('error.item_errors') || 'Items have errors'}`);
    }

    // Payment errors
    const paymentErrors = this.getPaymentsWithErrors();
    if (paymentErrors.length > 0) {
      errors.push(`${translate('error.payment_errors') || 'Payments have errors'}`);
    }

    if (errors.length === 0) {
      return translate('error.invalid_form') || 'Form contains errors. Please check all fields.';
    }

    return errors.join('\n');
  }

  /**
   * Opens a modal with detailed error information
   */
  openErrorModal() {
    // This is a simplified version - you could create a proper dialog
    // with a custom component showing a more detailed error summary
    this.showErrors = true; // This will display the built-in error summary
  }

  // Helper methods for RSD conversion
  getPaymentAmountInRSD(index: number): number {
    const amount = this.formService.getPaymentControl(index, 'amount').value || 0;
    const exchangeRate = this.formService.getExchangeRateControl().value || 1;
    return amount * exchangeRate;
  }

  getAdvanceAmountInRSD(index: number): number {
    const amount = this.formService.getPaymentControl(index, 'paidByAdvance').value || 0;
    const exchangeRate = this.formService.getExchangeRateControl().value || 1;
    return amount * exchangeRate;
  }

  getTotalPaymentInRSD(): number {
    const totalPayment = this.formService.getPaymentSum() || 0;
    const exchangeRate = this.formService.getExchangeRateControl().value || 1;
    return totalPayment * exchangeRate;
  }

  getTotalAdvanceInRSD(): number {
    const totalAdvance = this.formService.getPayByAdvanceSum() || 0;
    const exchangeRate = this.formService.getExchangeRateControl().value || 1;
    return totalAdvance * exchangeRate;
  }
}
