import { Component, OnInit, Inject } from '@angular/core';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
import { translate } from '@ngneat/transloco';
import { PaymentSlip } from '../payment-slip/payment-slip.component';
import { DomSanitizer, SafeUrl } from '@angular/platform-browser';
import { QRService } from 'src/app/shared/services/backend/qr/qr.service';

@Component({
  selector: 'app-billing-details-dialog',
  templateUrl: './billing-details-dialog.component.html',
  styleUrls: ['./billing-details-dialog.component.scss'],
})
export class BillingDetailsDialogComponent implements OnInit {
  constructor(
    @Inject(MAT_DIALOG_DATA) public data: any,
    private qrService: QRService,
    private sanitizer: DomSanitizer
  ) {}

  filteredItems: any[] = [];

  paymentSlipData: PaymentSlip = {
    payer: `${this.data.user.billing.customerName}, ${this.data.user.billing.customerAddress}, ${this.data.user.billing.customerBillingDistrict}`,
    paymentPurpouse: 'Uplata po računu broj ' + this.data.id,
    recipient: 'SQUARE ONE CONSULTING',
    payerSignature: '',
    paymentPass: '',
    currency: 'RSD',
    amount: this.data.total,
    recipientAcc: '265163031000953625',
    model: '',
    referanceNumber: this.data.id,
    receptionDate: '',
    currencyDate: '',
  };

  imageUrl: SafeUrl = '';

  ngOnInit(): void {
    this.initalizeItems();
    this.generateQr();
  }

  private generateQr() {
    let ro = '00';
    if (this.data.id != '\\') ro += this.data.id;
    this.qrService
      .generateQR({
        K: 'PR',
        V: '01',
        C: '1',
        R: '265163031000953625',
        N: 'SQUARE ONE CONSULTING',
        I: 'RSD' + this.formatNumber(this.data.total),
        SF: '221',
        RO: '00' + ro,
      })
      .subscribe((response: any) => {
        const blob = new Blob([response], { type: 'image/png' });
        this.imageUrl = this.sanitizer.bypassSecurityTrustUrl(
          URL.createObjectURL(blob)
        );
      });
  }

  private formatNumber(originalNumber: number) {
    if (Number.isInteger(originalNumber))
      return originalNumber.toString() + ',00';

    return originalNumber.toString().replace('.', ',');
  }

  private initalizeItems() {
    this.filteredItems = this.data.invoiceItems;
  }

  filterItems(filters: any) {
    let tempData = this.data.invoiceItems;
    if (filters.searchName != '') {
      tempData = tempData.filter((item: any) =>
        item.invoiceItemName
          .toLowerCase()
          .includes(filters.searchName.toLowerCase())
      );
    }
    if (filters.searchPrice != '') {
      tempData = tempData.filter(
        (item: any) => item.invoiceItemUnitPrice == filters.searchPrice
      );
    }
    if (filters.searchQuantity != '') {
      tempData = tempData.filter(
        (item: any) => item.invoiceItemQuantity == filters.searchQuantity
      );
    }

    this.filteredItems = tempData;
  }

  getStatus(status: string) {
    switch (status.toLowerCase()) {
      case 'sent':
        return { text: translate('billing.sent'), color: 'success' };
        break;
      case 'unsent':
        return { text: translate('billing.un_sent'), color: 'warning' };
        break;
      case 'not_created':
        return { text: translate('billing.not_created'), color: 'danger' };
        break;
      case 'paid':
        return { text: translate('billing.paid'), color: 'success' };
        break;
      default:
        return { text: translate('billing.not_created'), color: 'danger' };
        break;
    }
  }
}
