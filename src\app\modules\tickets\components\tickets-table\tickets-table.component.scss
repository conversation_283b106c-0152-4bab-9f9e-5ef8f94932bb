.table-container {
  width: 100%;
  overflow-y: auto;
  overflow-x: auto; /* Add horizontal scrolling */
}

.tickets-table {
  min-width: 100% !important;
  overflow-x: hidden;
  margin: 0px !important;
  tr.mat-header-row {
    height: 50px;
  }
  th {
    font-family: "Inter" !important;
    font-weight: 600;
    padding-left: 20px;
    font-size: 14px;
    color: #848dac;
    border-top: 1px solid #e1e5f2;
    border-bottom: 1px solid #e1e5f2;
    width: 250px !important;
    text-wrap: nowrap !important;
    background-color: #fafbff;
  }
  td {
    padding-left: 20px;
    font-size: 13px;
    color: #022b3a;
    border-bottom: 1px solid #e1e5f2;
    width: 250px !important;
    text-wrap: nowrap !important;
  }
  .icons {
    display: flex;
    height: 100%;
  }
  .actions {
    width: 20px !important;
    padding-left: 0;
    padding-right: 0;
    color: #1f7a8c;
    z-index: -2;
  }
  .inactive {
    background: repeating-linear-gradient(
      -55deg,
      #22222216,
      #22222216,
      10px,
      #33333322 10px,
      #33333322 20px
    );
  }
}

tr.detail-row {
  height: 0;
}

tr:hover {
  background: #edf5fd;
  cursor: pointer;
}

tr.element-row:not(.expanded-row):active {
  background: #efefef;
}

.element-row td {
  border-bottom-width: 0;
}

.element-detail {
  overflow: hidden;
  width: 100%;
}

.expanded-row {
  background-color: #edf5fd;
}
.detail-row {
  background-color: #edf5fd;
}

tr.detail-row {
  height: 0;
  background-color: #edf5fd;
}

.mat-table {
  table-layout: fixed;
  min-width: 300px;
}

.mat-header-cell.stickyEnd {
  position: sticky;
  left: 0;
  z-index: 1;
  background-color: red;
}

.icon {
  cursor: pointer;
  font-size: 20px;
  margin-top: 9px;
}

button {
  width: 100%;
  display: block;
  text-align: left;
}

::ng-deep.mat-menu-panel {
  max-height: 200px !important;
}
