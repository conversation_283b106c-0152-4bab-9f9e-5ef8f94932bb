import { Component, OnInit, Input, ViewChild } from '@angular/core';
import { MatAutocomplete } from '@angular/material/autocomplete';
import { MatFormFieldAppearance } from '@angular/material/form-field';
import { FormControl } from '@angular/forms';
import { Customer } from 'src/app/shared/services/backend/receipts/types/receipts-response.dto';

@Component({
  selector: 'app-meta-field-input',
  templateUrl: './meta-field-input.component.html',
  styleUrls: ['./meta-field-input.component.scss'],
})
export class MetaFieldInputComponent implements OnInit {
  @Input() placeholder?: string = '';
  @Input() tooltip: string = '';
  @Input() type: string = 'text';
  @Input() appearance: MatFormFieldAppearance = 'outline';
  @Input() control: FormControl = new FormControl();

  @Input() data: any[] = [];
  transformedData: any[] = [];

  filteredOptions: any[] = [];

  @ViewChild('auto') auto!: MatAutocomplete;

  constructor() {}

  ngOnInit(): void {
    this.filteredOptions = this.data;
    this.control.valueChanges.subscribe((value) => {
      this.filteredOptions = value ? this.filter(value) : this.data.slice();
    });
  }

  displayFn(option: string | Customer): string {
    if (typeof option === 'string') {
      return option;
    } else {
      return `${option.customerName} - ${option.customerEmail} - ${option.customerJMBG}`;
    }
  }

  displayWith = (value?: any) => {
    const customerData = this.data.find(
      (field: any) => field.value == value
    ) as any;
    if (!customerData) return value;
    return `${customerData.customerName} - ${customerData.customerJMBG} - ${customerData.customerEmail}`;
  };

  filter(value: string): (string | Customer)[] {
    const filterValue = value.toLowerCase();
    return this.data.filter((el) =>
      typeof el === 'string'
        ? el.toLowerCase().includes(filterValue)
        : this.displayFn(el).toLowerCase().includes(filterValue)
    );
  }

  getValue(element: any) {
    if (typeof element === 'string') return element;
    return element.value;
  }
}
