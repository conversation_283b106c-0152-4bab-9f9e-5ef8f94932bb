.x-button{
    position: relative;
    top:0px;
    left:0px;
    background: transparent;
    border: none;
}

#identification{
    margin-bottom: 40px;
    ::ng-deep{
        .mat-form-field-appearance-outline .mat-form-field-outline{
            background-color:#E1E5F2
        } 
        .mat-form-field{
            font-size: 12px !important;
        }
    }  
}
#test{
    height:100%;
}

.container-fluid{
    height:100%;
}

#leftPart{
    min-height: 100%;
    border-right: 3px solid #E1E5F2;
    padding-right: 25px;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    position: relative;

    .refundRow{
        position: absolute;
        width: 100%;
        bottom: 50px;
    }

    button{
        background-color:#008BFF;
        border: none;
        cursor: pointer;
        color: white;
        justify-content: center;
        font-size: 0.9rem;
        display: flex;
        align-items: center;
        padding: 0rem 1rem;
        border-radius: 0.25rem;
        height: 2.6rem;
        width: 100%;
    }

    table{
        table-layout:fixed;
        width: 100%;
        th{
            color:#022B3A !important;
            font-size:14px;
            font-weight: 500;
            border-bottom: 1px solid #E1E5F2;
        }
        tr{
            border-bottom: 1px solid #E1E5F2;
        }
        td{
            font-size:12px;
            color:#022B3A !important;
            padding-top: 10px;
            padding-bottom: 10px; 
            padding-right: 20px;
        }
        ::ng-deep{
            .mat-form-field{
                font-size: 11px !important;
            }
           
        }  
    }

  
}
