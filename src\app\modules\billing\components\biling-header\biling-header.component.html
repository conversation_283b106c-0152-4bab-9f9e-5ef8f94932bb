<div class="container-fluid filters">
  <form>
    <div class="row align-items-center">
      <div class="col-md-3">
        <app-expand-btn
          text="Grupne akcije"
          [menuItems]="menuItems"
          [disabled]="!this.bulkActionService.getIsEnoughSelected()"
        ></app-expand-btn>
      </div>
      <div class="col-md-3">
        <app-fiscomm-select
          placeholder="Izaberite mesec"
          [enableNone]="true"
          [options]="monthsSelect"
        >
        </app-fiscomm-select>
      </div>
      <div class="col-md-3">
        <app-fiscomm-select placeholder="Izaberite godinu" [enableNone]="true">
        </app-fiscomm-select>
      </div>
      <div class="col-md-2">
        <app-fiscomm-select placeholder="Izaberite status" [enableNone]="true">
        </app-fiscomm-select>
      </div>

      <!-- <div class="col-lg-4 col-md-6 d-flex buttons justify-content-end">
        <span class="icon" (click)="actionEmitter.emit({ action: 'Refresh' })">
          <mat-icon>update</mat-icon>
        </span>
        <span>
          <p>Ažurirano:</p>
          <p>{{ refreshTime | fiscommDate }}</p>
        </span>
      </div> -->
      <!-- <div class="col-lg-2 col-md-6 d-flex align-items-center justify-content-end buttons">
                <input type="checkbox"><p>Trening mod</p>
            </div> -->
    </div>
  </form>
</div>
