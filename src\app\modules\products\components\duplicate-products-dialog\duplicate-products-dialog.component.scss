.duplicate-products-dialog {
  max-width: 900px;
  overflow: hidden;
}

.mat-mdc-dialog-content {
  max-height: 80vh;
  padding-bottom: 20px;
}

.table-container {
  overflow-x: auto;
  max-width: 100%;
}

table {
  min-width: 750px;
  width: 100%;
}

mat-dialog-actions {
  margin-top: 16px;
  padding: 8px 0;
}

.product-info {
  padding: 8px 0;

  div {
    margin-bottom: 4px;
  }
}

.currency-prices {
  margin-top: 10px;
  padding: 8px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border-left: 3px solid #2196F3;

  .currency-prices-title {
    margin-bottom: 6px;
    color: #0d47a1;
  }

  .currency-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    .currency-tag {
      display: inline-block;
      padding: 4px 8px;
      border-radius: 16px;
      font-size: 0.85rem;
      background-color: #e9ecef;
      color: #495057;
      white-space: nowrap;

      &.has-value {
        background-color: #e3f2fd;
        color: #0d47a1;
        border: 1px solid #90caf9;
        font-weight: 500;
      }
    }
  }
}
