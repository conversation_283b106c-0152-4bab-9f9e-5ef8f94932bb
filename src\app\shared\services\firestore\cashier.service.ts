import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable, of, tap, map, switchMap, from } from 'rxjs';
import { Cashier } from '../../../models/cashier.model';
import { environment } from 'src/environments/environment';
import { AngularFirestore } from '@angular/fire/compat/firestore';
import { AuthService } from '../../../core/services/auth.service';

@Injectable({
  providedIn: 'root'
})
export class CashierService {
  private cashiersSubject = new BehaviorSubject<Cashier[]>([]);
  public cashiers$ = this.cashiersSubject.asObservable();
  private readonly COLLECTION_NAME = 'platform-cashiers';

  constructor(
    private http: HttpClient,
    private firestore: AngularFirestore,
    private authService: AuthService
  ) { }

  getCashiers(): Observable<Cashier[]> {
    return this.authService.getUserId().pipe(
      switchMap(uid => {
        if (!uid) return of([]);
        return this.firestore.collection<Cashier>(this.COLLECTION_NAME,
          ref => ref.where('uid', '==', uid)
        ).snapshotChanges().pipe(
          map(actions => actions.map(a => {
            const data = a.payload.doc.data();
            const id = a.payload.doc.id;

            // Convert Firestore Timestamp to JavaScript Date
            let createdAt = undefined;
            if (data.createdAt) {
              // For Firestore Timestamp
              if (data.createdAt.toDate) {
                createdAt = data.createdAt.toDate();
              } else {
                // For regular Date objects
                createdAt = new Date(data.createdAt);
              }
            }

            return {
              ...data,
              id,
              createdAt
            } as Cashier;
          })),
          tap(cashiers => this.cashiersSubject.next(cashiers))
        );
      })
    );
  }

  addCashier(cashier: Cashier): Observable<Cashier> {
    return this.authService.getUserId().pipe(
      switchMap(uid => {
        if (!uid) return of(cashier);

        // Add uid and createdAt to cashier
        const newCashier: Cashier = {
          ...cashier,
          uid,
          createdAt: new Date()
        };

        return from(this.firestore.collection(this.COLLECTION_NAME).add(newCashier)).pipe(
          map(docRef => {
            return { ...newCashier, id: docRef.id };
          }),
          tap(() => this.getCashiers().subscribe())
        );
      })
    );
  }

  updateCashier(cashier: Cashier): Observable<Cashier> {
    if (!cashier.id) {
      return of(cashier);
    }

    // Create a copy without the id field (as it's not part of the document data)
    const { id, ...cashierData } = cashier;
    return from(this.firestore.collection(this.COLLECTION_NAME).doc(id).update(cashierData)).pipe(
      map(() => cashier),
      tap(() => this.getCashiers().subscribe())
    );
  }

  deleteCashier(id: string): Observable<void> {
    return from(this.firestore.collection(this.COLLECTION_NAME).doc(id).delete()).pipe(
      tap(() => this.getCashiers().subscribe())
    );
  }

  getActiveCashiers(): Observable<Cashier[]> {
    return this.authService.getUserId().pipe(
      switchMap(uid => {
        if (!uid) return of([]);

        return this.firestore.collection<Cashier>(this.COLLECTION_NAME,
          ref => ref.where('uid', '==', uid).where('active', '==', true)
        ).snapshotChanges().pipe(
          map(actions => actions.map(a => {
            const data = a.payload.doc.data();
            const id = a.payload.doc.id;
            // Convert Firestore Timestamp to JavaScript Date
            let createdAt = undefined;
            if (data.createdAt) {
              // For Firestore Timestamp
              if (data.createdAt.toDate) {
                createdAt = data.createdAt.toDate();
              } else {
                // For regular Date objects
                createdAt = new Date(data.createdAt);
              }
            }
            return {
              ...data,
              id,
              createdAt
            } as Cashier;
          })),
          tap(cashiers => {
            this.cashiersSubject.next(cashiers);
          })
        );
      })
    );
  }

  getCashierSuggestions(query: string): Observable<Cashier[]> {
    if (!query || query.trim() === '') {
      return of([]);
    }

    return this.authService.getUserId().pipe(
      switchMap(uid => {
        if (!uid) return of([]);

        // Get all active cashiers for the current user
        return this.firestore.collection<Cashier>(this.COLLECTION_NAME,
          ref => ref.where('uid', '==', uid).where('active', '==', true)
        ).snapshotChanges().pipe(
          map(actions => actions.map(a => {
            const data = a.payload.doc.data();
            const id = a.payload.doc.id;

            // Convert Firestore Timestamp to JavaScript Date
            let createdAt = undefined;
            if (data.createdAt) {
              // For Firestore Timestamp
              if (data.createdAt.toDate) {
                createdAt = data.createdAt.toDate();
              } else {
                // For regular Date objects
                createdAt = new Date(data.createdAt);
              }
            }

            return {
              ...data,
              id,
              createdAt
            } as Cashier;
          })),
          // Filter by name containing the query (case insensitive)
          map(cashiers => cashiers.filter(cashier =>
            cashier.name.toLowerCase().includes(query.toLowerCase())
          ))
        );
      })
    );
  }
}
