// Custom Theming for Angular Material
// For more information: https://material.angular.io/guide/theming
@use "@angular/material" as mat;
@import "assets/styles/variables.scss";
@import "~bootstrap/scss/bootstrap";
@import "~@angular/material/prebuilt-themes/indigo-pink.css";
@import url("https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css");

@import url("https://fonts.googleapis.com/css2?family=Inter:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,800;1,100;1,200;1,300;1,400;1,500&display=swap");

@import url("https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&family=Roboto:wght@300&display=swap");
@import url("https://fonts.googleapis.com/icon?family=Material+Icons|Material+Icons+Outlined");

// Plus imports for other components in your app.

// Include the common styles for Angular Material. We include this here so that you only
// have to load a single css file for Angular Material in your app.
// Be sure that you only ever include this mixin once!
@include mat.core();

@font-face {
  font-family: "dejavu_sans_monobook";
  src: url("assets/DejaVuSansMono-webfont.woff") format("woff");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "dejavu_sans_monobold";
  src: url("assets/DejaVuSansMono-Bold-webfont.woff") format("woff");
  font-weight: normal;
  font-style: normal;
}

// Define the palettes for your theme using the Material Design palettes available in palette.scss
// (imported above). For each palette, you can optionally specify a default, lighter, and darker
// hue. Available color palettes: https://material.io/design/color/
$fiscomm-e-invoices-client-primary: mat.define-palette(mat.$indigo-palette);
$fiscomm-e-invoices-client-accent: mat.define-palette(
  mat.$pink-palette,
  A200,
  A100,
  A400
);

// The warn palette is optional (defaults to red).
$fiscomm-e-invoices-client-warn: mat.define-palette(mat.$red-palette);

// Create the theme object. A theme consists of configurations for individual
// theming systems such as "color" or "typography".
$custom-font-family: "Inter", sans-serif;
$fiscomm-e-invoices-client-theme: mat.define-light-theme(
  (
    typography: (
      font-family: $custom-font-family,
    ),
    color: (
      primary: $fiscomm-e-invoices-client-primary,
      accent: $fiscomm-e-invoices-client-accent,
      warn: $fiscomm-e-invoices-client-warn,
    ),
  )
);

// Include theme styles for core and each component used in your app.
// Alternatively, you can import and @include the theme mixins for each component
// that you are using.
@include mat.all-component-themes($fiscomm-e-invoices-client-theme);

:root {
  --menu-width-open: 200px;
  --menu-width-closed: 64px;
}

body {
  background-color: $background-color;
}

/* You can add global styles to this file, and also import other style files */
body {
  background-color: #f1f3f9;
}

html,
body {
  height: 100%;
}
body {
  margin: 0;
  font-family: "Inter" !important;
  font-style: normal;
}

// ----------Material select---------------

.mat-tab-label,
.mat-tab-link,
.mat-button,
.mat-slide-toggle-content,
.mat-table {
  font-family: "Inter" !important;
}

.mat-tab-group.mat-primary .mat-ink-bar,
.mat-tab-nav-bar.mat-primary .mat-ink-bar {
  bottom: -1px !important;
  background-color: #2cafc9 !important;
}

.select-gray,
.select-blue,
.select-lighter {
  .mat-form-field-infix {
    border-top: 5px solid transparent !important;
    .mat-select-arrow-wrapper {
      transform: translateY(0%) !important;
    }
  }

  .mat-form-field-underline {
    display: none;
  }

  .mat-form-field-flex {
    border-radius: 0.25rem !important;
    font-size: 0.9rem !important;
    background-color: #e7eaf5 !important;
  }
}

.select-gray,
.select-lighter {
  .mat-select-value {
    font-style: italic;
    color: #5a8699 !important;
  }
}

.select-lighter {
  .mat-form-field-flex {
    border-radius: 0.25rem !important;
    font-size: 0.9rem !important;
    background-color: #fbfdff !important;
  }
}

.select-blue {
  .mat-form-field-flex {
    background-color: #008bff !important;
  }
  .mat-select-value {
    font-weight: bold;
    text-align: center;
    color: white !important;
  }
  .mat-select-arrow {
    color: white !important;
  }
}

.select-no-padding {
  .mat-form-field-wrapper {
    padding-bottom: 0 !important;
  }
}
//------------------------------------------

//------------Material dialog---------------
.mat-dialog-content {
  max-height: none !important;
}

.mat-dialog-container {
  background-color: #edf5fd;
}

.cdk-overlay-pane {
  max-width: none !important;
}
//------------------------------------------

html,
body {
  height: 100%;
}

.main {
  padding: 40px;

  // homepage should be full height
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.color-dark-blue {
  color: $fiscomm-dark-blue !important;
}

.add-invoice-button,
.btn-green,
.btn-red,
.action-invoice-button {
  background-color: #3dccaa;
  border: none;
  cursor: pointer;
  color: white;
  justify-content: center;
  font-size: 0.9rem;
  // font-weight: bold;
  display: flex;
  align-items: center;
  padding: 0rem 1rem;
  border-radius: 0.25rem;
  height: 2.6rem;
}

.action-invoice-button,
.btn-primary {
  background-color: #008bff;
  justify-content: center;
}

.btn-green:hover {
  background-color: #2dbb99;
}

.btn-red {
  background-color: #eb8e88;
  justify-content: center;
}

.btn-red:hover {
  background-color: #d17a74;
}
th.mat-header-cell {
  font-size: 1.2rem;
}

.bad-toast {
  background-color: red;
  color: white;
  button {
    color: white;
  }
}

.good-toast {
  background-color: #3dccaa;
  color: white;
  button {
    color: white;
  }
}


// need to globally make overflow y of cdk-global-overlay-wrapper scrollable
.cdk-global-overlay-wrapper {
  overflow-y: auto;
}



