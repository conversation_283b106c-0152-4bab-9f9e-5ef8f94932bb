.container-fluid {
  background-color: white;
  padding: 10px;
  border-radius: 4px;
  padding-left: 20px;
  p {
    margin: 0;
    color: #4f4f4f;
    font-size: 15px;
  }
  .buttons {
    width: 165px;
  }
  select {
    background-color: transparent;
    border: 1px solid #dfe3e8;
    color: #4f4f4f;
  }
}

.page_button {
  border: 1px solid #dfe3e8;
  padding: 5px;
  margin-left: 5px;
  margin-right: 5px;
  color: #4f4f4f;
  cursor: pointer;
}

.page_button:not(.disabled):hover {
  border: 1px solid #2cafc9;
  color: #2cafc9;
}

.disabled {
  background-color: #4f4f4f0a;
  color: #4f4f4f77;
  opacity: 0.6;
  cursor: default;
}

.right {
  justify-content: end;
}

.rightContainer {
  justify-content: end;
}

@media only screen and (max-width: 576px) {
  .right {
    justify-content: start !important;
  }
  .rightContainer {
    justify-content: space-between;
  }
}
