import { DatePipe } from '@angular/common';
import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'fiscommDate',
})
export class FiscommDatePipe implements PipeTransform {
  constructor(private datePipe: DatePipe) {}

  transform(value: any): string {
    let format = 'dd.M.yyyy, HH:mm';
    const date = new Date(value || new Date());

    // Get the current timezone offset in minutes
    const timezoneOffsetInMinutes = date.getTimezoneOffset();

    // Adjust the date by subtracting the timezone offset in minutes
    date.setMinutes(date.getMinutes() + timezoneOffsetInMinutes);

    return this.datePipe.transform(date, format) as string;
  }
}
