import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class ReportsService {

  constructor(private http: HttpClient) { }

  mainFiscommTravelagency:string = environment.mainFiscommTravelagency.baseUrl
  authToken = environment.authToken;

  headers = new HttpHeaders({
    'Content-Type': 'application/json',
    Authorization: `Bearer ${this.authToken}`,
  });

  generateReport(report:any) {
    return this.http.post<any>(`${this.mainFiscommTravelagency}/reports/${report.type}`, report, { headers: this.headers });
  }

  getReports() {
    return this.http.get(`${this.mainFiscommTravelagency}/reports`, { headers: this.headers });
  }
}

interface Filters {
  dateFrom?: string;
  dateTo?: string;
  status?: string;
}
