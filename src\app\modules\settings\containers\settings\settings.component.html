<div
  class="container-fluid ps-lg-5 pe-lg-5 pt-lg-4 pb-lg-4 ps-md-3 pe-lmd-3 pt-md-2 pb-md-2"
  *transloco="let t"
>
  <app-page-title text="{{ t('settings') }}"></app-page-title>
  <app-settings-navbar></app-settings-navbar>

  <app-settings-container title="{{ t('base_information') }}">
    <ng-container content>
      <div class="row">
        <div class="col-md-12">
          <p>PIB: {{ generalSettingsData.vatRegNum }}</p>
        </div>
        <div class="col-md-12">
          <p>
            {{ t("settings_page.ident_number") }}: {{ generalSettingsData.mb }}
          </p>
        </div>
        <div class="col-md-12">
          <p>
            {{ t("settings_page.company_name") }}:
            {{ generalSettingsData.name }}
          </p>
        </div>
        <div class="col-md-12">
          <p>
            {{ t("settings_page.monthly_price") }}:
            {{ generalSettingsData.monthlyPrice }}
          </p>
        </div>
      </div>
      <div class="row">
        <div class="col-md-12">
          <app-fiscomm-input
            [control]="adress"
            placeholder="{{ t('settings_page.input.adress') }}"
          ></app-fiscomm-input>
        </div>
        <div class="col-md-12">
          <app-fiscomm-input
            [control]="municipality"
            placeholder="{{ t('settings_page.input.municipality') }}"
          ></app-fiscomm-input>
        </div>
        <div class="col-md-12">
          <app-fiscomm-input
            [control]="postalCode"
            placeholder="{{ t('settings_page.input.postal_code') }}"
          ></app-fiscomm-input>
        </div>
        <ng-container
          *ngFor="let email of generalSettingsData.emails; let i = index"
        >
          <div *ngIf="i > 0" class="col-md-11">
            <app-fiscomm-input
              [control]="getEmailControl(i)"
              placeholder="E-mail"
            ></app-fiscomm-input>
          </div>
          <div
            *ngIf="i > 0"
            class="col-md-1 d-flex align-items-center"
            (click)="removeEmail(i)"
          >
            <mat-icon>delete</mat-icon>
          </div>
          <div *ngIf="i == 0" class="col-md-12">
            <app-fiscomm-input
              [control]="getEmailControl(i)"
              placeholder="E-mail"
            ></app-fiscomm-input>
          </div>
        </ng-container>

        <div class="col-md-12">
          <a class="d-flex align-items-center" (click)="addEmail()">
            <mat-icon>add</mat-icon>{{ t("settings_page.additional_email") }}
          </a>
        </div>
      </div>
    </ng-container>
    <ng-container buttons>
      <app-success-btn
        text="{{ t('settings_page.save_changes') }}"
        (click)="saveGeneralSettings()"
      ></app-success-btn>
    </ng-container>
  </app-settings-container>
</div>
