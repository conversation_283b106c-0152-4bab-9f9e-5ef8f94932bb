import { Component, Inject, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators, FormArray, FormControl } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { CustomField, CustomFieldType } from 'src/app/models/custom-field.model';
import { CustomFieldService } from 'src/app/shared/services/firestore/custom-field.service';
import { TranslocoService } from '@ngneat/transloco';

@Component({
  selector: 'app-add-custom-field-dialog',
  templateUrl: './add-custom-field-dialog.component.html',
  styleUrls: ['./add-custom-field-dialog.component.scss']
})
export class AddCustomFieldDialogComponent implements OnInit {
  customFieldForm: FormGroup;

  isEditMode = false;
  dialogTitle: string;

  // Form controls for direct access in template
  nameControl: FormControl;
  descriptionControl: FormControl;
  defaultValueControl: FormControl;
  fieldTypeControl: FormControl;

  constructor(
    private fb: FormBuilder,
    private dialogRef: MatDialogRef<AddCustomFieldDialogComponent>,
    private customFieldService: CustomFieldService,
    private translocoService: TranslocoService,
    @Inject(MAT_DIALOG_DATA) public data: { customField?: CustomField }
  ) {
    this.nameControl = new FormControl('', [Validators.required, Validators.maxLength(100)]);
    this.descriptionControl = new FormControl('', Validators.maxLength(255));
    this.defaultValueControl = new FormControl('');
    this.fieldTypeControl = new FormControl('select', Validators.required);

    this.customFieldForm = this.createForm();

    if (data?.customField) {
      this.isEditMode = true;
      this.dialogTitle = this.translocoService.translate('custom_fields.edit');
      this.populateForm(data.customField);
    } else {
      this.dialogTitle = this.translocoService.translate('custom_fields.add');
    }
  }

  ngOnInit(): void {
    // React to field type changes to show/hide possible values
    this.fieldTypeControl.valueChanges.subscribe(value => {
      if (value === 'select') {
        this.ensurePossibleValuesExist();
      }
    });
  }

  private createForm(): FormGroup {
    return this.fb.group({
      name: this.nameControl,
      description: this.descriptionControl,
      fieldType: 'select',
      isRequired: [false, Validators.required],
      defaultValue: this.defaultValueControl,
      possibleValues: this.fb.array([]),
      isActive: true
    });
  }

  private populateForm(customField: CustomField): void {
    this.nameControl.setValue(customField.name);
    this.descriptionControl.setValue(customField.description || '');
    this.defaultValueControl.setValue(customField.defaultValue || '');
    this.fieldTypeControl.setValue(customField.fieldType);

    this.customFieldForm.patchValue({
      isRequired: customField.isRequired,
      isActive: customField.isActive
    });

    // Set possible values if they exist
    if (customField.possibleValues && customField.possibleValues.length > 0) {
      const valuesFormArray = this.customFieldForm.get('possibleValues') as FormArray;
      // Clear existing form array
      while (valuesFormArray.length) {
        valuesFormArray.removeAt(0);
      }

      // Add each possible value
      customField.possibleValues.forEach(value => {
        valuesFormArray.push(this.fb.control(value));
      });
    }
  }

  get possibleValues(): FormArray {
    return this.customFieldForm.get('possibleValues') as FormArray;
  }

  getPossibleValueControl(index: number): FormControl {
    return this.possibleValues.at(index) as FormControl;
  }

  addPossibleValue(): void {
    this.possibleValues.push(this.fb.control(''));
  }

  removePossibleValue(index: number): void {
    this.possibleValues.removeAt(index);
  }

  private ensurePossibleValuesExist(): void {
    if (this.possibleValues.length === 0) {
      this.addPossibleValue();
    }
  }

  isSelectType(): boolean {
    return this.fieldTypeControl.value === 'select';
  }

  cancel(): void {
    this.dialogRef.close();
  }

  save(): void {
    if (this.customFieldForm.invalid) {
      return;
    }

    const formValue = this.customFieldForm.value;
    const customField: CustomField = {
      name: formValue.name,
      description: formValue.description,
      fieldType: formValue.fieldType,
      isRequired: formValue.isRequired,
      defaultValue: formValue.defaultValue,
      isActive: formValue.isActive,
      order: this.data?.customField?.order || 0,
      possibleValues: this.isSelectType() ? formValue.possibleValues.filter((v: string) => v.trim() !== '') : undefined
    };

    if (this.isEditMode && this.data.customField?.id) {
      customField.id = this.data.customField.id;
      this.customFieldService.updateCustomField(customField).subscribe(() => {
        this.dialogRef.close(true);
      });
    } else {
      this.customFieldService.addCustomField(customField).subscribe(() => {
        this.dialogRef.close(true);
      });
    }
  }
}
