<div class="invoices-bulk" *transloco="let t">
    <div class="title">
      <h1 mat-dialog-title>{{t('receipts.name')}}</h1>
    </div>
    <button class="x-button" mat-dialog-close>
      <mat-icon>close</mat-icon>
    </button>
    <div mat-dialog-content>
      <div class="filter-group">

      </div>
        <table class="bulk-table" id="rowInfo">
        <thead>
            <tr>
            <th scope="col">{{t('receipts.invoice_number')}}</th>
            <th scope="col">{{t('receipts.invoice_number_pos')}}</th>
            <th scope="col">{{t('receipts.total_amount')}}</th>
            <th scope="col">{{t('receipts.customer_iden')}}</th>
            <th scope="col"><div [class]="
              'status ' +
              (allDone === 'waiting'
                ? 'red'
                : allDone=== 'done'
                ? 'green'
                : '')
            ">
                {{ t(allDone) }}
              </div>
            </th>
            </tr>
        </thead>
        <tbody>
            <tr *ngFor="let receipt of receipts; let i = index">
            <td scope="row">{{ receipt.invoice_number }}</td>
            <td>
                {{ receipt.invoice_number_pos}}
            </td>
            <td>
                {{ receipt.total_amount}} RSD
            </td>
            <td class="d-flex">
              <form [formGroup]="refundFormService.getRefundForm()">
                <app-fiscomm-select [control]="refundFormService.getBuyerIDPrefix(i)"
                placeholder="{{t('receipts.input.document_type')}}"
                [options]="selectDataService.getBuyerIdDocumentTypes()">
              </app-fiscomm-select>
            
              <app-fiscomm-input [control]="refundFormService.getBuyerIDSufix(i)"
                placeholder="{{t('receipts.input.document_number')}}">
              </app-fiscomm-input>
            </form>
            </td>
            <td>
              <div [class]="
              'status ' +
              (receiptStatus[i] === 'waiting'
                ? 'red'
                : receiptStatus[i] === 'done'
                ? 'green'
                : '')
            ">
            {{ t(receiptStatus[i]) }}
          </div>
            </td>
            </tr>
        </tbody>
        </table>
    </div>
    <div class="space"></div>
    <div class="modal-buttons border-top" mat-dialog-actions>
      <app-danger-btn text="{{t('cancel')}}" mat-dialog-close=""></app-danger-btn>
      <app-success-btn text="{{t('start')}}" [disabled]="createDisabled" (click)="handleCreate()"></app-success-btn>
    </div>
  </div>
  