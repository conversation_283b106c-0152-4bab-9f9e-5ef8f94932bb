.filters {
  width: 100%;
  padding: 20px;
  background-color: white;
  border-top: 3px solid #e1e5f2;
}

.buttons {
  margin-top: 10px;
  div {
    margin-top: 5px;
    p {
      margin-bottom: 0;
      margin-left: 8px;
      font-size: 13px;
      text-wrap: nowrap;
    }

    span.icon {
      cursor: pointer;
      border-radius: 8px;
      width: 50px;
      height: 40px;
      background-color: #d5dcf2;
      .mat-icon {
        color: #205372;
      }
      display: flex;
      justify-content: center;
      align-items: center;
    }
    span.icon:hover {
      background-color: #d5dcf2ae;
    }
  }
}

form {
  height: 100%;
}

.row {
  height: 50%;
}

::ng-deep {
  .mat-form-field {
    font-size: 12px !important;
  }
  #receiptsHeader {
    .mat-form-field-appearance-outline .mat-form-field-outline {
      background-color: #e1e5f2;
    }
  }
  .mat-date-range-input {
    display: flex !important;
  }
  .mat-date-range-input-container {
    display: flex !important;
    align-items: center !important;
    height: 30px !important;
  }
  .mat-form-field-infix {
    display: flex !important;
  }
  .datePicker {
    .mat-form-field-infix {
      padding: 0;
    }
  }
}

.buttons {
  border-top: #e1e5f2 solid 1px;
  padding-top: 15px;
}

#advanced {
  p {
    font-size: 15px;
    margin-bottom: 6px;
    cursor: pointer;
  }
}

.expandRow {
  min-height: 40px;
  overflow: hidden;
}
