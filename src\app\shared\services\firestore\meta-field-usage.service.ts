import { Injectable } from '@angular/core';
import { AngularFirestore } from '@angular/fire/compat/firestore';
import { Observable, from, of } from 'rxjs';
import { switchMap } from 'rxjs/operators';
import firebase from 'firebase/compat/app';

export interface ReceiptInfo {
  invoiceNumber: string;
  sdcDateTime: string;
  invoicePdfUrl: string;
  verificationUrl: string;
  createdAt: Date;
}

@Injectable({
  providedIn: 'root'
})
export class MetaFieldUsageService {
  private readonly COLLECTION_NAME = 'metaFields';

  constructor(private firestore: AngularFirestore) { }

  /**
   * Updates the metaField document and search-values subcollection when a receipt is created
   * @param metaFieldId - The ID of the meta field
   * @param value - The value entered for this field in the receipt
   * @param receiptInfo - Information about the receipt that used this field
   */
  updateFieldUsage(
    metaFieldId: string,
    value: string,
    receiptInfo?: {
      invoiceNumber?: string;
      sdcDateTime?: string;
      invoicePdfUrl?: string;
      verificationUrl?: string;
    }
  ): Observable<void> {
    if (!metaFieldId || !value || value.trim() === '') {
      return of(void 0); // Nothing to update
    }

    // 1. Mark the field as used in receipts
    const updateFieldPromise = this.firestore.collection(this.COLLECTION_NAME)
      .doc(metaFieldId)
      .set({ isReceiptCreated: true }, { merge: true });

    // 2. Add the value to search-values subcollection (avoiding duplicates)
    return from(updateFieldPromise).pipe(
      switchMap(() => this.addSearchValue(metaFieldId, value, receiptInfo))
    );
  }

  /**
   * Adds a value to the search-values subcollection if it doesn't already exist
   * Then adds receipt info to a receipt-info subcollection under the search value
   * @param metaFieldId - The ID of the meta field
   * @param value - The value to add
   * @param receiptInfo - Information about the receipt that used this field
   */
  private addSearchValue(
    metaFieldId: string,
    value: string,
    receiptInfo?: {
      invoiceNumber?: string;
      sdcDateTime?: string;
      invoicePdfUrl?: string;
      verificationUrl?: string;
    }
  ): Observable<void> {
    const normalizedValue = value.trim();
    if (!normalizedValue) return of(void 0);

    // Create a document ID that's deterministic based on the value to prevent duplicates
    const valueId = normalizedValue.toLowerCase();

    // Data to store - include original value and lowercase for easier searching
    const valueData = {
      value: normalizedValue,
      valueLower: normalizedValue.toLowerCase(),
      createdAt: firebase.firestore.FieldValue.serverTimestamp()
    };

    // Reference to the search-values subcollection
    const searchValuesRef = this.firestore.collection(this.COLLECTION_NAME)
      .doc(metaFieldId)
      .collection('search-values')
      .doc(valueId);

    // Check if it exists first, then set or update if necessary
    return from(searchValuesRef.get()).pipe(
      switchMap(doc => {
        // First, ensure the search value document exists
        const valuePromise = !doc.exists
          ? searchValuesRef.set(valueData)
          : Promise.resolve();

        // If receipt info is provided, add it to the receipt-info subcollection
        if (receiptInfo) {
          return from(valuePromise).pipe(
            switchMap(() => {
              // Create a unique ID for the receipt (using invoiceNumber if available)
              const receiptId = receiptInfo.invoiceNumber
                ? receiptInfo.invoiceNumber.replace(/[^a-zA-Z0-9]/g, '')
                : new Date().getTime().toString();

              // Prepare receipt data
              const receiptData: ReceiptInfo = {
                invoiceNumber: receiptInfo.invoiceNumber || '',
                sdcDateTime: receiptInfo.sdcDateTime || '',
                invoicePdfUrl: receiptInfo.invoicePdfUrl || '',
                verificationUrl: receiptInfo.verificationUrl || '',
                createdAt: new Date()
              };

              // Add to receipt-info subcollection
              return from(searchValuesRef.collection('receipt-info').doc(receiptId).set(receiptData));
            })
          );
        }

        return from(valuePromise);
      })
    );
  }
}
