import { Component, OnInit } from '@angular/core';
import { TicketService } from 'src/app/shared/services/backend/tickets/ticket.service';
import { menuItems } from '../../components/tickets-menu/tickets-menu.component';
import { DialogService } from 'src/app/shared/dialogs/dialog.service';

@Component({
  selector: 'app-tickets',
  templateUrl: './tickets.component.html',
  styleUrls: ['./tickets.component.scss'],
})
export class TicketsComponent implements OnInit {
  menuItems: menuItems[] = [
    {
      name: 'U toku',
      link: 'u-toku',
    },
    {
      name: '<PERSON><PERSON><PERSON><PERSON>',
      link: 'zavrsen<PERSON>',
    },
    {
      name: 'Zatvor<PERSON>',
      link: 'zatvoreni',
    },
  ];

  constructor(private dialogService: DialogService) {}

  ngOnInit(): void {}

  openNewTicketDialog() {
    this.dialogService.openNewTicketDialog({
      width: '480px',
      height: '90%',
    });
  }
}
