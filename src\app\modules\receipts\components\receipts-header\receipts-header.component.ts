import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import {
  Customer,
  MetaFieldsDto,
} from 'src/app/shared/services/backend/receipts/types/receipts-response.dto';
import { SelectTypesDataService } from 'src/app/shared/services/select-types-data.service';
import { FormFiltersService } from '../../services/form-filters.service';
import { Action } from '../../containers/receipts/receipts.component';
import {
  animate,
  state,
  style,
  transition,
  trigger,
} from '@angular/animations';
import { BulkActionsService } from '../../../../shared/services/bulk-actions.service';
import { TranslocoService, translate } from '@ngneat/transloco';
import { Observable, Subject, filter, take } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';
import { CustomFieldsAdapterService } from '../../services/custom-fields-adapter.service';
import { FormArray, FormControl, FormGroup } from '@angular/forms';

@Component({
  selector: 'app-receipts-header',
  templateUrl: './receipts-header.component.html',
  styleUrls: ['./receipts-header.component.scss'],
  animations: [
    trigger('collapse', [
      state('collapsed', style({ height: '0px', minHeight: '0' })),
      state('expanded', style({ height: '*' })),
      transition(
        'expanded <=> collapsed',
        animate('225ms cubic-bezier(0.4, 0.0, 0.2, 1)')
      ),
    ]),
  ],
})
export class ReceiptsHeaderComponent implements OnInit {
  @Output() actionEmitter: EventEmitter<Action> = new EventEmitter();
  @Output() bulkActionEmitter: EventEmitter<Action> = new EventEmitter();

  // Use observable instead of input for metaFields
  customFields$: Observable<MetaFieldsDto[]>;
  customFields: MetaFieldsDto[] = [];

  @Input() refreshTime: Date = new Date();
  @Input() isSearchChanged: boolean = false;
  @Input() receiptsCount: number = 0;

  showAdvanced: boolean = false;
  icon: string = 'expand_more';

  menuItemsAll = [
    {
      label: 'Dodaj novi avans',
      action: () => this.bulkActionEmitter.emit({ action: 'advance' }),
      enabled: true,
    },
    {
      label: 'Finalizuj',
      action: () => this.bulkActionEmitter.emit({ action: 'finalize' }),
      enabled: true,
    },
    {
      label: 'Refundiraj',
      action: () => this.bulkActionEmitter.emit({ action: 'refund' }),
      enabled: true,
    },
  ];

  menuItems: any[] = [];
  hasRefundSelected: boolean = false;
  hasNotFiscalizedSelected: boolean = false;

  metaFieldGroups: FormGroup[] = [];

  constructor(
    public selectData: SelectTypesDataService,
    public formService: FormFiltersService,
    public bulkActionsService: BulkActionsService,
    private translocoService: TranslocoService,
    private route: ActivatedRoute,
    private customFieldsAdapter: CustomFieldsAdapterService,
    private router: Router

  ) {
    this.customFields$ = this.customFieldsAdapter.getMetaFields();
  }

  ngOnInit(): void {

    this.bulkActionsService.emptyAllElements();
    this.bulkActionsService
      .hasRefundElementSelected$()
      .subscribe((hasRefundSelected) => {
        this.hasRefundSelected = hasRefundSelected;
        this.filterMenuItems();
      });
    this.translocoService.langChanges$.subscribe(() => {
      this.translateLabels();
    });

    // Load custom fields and set up form controls
    this.customFields$.pipe(take(1)).subscribe(fields => {
      this.customFields = fields;

      // Clear existing meta field controls
      while (this.formService.getMetaFieldFormArray().length > 0) {
        this.formService.getMetaFieldFormArray().removeAt(0);
      }

      // Add form control for each custom field
      fields.forEach(field => {
        // Create a form control for the field
        const control = this.formService.addMetaFieldControl(field.key);
        this.metaFieldGroups.push(control);
      });
    });

    this.loadQueryData();
  }

  private filterMenuItems() {
    this.hasRefundSelected
      ? (this.menuItems = this.menuItemsAll.slice(0, 2))
      : (this.menuItems = this.menuItemsAll);
  }

  translateLabels() {
    this.translocoService.events$
      .pipe(
        filter(
          (event) =>
            event.type == 'translationLoadSuccess' ||
            event.type == 'langChanged'
        )
      )
      .subscribe(() => {
        const lang = this.translocoService.getActiveLang();
        this.menuItemsAll = [
          {
            label: translate('menu.advance', {}, lang),
            action: () => this.bulkActionEmitter.emit({ action: 'advance' }),
            enabled: true,
          },
          {
            label: translate('menu.finalize'),
            action: () => this.bulkActionEmitter.emit({ action: 'finalize' }),
            enabled: true,
          },
          {
            label: translate('menu.refund'),
            action: () => this.bulkActionEmitter.emit({ action: 'refund' }),
            enabled: true,
          },
        ];
        this.filterMenuItems();
      });
  }

  emitAddAction() { }

  toggleAdvanced() {
    this.showAdvanced = !this.showAdvanced;
    this.showAdvanced
      ? (this.icon = 'expand_less')
      : (this.icon = 'expand_more');

    //
    if (this.showAdvanced) {
      const queryParams = this.route.snapshot.queryParams;

      if (queryParams['metaFields']) {
        this.formService.populateMetaFields(JSON.parse(queryParams['metaFields']));
      }
    }

  }

  private loadQueryData() {
    const queryParams = this.route.snapshot.queryParams;
    const formControls = this.formService.getFiltersForm().controls;
    for (const key in queryParams) {
      if (queryParams.hasOwnProperty(key) && formControls[key]) {
        let value = queryParams[key];

        // Handle metaFields separately
        if (key !== 'metaFields') {

          // Set value for regular form controls
          formControls[key].setValue(value);
        }
      }
    }
  }
}
