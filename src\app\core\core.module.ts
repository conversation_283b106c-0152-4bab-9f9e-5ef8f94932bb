import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Routes } from '@angular/router';

import { SharedModule } from '../shared/shared.module';
import { DashboardComponent } from './dashboard/dashboard.component';
import { AuthGuard } from './guards/auth.guard';
import { HTTP_INTERCEPTORS } from '@angular/common/http';
import { ResponseInterceptor } from './interceptors/response.interceptor';
import { LoadingInterceptor } from './interceptors/loading.interceptor';

const dashboardRoutes: Routes = [
  { path: '', redirectTo: 'auth', pathMatch: 'full' },

  {
    path: '',
    component: DashboardComponent,
    canActivate: [AuthGuard],
    children: [
      // {
      //   path: '',
      //   redirectTo: 'pregled-racuna',
      //   pathMatch: 'full'
      // },
      {
        path: 'pocetna',
        loadChildren: () =>
          import('../modules/home/<USER>').then((m) => m.HomeModule),
      },
      {
        path: 'pregled-racuna',
        loadChildren: () =>
          import('../modules/receipts/receipts.module').then(
            (m) => m.ReceiptsModule
          ),
      },
      {
        path: 'e-fakture',
        loadChildren: () =>
          import('../modules/invoices/invoices.module').then(
            (m) => m.InvoicesModule
          ),
      },
      {
        path: 'merchant-pro',
        loadChildren: () =>
          import('../modules/merchant-pro/merchant-pro.module').then(
            (m) => m.MerchantProModule
          ),
      },
      {
        path: 'placanje',
        loadChildren: () =>
          import('../modules/billing/billing.module').then(
            (m) => m.BillingModule
          ),
      },
      {
        path: 'izvestaji',
        loadChildren: () =>
          import('../modules/reports/reports.module').then(
            (m) => m.ReportsModule
          ),
      },
      {
        path: 'podesavanja',
        loadChildren: () =>
          import('../modules/settings/settings.module').then(
            (m) => m.SettingsModule
          ),
      },
      {
        path: 'proizvodi',
        loadChildren: () =>
          import('../modules/products/products.module').then(
            (m) => m.ProductsModule
          ),
      },
      {
        path: 'tiketi',
        loadChildren: () =>
          import('../modules/tickets/tickets.module').then(
            (m) => m.TicketsModule
          ),
      },
      {
        path: 'kasiri',
        loadChildren: () =>
          import('../modules/cashiers/cashiers.module').then(
            (m) => m.CashiersModule
          ),
      },
      {
        path: 'proizvoljna-polja',
        loadChildren: () =>
          import('../modules/custom-fields/custom-fields.module').then(
            (m) => m.CustomFieldsModule
          ),
      },
    ],
  },
  {
    path: 'auth',
    loadChildren: () =>
      import('../modules/login/login.module').then((m) => m.LoginModule),
  },
];

@NgModule({
  declarations: [DashboardComponent],
  imports: [CommonModule, RouterModule.forChild(dashboardRoutes), SharedModule],
  providers: [
    {
      provide: HTTP_INTERCEPTORS,
      useClass: ResponseInterceptor,
      multi: true,
    },
    {
      provide: HTTP_INTERCEPTORS,
      useClass: LoadingInterceptor,
      multi: true,
    },
  ],
})
export class CoreModule {}
