#homepage {
  background-color: #dbebfb;
  // padding: 0 15px;
  // width should be
  min-height: 100vh;

  .home-container {
    width: 80%;
    margin: 0 auto;
  }
}

.welcome,
.box {
  text-align: center;
  padding: 25px;
  border-radius: 8px;
  background: #dbebfb;

  &.box {
  }

  h1 {
    font-weight: bolder;
    font-size: 2.5rem;
    background: -webkit-linear-gradient(left, #066689, #008bff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  p {
    text-align: justify;
    margin-bottom: 10px;
  }
}

.btn {
  font-weight: 600;
  border: #008bff solid 2px;
}

.border-warp {
  position: relative;
  border-radius: 10px;
  height: 100%;
  //background: red;

  border: 1px solid #008bff;
  padding: 2px;

  &.home-title {
    background: linear-gradient(to right, #066689, #008bff);
    margin: 20px;
  }
}

#col {
  height: 100%;
  margin-top: 0;
}

#col .box {
  height: 100%;
}

.row {
  width: 100%;
}

@media (min-width: 992px) {
  #col .box {
    height: 100%;
    .btn {
      position: absolute;
      bottom: 30px;
    }
  }
}

@media (min-width: 576px) {
}

.company-details {
  text-align: left;

  h4 {
    color: #066689;
    font-weight: 600;
    background: -webkit-linear-gradient(left, #066689, #008bff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 10px;
  }

  .info-item {
    margin-bottom: 8px;

    strong {
      margin-right: 5px;
      color: #066689;
      font-weight: 600;
    }

    span {
      font-weight: 500;
      color: #3a3a3a;
    }
  }
}
