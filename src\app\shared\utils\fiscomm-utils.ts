import { Observable } from 'rxjs';
import {
  Customer,
  MetaFieldsDto,
  Receipts,
} from '../services/backend/receipts/types/receipts-response.dto';

export class FiscommUtils {
  public static formatDate(date?: string) {
    if (!date) return '';

    var d = new Date(date),
      month = '' + (d.getMonth() + 1),
      day = '' + d.getDate(),
      year = d.getFullYear();

    if (month.length < 2) month = '0' + month;
    if (day.length < 2) day = '0' + day;

    return [year, month, day].join('-');
  }

  public static groupBy(key: string, array: any[]) {
    var result: any = [];
    for (var i = 0; i < array.length; i++) {
      var added = false;
      for (var j = 0; j < result.length; j++) {
        if (result[j][key] == array[i][key]) {
          result[j].items.push(array[i]);
          added = true;
          break;
        }
      }
      if (!added) {
        var entry: any = { items: [] };
        entry[key] = array[i][key];
        entry.items.push(array[i]);
        result.push(entry);
      }
    }
    return result;
  }

  private static metaFieldsDictionary: { [key: string]: any } = {};

  static populateDictionary(data: any[]) {
    data.forEach((fields) => {
      if (fields.metaFieldType == 'CUSTOMER')
        fields.values.forEach((customer: Customer) => {
          this.metaFieldsDictionary[
            fields.key + customer.value
          ] = `${customer.customerName} - ${customer.customerEmail} - ${customer.customerJMBG}`;
        });
      else {
        fields.values.forEach(
          (destination: string) =>
            (this.metaFieldsDictionary[fields.key + destination] = destination)
        );
      }
    });
  }

  static getReceiptMeta(key: string, value: string): any {
    return this.metaFieldsDictionary[key + value];
  }

  static includeMetaFieldsInReceipt(receipt: Receipts) {
    receipt.meta_fields.forEach((field) => {
      receipt[field.key] = this.getReceiptMeta(field.key, field.value);
    });
  }

  static removeEmpty(obj: any) {
    for (let prop in obj) {
      if (
        obj[prop] === '' ||
        obj[prop] === 0 ||
        obj[prop] === undefined ||
        obj[prop] === null
      ) {
        delete obj[prop];
      } else if (typeof obj[prop] === 'object') {
        this.removeEmpty(obj[prop]);
      }
    }
  }
}
