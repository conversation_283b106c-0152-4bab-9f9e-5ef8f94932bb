<ng-container *transloco="let t">
  <div class="currency-prices-container">
    <div class="currency-prices-list">
      <div *ngFor="let currencyPrice of currencyPrices; let i = index" class="currency-price-item">
        <div class="currency-info">
          <span class="currency-symbol">{{ getCurrencySymbol(currencyPrice.currencyCode) }}</span>
          <span class="currency-code">{{ currencyPrice.currencyCode }}</span>
          <span class="currency-price">{{ currencyPrice.price | rsdCurrency:(getCurrencySymbol(currencyPrice.currencyCode) + ' '):2:'.':',':3 }}</span>
        </div>
        <button mat-icon-button color="warn" (click)="removeCurrencyPrice(i)" aria-label="Remove currency" class="remove-button">
          <mat-icon>close</mat-icon>
        </button>
      </div>
    </div>

    <form [formGroup]="priceForm" (ngSubmit)="addCurrencyPrice()" class="add-currency-form">
      <div class="form-row">
        <mat-form-field appearance="outline" class="currency-select">
          <mat-label>{{ t('currency') }}</mat-label>
          <mat-select formControlName="currencyCode">
            <mat-option *ngFor="let code of unusedCurrencies" [value]="code">
              {{ getCurrencySymbol(code) }} {{ code }} -
              <ng-container *ngIf="getCurrencyTranslationKey(code) && t(getCurrencyTranslationKey(code)); else defaultName">
                {{ t(getCurrencyTranslationKey(code)) }}
              </ng-container>
              <ng-template #defaultName>
                {{ getCurrencyName(code) }}
              </ng-template>
            </mat-option>
          </mat-select>
          <mat-error *ngIf="priceForm.get('currencyCode')?.invalid">{{ t('validation.required') }}</mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline" class="price-input">
          <mat-label>{{ t('price') }}</mat-label>
          <input matInput type="number" formControlName="price" min="0" step="0.01">
          <mat-error *ngIf="priceForm.get('price')?.invalid">{{ t('validation.required') }}</mat-error>
        </mat-form-field>

        <button mat-mini-fab color="primary" type="submit" [disabled]="!priceForm.valid" class="add-button">
          <mat-icon>add</mat-icon>
        </button>
      </div>
    </form>

    <div *ngIf="currencyPrices.length === 0" class="no-currencies-message">
      {{ t('items.no_additional_currencies') }}
    </div>
  </div>
</ng-container>
