import { HttpHeaders, HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, shareReplay } from 'rxjs';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root',
})
export class ClientService {
  baseURL: string = environment.admin.baseUrl;
  headers = new HttpHeaders().set('intercept-msg', 'Akcija uspela');
  private cachedClients$?: Observable<any>;

  private uid?: string = '';

  constructor(private http: HttpClient) {}

  setUid(uid: string) {
    this.uid = uid;
  }

  getUid() {
    return this.uid;
  }

  getClient(uid?: string): Observable<any> {
    let sendUid = uid;
    if (!sendUid) sendUid = this.getUid();

    if (!this.cachedClients$ || sendUid != this.uid) {
      this.cachedClients$ = this.http
        .get<any>(this.baseURL + '/clients/' + sendUid)
        .pipe(shareReplay(1));
    }
    return this.cachedClients$;
  }

  updateClient(uid: string, client: any, successMsg?: string) {
    let header = new HttpHeaders();
    if (successMsg != undefined)
      header = header.set('intercept-msg', encodeURIComponent(successMsg));
    return this.http.patch(this.baseURL + '/clients/' + uid, client, {
      headers: header,
    });
  }

}
