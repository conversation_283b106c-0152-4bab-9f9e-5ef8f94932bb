import { ComponentFixture, TestBed } from '@angular/core/testing';

import { MultipleOrderFiscalisationDialogComponent } from './multiple-order-fiscalisation-dialog.component';

describe('MultipleOrderFiscalisationDialogComponent', () => {
  let component: MultipleOrderFiscalisationDialogComponent;
  let fixture: ComponentFixture<MultipleOrderFiscalisationDialogComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ MultipleOrderFiscalisationDialogComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(MultipleOrderFiscalisationDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
