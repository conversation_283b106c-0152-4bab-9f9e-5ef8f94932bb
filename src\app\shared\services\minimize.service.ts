import { Injectable } from '@angular/core';
import { DialogService } from '../dialogs/dialog.service';
import { BehaviorSubject, Subject } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class MinimizeService {
  constructor(private dialogService: DialogService) {}

  private minimizedModals: minimizedModals[] = [];
  private minimizedModalsSubject = new BehaviorSubject<minimizedModals[]>([]);
  private minimizedModals$ = this.minimizedModalsSubject.asObservable();

  minimize(
    modalName: string,
    modalType: string,
    data: any,
    minimizedId?: string
  ) {
    minimizedId
      ? this.editModal(minimizedId, modalName, data)
      : this.saveModal(modalName, modalType, data);
    this.minimizedModalsSubject.next(this.minimizedModals);
  }

  private saveModal(modalName: string, modalType: string, data: any) {
    this.minimizedModals.push({
      name: modalName,
      minimizedId: this.generateUniqueId(),
      type: modalType,
      data: data,
    });
  }

  private editModal(minimizedId: string, modalName: string, data: any) {
    let modal = this.minimizedModals.find(
      (el) => el.minimizedId == minimizedId
    );
    if (modal) {
      modal.name = modalName;
      modal.data = data;
    }
  }

  deleteMinimized(minimizedId: string) {
    this.minimizedModals = this.minimizedModals.filter(
      (el) => el.minimizedId != minimizedId
    );
    this.minimizedModalsSubject.next(this.minimizedModals);
  }

  emptyMinimized() {
    this.minimizedModals = [];
    this.minimizedModalsSubject.next(this.minimizedModals);
  }

  getMinimized() {
    return this.minimizedModals;
  }

  getMinimizedObservable() {
    return this.minimizedModals$;
  }

  openMinimized(minimizedId: string) {
    let modal = this.minimizedModals.find(
      (el) => el.minimizedId == minimizedId
    );

    if (!modal) return 'Not found';

    switch (modal?.type) {
      case 'createReceipt':
        return this.dialogService.openCreateReceiptDialog(
          { width: '80%', height: '90%' },
          { ...modal.data, minimizedId }
        );
        break;
      default:
        return 'Invalid type';
        break;
    }
  }

  private generateUniqueId(): string {
    const timestamp = new Date().getTime().toString(16);
    const randomString = Math.random().toString(16).substring(2);
    const uniqueId = timestamp + randomString;
    return uniqueId;
  }
}

interface minimizedModals {
  name: string;
  type: string;
  minimizedId: string;
  data: any[];
}
