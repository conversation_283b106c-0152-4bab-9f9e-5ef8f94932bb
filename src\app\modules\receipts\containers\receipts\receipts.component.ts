import { Component, OnInit, LOCALE_ID, Inject } from '@angular/core';
import { Sort } from '@angular/material/sort';
import { Observable, catchError, map, pipe, take, tap } from 'rxjs';
import { ReceiptsService } from 'src/app/shared/services/backend/receipts/receipts.service';
import { ReceiptsQueryParams } from 'src/app/shared/services/backend/receipts/types/receipts-request.dto';
import {
  Receipts,
  MetaFieldsDto,
} from 'src/app/shared/services/backend/receipts/types/receipts-response.dto';
import { FormFiltersService } from '../../services/form-filters.service';
import { DialogService } from 'src/app/shared/dialogs/dialog.service';
import { FormCreateReceiptService } from '../../services/form-create-receipt.service';
import { FiscommUtils } from 'src/app/shared/utils/fiscomm-utils';
import { UserSettingsService } from 'src/app/shared/services/backend/user/user-settings.service';
import { formatDate } from '@angular/common';
import { PaginatorEvent } from 'src/app/shared/components/fiscomm-paginator/fiscomm-paginator.component';
import { AuthService } from 'src/app/core/services/auth.service';
import { ActivatedRoute, Router } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { BreakpointObserver } from '@angular/cdk/layout';
import { CustomFieldsAdapterService } from '../../services/custom-fields-adapter.service';

@Component({
  selector: 'app-receipts',
  templateUrl: './receipts.component.html',
  styleUrls: ['./receipts.component.scss'],
})
export class ReceiptsComponent implements OnInit {
  //meta
  metaFields$: Observable<MetaFieldsDto[]>;
  metaFields: MetaFieldsDto[] = [];

  //paging info
  perPage: number = 10;
  pageIndex: number = 0;
  pageSizeOptions = [10, 15, 20];
  to: number = 10;

  //receipts
  receipts$: Observable<Receipts[]> = new Observable();
  receipts: any = undefined;
  params: ReceiptsQueryParams = {
    page: this.pageIndex + 1,
    perPage: this.perPage,
  };
  searchParams: ReceiptsQueryParams = {};

  //receipts user columns
  userColumns$: Observable<string[]> = new Observable();

  //data time
  refreshTime: Date = new Date();

  //action
  actionData = null;

  //seach
  isSearchChanged: boolean = false;

  receiptsCount: number = 0;
  uid: any;

  openInNew: boolean = true;

  constructor(
    private receiptsService: ReceiptsService,
    private userColumnsService: UserSettingsService,
    private filterFormservice: FormFiltersService,
    private createFormService: FormCreateReceiptService,
    private dialogService: DialogService,
    private authService: AuthService,
    private router: Router,
    private route: ActivatedRoute,
    private http: HttpClient,
    private breakpointObserver: BreakpointObserver,
    private customFieldsAdapter: CustomFieldsAdapterService,
    @Inject(LOCALE_ID) public locale: string
  ) {
    this.metaFields$ = this.customFieldsAdapter.getMetaFields();
  }

  ngOnInit(): void {
    this.route.queryParams.pipe(take(1)).subscribe((params) => {
      // remove metafields from params
      console.log(params);

      let metaFields = [];
      if (params['metaFields']) {
        metaFields = JSON.parse(params['metaFields']);
      }
      this.params = {
        ...params,
        metaFields: metaFields,
        page: params['page'] || 1,
        perPage: params['perPage'] || 10,
      };
      this.getReceipts();
    });

    this.authService
      .getUserId()
      .pipe(take(1))
      .subscribe((uid: any) => {
        this.uid = uid;
        this.getReceiptsCount();
      });

    this.getMetaFields();
    this.getUserColumns();
  }

  private getMetaFields() {
    if (this.params.metaFields) {
      this.filterFormservice.populateMetaFields(this.params.metaFields);
      console.log(this.params.metaFields);
    }
    this.metaFields$.pipe(
      take(1),
      tap((fields) => {
        this.metaFields = fields;
        while (this.filterFormservice.getMetaFieldFormArray().length > 0) {
          this.filterFormservice.getMetaFieldFormArray().removeAt(0);
        }

        fields.forEach((field) => this.setupMetaFields(field));
      })
    ).subscribe();

    this.filterFormservice
      .getFiltersForm()
      .valueChanges.subscribe((x) => (this.isSearchChanged = true));
  }

  private setupMetaFields(field: MetaFieldsDto) {
    this.filterFormservice.addMetaFieldControl(field.key);
  }

  private getReceipts() {
    this.receipts = undefined;
    this.refreshTime = new Date();
    this.receiptsService
      .getReceipts({ ...this.params })
      .subscribe((receiptsDto) => {
        receiptsDto.data.map((receipt) =>
          FiscommUtils.includeMetaFieldsInReceipt(receipt)
        );
        const params = this.params;
        FiscommUtils.removeEmpty(params);

        this.router.navigate([], {
          queryParams: {
            ...params,
            // for meta fields, need to stringify the array
            metaFields: JSON.stringify(params.metaFields),
          }
        });
        this.receipts = receiptsDto.data;
        return receiptsDto.data;
      });
  }

  private getReceiptsCount() {
    this.receiptsService
      .getReceiptsCount({ ...this.searchParams, uid: this.uid })
      .subscribe((response: number) => {
        this.receiptsCount = response;
      });
  }

  private getUserColumns() {
    this.userColumns$ = this.userColumnsService.getColumnSettings();
  }

  private search() {
    const formData = this.filterFormservice.getFiltersForm().value;
    FiscommUtils.removeEmpty(formData);
    formData.metaFields = formData.metaFields.filter((field: any) => {
      if (field.value) return field;
    });
    if (formData.date_before)
      formData.date_before = formatDate(
        formData.date_before,
        'yyyy-MM-dd',
        this.locale
      );
    if (formData.date_after)
      formData.date_after = formatDate(
        formData.date_after,
        'yyyy-MM-dd',
        this.locale
      );
    this.searchParams = { ...formData };
    this.params = {
      perPage: this.params.perPage,
      page: 1,
      ...this.searchParams,
    };
    this.getReceipts();
    this.isSearchChanged = false;
  }

  handlePageEvent(e: PaginatorEvent) {
    this.params.page = e.page;
    this.params.perPage = e.perPage;
    this.getReceipts();
  }

  handleActions(action: Action) {
    switch (action.action.toLowerCase()) {
      case 'copy':
        this.sendDataToDialog(action.data);
        break;
      case 'advance':
        this.sendDataToDialog(action.data, 'Advance');
        break;
      case 'finalize':
        this.sendDataToDialog(action.data, 'Advance', 'Finalize');
        break;
      case 'refund':
        this.sendDataToDialog(
          action.data,
          action.data?.invoice_type.invoice_type,
          'Refund'
        );
        break;
      case 'add receipt':
        this.openReceiptDialog();
        break;
      case 'refresh':
        this.getReceipts();
        break;
      case 'search':
        this.search();
        break;
    }
  }

  handleBulkActions(action: Action) {
    switch (action.action.toLowerCase()) {
      case 'advance':
        this.openBulkActionDialog('advance');
        break;
      case 'finalize':
        this.openBulkActionDialog('finalize');
        break;
      case 'refund':
        this.openBulkActionDialog('refund');
        break;
    }
  }

  private openBulkActionDialog(data: any) {
    const dialogRef = this.dialogService.openBulkActionDialog(
      {
        width: '80%',
        height: '90%',
      },
      data
    );
  }

  private sendDataToDialog(data: any, type?: string, transactionType?: string) {
    this.createFormService.setReceiptFormData(data, type, transactionType);
    this.openReceiptDialog();
  }

  openReceiptDialog() {
    let dialogConfig = {
      width: '80%',
      height: '90%',
    };

  
    this.breakpointObserver
      .observe(['(max-width: 576px)'])
      .subscribe((result) => {
        if (result.matches) {
          dialogConfig.width = '95%';
        }
      });
    
      const dialogRef =
        this.dialogService.openCreateReceiptDialog(dialogConfig);
      this.afterCloseReceipt(dialogRef);
  }

  private afterCloseReceipt(dialogRef: any) {
    dialogRef.afterClosed().subscribe((createdReceipt: any) => {
      if (createdReceipt) {
        this.params.page = 1;
        if (
          this.createFormService.openInNew.value &&
          createdReceipt.invoicePdfUrl
        ) {
          this.dialogService.openPdfDialog(
            { width: '80%', height: '83vh' },
            { url: createdReceipt.invoicePdfUrl }
          );
        }
        setTimeout(() => {
          this.getReceipts();
        }, 1000);
      }
      this.createFormService.resetForm();
    });
  }

  sortData(event: Sort) {}

  ngOnDestroy() {}
}

export interface Action {
  action: string;
  data?: Receipts;
}
