<ng-container *transloco="let t">
  <h2 mat-dialog-title>
    {{ isEditMode ? t('cashiers.edit_cashier') : t('cashiers.add_cashier') }}
  </h2>

  <mat-dialog-content>
    <form [formGroup]="cashierForm">
      <app-fiscomm-input
        [placeholder]="t('cashiers.name')"
        [control]="nameControl">
      </app-fiscomm-input>

      <app-fiscomm-input
        [placeholder]="t('cashiers.cashier_code')"
        [control]="cashierCodeControl">
      </app-fiscomm-input>

      <app-fiscomm-input
        [placeholder]="t('cashiers.notes')"
        [control]="notesControl">
      </app-fiscomm-input>

      <div class="status-toggle">
        <mat-label class="me-3">{{ t('cashiers.status') }}:</mat-label>
        <mat-slide-toggle formControlName="active" color="primary">
          {{ cashierForm.get('active')?.value ? t('cashiers.active') : t('cashiers.inactive') }}
        </mat-slide-toggle>
      </div>
    </form>
  </mat-dialog-content>

  <mat-dialog-actions class="buttons mt-4">
    <app-success-btn
      [disabled]="cashierForm.invalid"
      (click)="onSubmit()"
      [text]="isEditMode ? t('save') : t('create')"
      class="me-5">
    </app-success-btn>
    <app-danger-btn
      (click)="onCancel()"
      [text]="t('cancel')">
    </app-danger-btn>
  </mat-dialog-actions>
</ng-container>
