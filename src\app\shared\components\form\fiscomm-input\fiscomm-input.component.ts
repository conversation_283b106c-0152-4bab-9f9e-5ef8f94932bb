import {
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
} from '@angular/core';
import { FormControl } from '@angular/forms';
import { MatAutocomplete } from '@angular/material/autocomplete';
import { MatFormFieldAppearance } from '@angular/material/form-field';

@Component({
  selector: 'app-fiscomm-input',
  templateUrl: './fiscomm-input.component.html',
  styleUrls: ['./fiscomm-input.component.scss'],
})
export class FiscommInputComponent implements OnInit {
  @Input() readOnly: boolean = false;
  @Input() placeholder?: string = '';
  @Input() tooltip: string = '';
  @Input() type: string = 'text';
  @Input() prefix: string = '';
  @Input() suffix: string = '';
  @Input() decimals: number = Infinity;
  @Input() suffixIcon: string = '';
  @Input() appearance: MatFormFieldAppearance = 'outline';
  @Input() control: FormControl = new FormControl('');
  @Input() autoComplete?: MatAutocomplete;
  @Input() max: any;
  @Input() loading: boolean = false;
  @Input() label?: string = '';
  @Input() required?: string = '';

  @Output() imageChange: EventEmitter<any> = new EventEmitter();

  constructor(private cdr: ChangeDetectorRef) {}

  ngOnInit(): void {
    this.cdr.detectChanges();
  }
}
