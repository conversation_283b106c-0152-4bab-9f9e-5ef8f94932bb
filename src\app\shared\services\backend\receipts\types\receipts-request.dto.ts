import { FormGroup } from '@angular/forms';
import { Item, MetaFiledReceipt } from './receipts-response.dto';

export type ReceiptsQueryParams = {
  invoiceNumberPos?: string;
  invoiceNumber?: string;
  page?: number;
  invoiceType?: string;
  transactionType?: string;
  paymentType?: string;
  perPage?: number;
  isFinalized?: boolean;
  metaFields?: MetaFiledReceipt[];
  Putnik?: string;
  Putovanje?: string;
  date_before?: Date;
  date_after?: Date;
};

export interface CreateReceipt {
  cashier?: string;
  buyerId?: string;
  buyerCostCenterId?: string;
  sdcDateTime?: string | Date;
  payment: Payment[];
  invoiceNumber?: string;
  invoiceNumberPos?: any;
  items: Item[];
  invoiceType: string;
  transactionType: string;
  referentDocumentNumber?: string;
  referentDocumentDT?: string;
  paidByAdvance?: number;
  metaFields: MetaField[];
  advanceChain?: any[];
  form?: FormGroup;
}

export interface Payment {
  amount: number;
  paymentType: string;
  paidByAdvance?: number;
  paymentTypeFriendlyName?: string;
}
interface MetaField {
  key?: string;
  value?: string;
}
