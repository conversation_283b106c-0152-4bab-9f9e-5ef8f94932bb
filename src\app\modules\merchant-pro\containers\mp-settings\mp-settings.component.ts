import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { AngularFireAuth } from '@angular/fire/compat/auth';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { Observable } from 'rxjs';
import { DialogService } from 'src/app/shared/dialogs/dialog.service';
import { MerchantProService } from 'src/app/shared/services/backend/merchant-pro/merchant-pro.service';
import { TaxService } from 'src/app/shared/services/backend/system/tax.service';

@Component({
  selector: 'app-mp-settings',
  templateUrl: './mp-settings.component.html',
  styleUrls: ['./mp-settings.component.scss']
})
export class MpSettingsComponent implements OnInit {

  fiscalisationTriggers = [{
    label: 'Samo Status plaćanja: Plaćeno',
    value: 'paid'
  }, {
    label: 'Samo poslata faktura',
    value: 'invoice'
  }, {
    label: 'Status plaćanja: Plaćeno i poslata faktura',
    value: 'paid_and_invoice'
    }]
  
  user: any;
  selectedFiscalisationTrigger = this.fiscalisationTriggers[0];
  merchantProForm: FormGroup = undefined as any;
  taxRatesOptions: any[] = [];
  taxRates: any[] = [];
  taxRates$: Observable<any> = new Observable;
  formInitalized: boolean = false;

  constructor(private authService: AngularFireAuth, private mpService: MerchantProService,
  private taxService:TaxService, private dialogService:DialogService) { 
    
  }

  ngOnInit(): void {
    this.initializeTaxAndSettings();
  }

  private initializeTaxAndSettings() {
    this.taxService.getTaxRates$().subscribe(response => {
      this.setTaxRates(response);
      this.initalizeUserSettings();
    });
  }

  private setTaxRates(taxRates: any) {
    this.taxRates = taxRates;
    this.taxRatesOptions = taxRates.map((el:any) => ({
      value: el.value.label,
      label:el.label
    }));
  }

  private initalizeUserSettings() {
    this.authService.onAuthStateChanged((user) => {
      this.user = user;
      if (this.user) {
        this.mpService.getSettings({ uid: this.user.uid }).then((settings$) => {
          settings$.subscribe((settings: any) => this.initalizeForm(settings));
        })
      }
    });
  }

  private initalizeForm(settings: any) {
    this.selectedFiscalisationTrigger = this.fiscalisationTriggers.find(t => t.value == settings.fiscalisationTrigger) || this.fiscalisationTriggers[0];
    const labelSetting = settings.defaultProductsLabel[0].label;
    this.merchantProForm = new FormGroup({
      isActive: new FormControl(settings.isActive, [Validators.required]),
      apiKey: new FormControl(settings.apiKey, [Validators.required]),
      baseUrl: new FormControl(settings.baseUrl, [Validators.required]),
      apiSecret: new FormControl(settings.apiSecret, [Validators.required]),
      fiscalisationTrigger: new FormControl(this.selectedFiscalisationTrigger.value, [Validators.required]),
      isEmailActive: new FormControl(false),
      shopTitle: new FormControl(settings.shopTitle, [Validators.required]),
      defaultProductsLabel: new FormControl(labelSetting,[Validators.required])
    })
    this.formInitalized = true;
  }

  getControl(value:string){
    return this.merchantProForm.get(value) as FormControl;
  }

  async updateSettings() {
    await this.mpService.updateSettings({
      uid: this.user.uid,
      settings: {
        ...this.merchantProForm.value,
        defaultProductsLabel:[this.findLabelValue(this.merchantProForm.value.defaultProductsLabel)]
      }
    })
  }

  private findLabelValue(label: string) {
    return this.taxRates.find(el => el.value.label == label).value;
  }

  // openEmailTemplatedialog() {
  //   this.dialogService.openEmailTemplateDialog();
  // }

  
}
