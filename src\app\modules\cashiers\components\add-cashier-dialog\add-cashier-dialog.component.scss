*{
    text-align: left;
}

.buttons{
    width: 100%;
    display: flex;
    justify-content: center;
}

.status-toggle {
    display: flex;
    align-items: center;
    margin: 1rem 0;
}

.dialog-container {
  min-width: 350px;

  h2 {
    margin-bottom: 20px;
  }

  .full-width {
    width: 100%;
  }

  .status-toggle {
    display: flex;
    align-items: center;
    margin: 20px 0;

    mat-label {
      margin-right: 15px;
      color: rgba(0, 0, 0, 0.6);
    }
  }

  [mat-dialog-actions] {
    margin-top: 20px;
    padding: 8px 0;
  }
}

/* Keep the buttons centered */
.mat-dialog-actions {
    text-align: center;
}
