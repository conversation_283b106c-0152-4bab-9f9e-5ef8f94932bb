import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Routes } from '@angular/router';
import { SharedModule } from 'src/app/shared/shared.module';
import { CustomFieldsModule } from '../custom-fields/custom-fields.module';
import { ReceiptsComponent } from './containers/receipts/receipts.component';
import { ReceiptsTableComponent } from './components/receipts-table/receipts-table.component';
import { RowExpandedDetailsComponent } from './components/row-expanded-details/row-expanded-details.component';
import { ReceiptsHeaderComponent } from './components/receipts-header/receipts-header.component';
import { CreateReceiptDialogComponent } from './components/create-receipt-dialog/create-receipt-dialog.component';
import { MetaFieldInputComponent } from './components/meta-field-input/meta-field-input.component';
import { CreateReceiptItemsComponent } from './components/create-receipt-items/create-receipt-items.component';
import { BulkActionDialogComponent } from '../../shared/components/bulk-action-dialog/bulk-action-dialog.component';
import { MatTooltipModule } from '@angular/material/tooltip';
import { DragDropModule } from '@angular/cdk/drag-drop';

const routes: Routes = [
  {
    path:'',
    component: ReceiptsComponent
  },
];

@NgModule({
  declarations: [
    ReceiptsComponent,
    ReceiptsTableComponent,
    RowExpandedDetailsComponent,
    ReceiptsHeaderComponent,
    CreateReceiptDialogComponent,
    MetaFieldInputComponent,
    CreateReceiptItemsComponent,
    BulkActionDialogComponent
  ],
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    SharedModule,
    MatTooltipModule,
    DragDropModule
  ]
})
export class ReceiptsModule { }
