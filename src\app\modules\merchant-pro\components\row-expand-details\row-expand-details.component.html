<mat-tab-group *transloco="let t">
  <mat-tab label="{{ t('items_menu') }}" class="tab">
    <table class="table" *transloco="let t">
      <thead>
        <th>{{ t("name") }}</th>
        <th>{{ t("amounts") }}</th>
        <th>{{ t("price") }}</th>
        <th>{{ t("discount") }}</th>
        <th>{{ t("price_discounted") }}</th>
        <th>{{ t("total_price") }}</th>
      </thead>
      <tbody>
        <tr *ngFor="let el of items">
          <ng-container *ngIf="el.item_type == 'product'">
            <td>{{ el.name }}</td>
            <td>{{ el.quantity }}</td>
            <td>
              {{
                el.unit_price_gross | rsdCurrency : "RSD " : 2 : "." : "," : 3
              }}
            </td>
            <td class="empty">/</td>
            <td>
              {{
                el.unitPriceGrossWithDiscount
                  | rsdCurrency : "RSD " : 2 : "." : "," : 3
              }}
            </td>
            <td>
              {{
                el.unit_price_gross | rsdCurrency : "RSD " : 2 : "." : "," : 3
              }}
            </td>
          </ng-container>
          <ng-container *ngIf="el.item_type == 'coupon'">
            <td>{{ el.product_name }}</td>
            <td>{{ el.quantity }}</td>
            <td class="empty">/</td>
            <td>
              {{
                el.unit_price_gross | rsdCurrency : "RSD " : 2 : "." : "," : 3
              }}
            </td>
            <td class="empty">/</td>
            <td>
              {{
                el.unit_price_gross | rsdCurrency : "RSD " : 2 : "." : "," : 3
              }}
            </td>
          </ng-container>
        </tr>
        <tr *ngIf="order.wallet_amount">
          <td>{{ t("from_wallet") }}:</td>
          <td class="empty">/</td>
          <td class="empty">/</td>
          <td>
            {{ order.wallet_amount | rsdCurrency : "RSD " : 2 : "." : "," : 3 }}
          </td>
          <td class="empty">/</td>
          <td>
            {{ order.wallet_amount | rsdCurrency : "RSD " : 2 : "." : "," : 3 }}
          </td>
        </tr>
        <tr>
          <td>{{ t("shipping") }}:</td>
          <td>1</td>
          <td>
            {{
              order.shipping_amount || 0
                | rsdCurrency : "RSD " : 2 : "." : "," : 3
            }}
          </td>
          <td class="empty">/</td>
          <td class="empty">/</td>
          <td>
            {{
              order.shipping_amount || 0
                | rsdCurrency : "RSD " : 2 : "." : "," : 3
            }}
          </td>
        </tr>
        <tr>
          <td>{{ t("total") }}:</td>
          <td></td>
          <td></td>
          <td></td>
          <td></td>
          <td>
            {{ order.total_amount | rsdCurrency : "RSD " : 2 : "." : "," : 3 }}
          </td>
        </tr>
      </tbody>
    </table>
  </mat-tab>
  <mat-tab label="Firma" class="tab">
    <div class="d-flex mt-2">
      <div>
        <div *ngFor="let field of orderFields.slice(0, 6)">
          <p>
            <b>{{ field.friendlyName }}</b
            >: {{ order[field.key] }}
          </p>
        </div>
      </div>
      <div class="ms-4">
        <div *ngFor="let field of orderFields.slice(6)">
          <p>
            <b>{{ field.friendlyName }}</b
            >: {{ order[field.key] }}
          </p>
        </div>
      </div>
    </div>
  </mat-tab>
</mat-tab-group>
