<div
  class="container-fluid ps-lg-5 pe-lg-5 pt-lg-4 pb-lg-4 ps-md-3 pe-lmd-3 pt-md-2 pb-md-2"
  *transloco="let t"
>
  <app-page-title text="{{ t('settings') }}"></app-page-title>
  <app-settings-navbar></app-settings-navbar>
  <app-settings-container title="{{ t('dashboard.e_invoices') }}">
    <ng-container content>
      <div class="row">
        <div class="col-md-12">
          <p>API {{ t("settings_page.key") }} (DEMO)</p>
          <textarea
            [value]="eInvoicesInfo.demoApiKey"
            (change)="demoApiKeyChanged($event)"
            placeholder="{{ t('settings_page.input.receipt_text') }}"
          ></textarea>
        </div>
      </div>
      <div class="row">
        <div class="col-md-12">
          <p>API {{ t("settings_page.key") }}</p>
          <textarea
            [value]="eInvoicesInfo.apiKey"
            (change)="apiKeyChanged($event)"
            placeholder="{{ t('settings_page.input.receipt_text') }}"
          ></textarea>
        </div>
      </div>
    </ng-container>
    <ng-container buttons>
      <app-success-btn
        text="{{ t('settings_page.save_changes') }}"
        (click)="saveEInvoicesSettings()"
      ></app-success-btn>
    </ng-container>
  </app-settings-container>
</div>
