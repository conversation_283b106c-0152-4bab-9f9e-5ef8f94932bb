.table-container {
  width: 100%;
  overflow-y: auto;
  overflow-x: auto;
}

table {
  margin: 0px !important;
  thead {
    width: 100%;
  }
  th {
    font-family: "Inter" !important;
    font-weight: 600;
    padding-left: 24px;
    font-size: 14px;
    color: #022b3a;
    border-top: 3px solid #e1e5f2;
    border-bottom: 3px solid #e1e5f2;
    height: 40px;
    text-wrap: nowrap !important;
  }
  td {
    padding-left: 24px;
    font-size: 13px;
    color: #022b3a;
    border: none;
    height: 40px;
    text-wrap: nowrap !important;
  }
  .icons {
    display: flex;
    height: 100%;
  }

  .loading {
    position: relative;

    .bar {
      background-color: #e1e5f2;
      height: 20px;
      border-radius: 7px;
      width: 80%;
    }

    &:after {
      position: absolute;
      transform: translateY(-50%);
      top: 50%;
      left: 0;
      content: "";
      display: block;
      width: 100%;
      height: 24px;
      background-image: linear-gradient(
        100deg,
        rgba(255, 255, 255, 0),
        rgba(255, 255, 255, 0.5) 60%,
        rgba(255, 255, 255, 0) 80%
      );
      background-size: 200px 24px;
      background-position: -100px 0;
      background-repeat: no-repeat;
      animation: loading 2s infinite;
    }
  }
}

@keyframes loading {
  40% {
    background-position: 100% 0;
  }
  100% {
    background-position: 100% 0;
  }
}
