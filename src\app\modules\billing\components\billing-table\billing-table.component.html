<div class="table-container" *ngIf="systemColumns">
  <table
    mat-table
    *transloco="let t"
    [dataSource]="data"
    multiTemplateDataRows
    class="billing-table"
  >
    <ng-container matColumnDef="checkbox">
      <th mat-header-cell *matHeaderCellDef>
        <input type="checkbox" (change)="checkUncheckAll()" />
      </th>
      <td mat-cell *matCellDef="let element">
        <input
          type="checkbox"
          class="checkbox"
          [checked]="getCheckbox(element._id)"
          (change)="toggleElement(element)"
        />
      </td>
    </ng-container>

    <ng-container matColumnDef="Broj fakture">
      <th mat-header-cell *matHeaderCellDef>
        {{ t("billing.invoice_number") }}
      </th>
      <td mat-cell *matCellDef="let element">{{ element.id }}</td>
    </ng-container>
    <ng-container matColumnDef="Mesec">
      <th mat-header-cell *matHeaderCellDef>{{ t("billing.month") }}</th>
      <td mat-cell *matCellDef="let element">{{ element.month }}</td>
    </ng-container>
    <ng-container matColumnDef="Godina">
      <th mat-header-cell *matHeaderCellDef>{{ t("billing.year") }}</th>
      <td mat-cell *matCellDef="let element">{{ element.year }}</td>
    </ng-container>
    <ng-container matColumnDef="Iznos">
      <th mat-header-cell *matHeaderCellDef>{{ t("amount") }}</th>
      <td mat-cell *matCellDef="let element">
        <ng-container *ngIf="element.total"
          >{{ element.total | rsdCurrency : "RSD " : 2 : "." : "," : 3 }}
        </ng-container>
        <ng-container *ngIf="!element.total"> \ </ng-container>
      </td>
    </ng-container>
    <ng-container matColumnDef="Due date">
      <th mat-header-cell *matHeaderCellDef>{{ t("billing.due_date") }}</th>
      <td mat-cell *matCellDef="let element">
        {{ element.dueDate | fiscommDate }}
      </td>
    </ng-container>
    <ng-container matColumnDef="Pdf Link">
      <th mat-header-cell *matHeaderCellDef>Link</th>
      <td mat-cell *matCellDef="let element">
        <a target="_blank" href="{{ element.pdfUrl }}">Link</a>
      </td>
    </ng-container>
    <ng-container matColumnDef="Status">
      <th mat-header-cell *matHeaderCellDef>Status</th>
      <td mat-cell *matCellDef="let element">
        <app-pill
          [text]="getStatus(element.status).text"
          [color]="getStatus(element.status).color"
        ></app-pill>
      </td>
    </ng-container>
    <ng-container matColumnDef="Akcija" stickyEnd>
      <th mat-header-cell *matHeaderCellDef>{{ t("actions") }}</th>
      <td mat-cell *matCellDef="let element">
        <mat-icon [matMenuTriggerFor]="menu">more_vert</mat-icon>
        <mat-menu #menu="matMenu">
          <button
            mat-menu-item
            (click)="actionEmitter.emit({ action: 'Download', data: element })"
          >
            {{ t("menu.download") }}
          </button>
          <button
            mat-menu-item
            [disabled]="true"
            (click)="actionEmitter.emit({ action: 'Pay', data: element })"
          >
            {{ t("menu.pay") }}
          </button>
          <button
            [disabled]="true"
            mat-menu-item
            [disabled]="true"
            (click)="actionEmitter.emit({ action: 'Contact', data: element })"
          >
            {{ t("menu.contact") }}
          </button>
        </mat-menu>
      </td>
    </ng-container>
    <ng-container matColumnDef="Details" stickyEnd>
      <th mat-header-cell *matHeaderCellDef></th>
      <td mat-cell *matCellDef="let element">
        <mat-icon (click)="openDetailsDialog(element)">open_in_new</mat-icon>
      </td>
    </ng-container>

    <tr mat-header-row *matHeaderRowDef="systemColumns"></tr>
    <tr
      mat-row
      *matRowDef="let element; columns: systemColumns"
      class="element-row"
      (dblclick)="openDetailsDialog(element)"
    ></tr>
  </table>
</div>
