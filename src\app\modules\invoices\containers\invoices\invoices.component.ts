import { Component, OnInit } from '@angular/core';
import { DialogService } from 'src/app/shared/dialogs/dialog.service';

@Component({
  selector: 'app-invoices',
  templateUrl: './invoices.component.html',
  styleUrls: ['./invoices.component.scss'],
})
export class InvoicesComponent implements OnInit {
  constructor(private dialogService: DialogService) {}

  ngOnInit(): void {}

  openCreateInvoice() {
    this.dialogService.openCreateInvoiceDialog({ width: '80%', height: '80%' });
  }
}
