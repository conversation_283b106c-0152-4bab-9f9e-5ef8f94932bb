import { Component, OnInit } from '@angular/core';
import { PaginatorEvent } from 'src/app/shared/components/fiscomm-paginator/fiscomm-paginator.component';
import { EInvoiceService } from 'src/app/shared/services/eInvoices/e-invoice.service';

@Component({
  selector: 'app-sale-invoices',
  templateUrl: './sale-invoices.component.html',
  styleUrls: ['./sale-invoices.component.scss'],
})
export class SaleInvoicesComponent implements OnInit {
  data: any[] = [];

  searchParams: any = {
    dateFrom: null,
    dateTo: null,
  };

  canSearchInvoices: boolean = false;

  columns = [
    'checkbox',
    'Broj fakture',
    'Tip fakture',
    'Klijent',
    '<PERSON>z<PERSON>',
    'Datum fakture',
    'Datum slanja',
    'Status',
    'Akcija',
    'Details',
  ];

  constructor(private eInvoiceService: EInvoiceService) {}

  ngOnInit(): void {
    this.getSalesInvoices();
  }

  getSalesInvoices() {
    if (!this.canSearch()) return;

    this.searchParams.dateFrom = this.formatDate(this.searchParams.dateFrom);
    this.searchParams.dateTo = this.formatDate(this.searchParams.dateTo);

    this.eInvoiceService
      .getSalesInvoices(this.searchParams)
      .subscribe((data: any) => {
        this.data = data.invoices;
      });
  }

  handleActions(event: any) {
    switch (event.action.toLowerCase()) {
      case 'akcija':
        break;
    }
  }

  loadInvoice(invoiceId?: string) {
    if (!invoiceId || !this.data) return;

    this.eInvoiceService.getSaleById(invoiceId).subscribe((data: any) => {
      if (!this.data) return;

      for (let index = 0; index < this.data.length; index++) {
        if (
          this.data[index].mfinInfo?.salesInvoiceId + '' ==
          data.invoice?.mfinInfo?.salesInvoiceId
        ) {
          this.data[index] = data.invoice;
        }
      }
    });
  }

  dateChanged(event: any) {
    if (event.dateFrom)
      this.searchParams.dateFrom = this.formatDate(event.dateFrom);
    if (event.dateTo) this.searchParams.dateTo = this.formatDate(event.dateTo);
    this.getSalesInvoices();
  }

  startDateChanged(dateString: string) {
    this.searchParams.dateFrom = this.formatDate(dateString);
  }

  canSearch() {
    if (this.searchParams.dateFrom && this.searchParams.dateTo) {
      const dateFrom = new Date(this.searchParams.dateFrom);
      const dateTo = new Date(this.searchParams.dateTo);

      if (dateFrom > dateTo) return false;
      if (dateFrom > new Date()) return false;
      if (dateTo > new Date()) return false;

      const diffTime = Math.abs(dateTo.getTime() - dateFrom.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      if (diffDays > 30) return false;

      return true;
    }

    return false;
  }

  formatDate(date?: string) {
    if (!date) return '';

    var d = new Date(date),
      month = '' + (d.getMonth() + 1),
      day = '' + d.getDate(),
      year = d.getFullYear();

    if (month.length < 2) month = '0' + month;
    if (day.length < 2) day = '0' + day;

    return [year, month, day].join('-');
  }
}
