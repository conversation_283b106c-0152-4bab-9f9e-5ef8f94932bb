import { Component, Input, OnInit } from '@angular/core';
import { TooltipPosition } from '@angular/material/tooltip';

@Component({
  selector: 'app-primary-btn',
  templateUrl: './primary-btn.component.html',
  styleUrls: ['./primary-btn.component.scss'],
})
export class PrimaryBtnComponent implements OnInit {
  @Input() text: string = '';
  @Input() disabled: boolean = false;
  @Input() tooltip: string = '';
  @Input() position: TooltipPosition = 'above';
  @Input() icon?: string = '';

  constructor() {}

  ngOnInit(): void {}
}
