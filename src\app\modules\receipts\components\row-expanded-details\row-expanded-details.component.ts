import { Component, OnInit,Input } from '@angular/core';
import { Item,Receipts } from 'src/app/shared/services/backend/receipts/types/receipts-response.dto';


@Component({
  selector: 'app-row-expanded-details',
  templateUrl: './row-expanded-details.component.html',
  styleUrls: ['./row-expanded-details.component.scss']
})
export class RowExpandedDetailsComponent implements OnInit {

  @Input() items: Item[] = [];
  @Input() receiptsHistory: Receipts[] = [];
  @Input() historyColumns: string[] = [];
  @Input() activeLang: string = 'sr';

  constructor() { }

  ngOnInit(): void {
  }

}
