@import "../../../assets/styles/variables.scss";

::ng-deep {
  .mat-drawer-inner-container {
    overflow-x: hidden;
  }
  #dashboard {
    .mat-form-field-wrapper {
      padding: 0;
      background-color: white;
      border-radius: 8px;
    }
    .mat-form-field {
      font-size: 0.76rem;
      color: black;
    }
  }
  #langSelect {
    .mat-form-field {
      width: 85% !important;
    }

    .mat-form-field-outline:hover {
      opacity: 0 !important;
    }
    .mat-select-value {
      color: white;
    }
    .mat-form-field-wrapper {
      background-color: transparent !important;
    }
    .mat-form-field-wrapper:hover {
      background-color: transparent !important;
    }
    .mat-form-field-outline {
      border-radius: 8px;
      background-color: #e1e5f2;
      opacity: 0.2;
    }
  }
}

.count {
  background-color: #e8edff;
  width: 28px;
  height: 20px;
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
}

// sidenav
.example-sidenav-content {
}

.example-sidenav,
.divider {
  white-space: nowrap;
  background: #094e67;
  overflow: hidden;
  -webkit-transition: width 0.3s ease-in-out;
  -moz-transition: width 0.3s ease-in-out;
  -o-transition: width 0.3s ease-in-out;
  transition: width 0.3s ease-in-out;
}
.active {
  background-color: rgba(9, 30, 66, 0.25);
}

img {
  filter: brightness(0) invert(1);
  max-width: 100%;
}

.no-effect {
  filter: none;
}

.sidenav-item,
.divider {
  font-size: 14px;
  color: #e1e5f2;
  margin: 0;
  margin-left: 10px;

  h5 {
    margin: 0;
    font-size: 14px;
  }
}

.divider {
  margin-right: 10px;
  color: rgba(191, 219, 247, 0.5);
  padding-bottom: 7px;

  border-bottom: 2px solid rgba(191, 219, 247, 0.5);
}

.resizable {
  -webkit-transition: margin 0.3s ease-in-out;
  -moz-transition: margin 0.3s ease-in-out;
  -o-transition: margin 0.3s ease-in-out;
  transition: margin 0.3s ease-in-out;
}

mat-icon {
  color: white;
}

.responsive-nav {
  padding: 10px;
  z-index: 100;
  width: 100%;
  background-color: #094e67;
  border: 2px solid #094e67;
}

.fiscomm-teal-color {
  // color: $fiscomm-teal;
  // font-weight: bold;
  // font-size: 2rem;
  transform: scale(1.2);
}

.bottom-list {
  position: absolute;
  bottom: 30px;
  width: 100%;
}

.activeMenu {
  background-color: #e1e5f2;
}
