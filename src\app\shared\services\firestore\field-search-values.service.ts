import { Injectable } from '@angular/core';
import { AngularFirestore } from '@angular/fire/compat/firestore';
import { Observable, forkJoin, from, of } from 'rxjs';
import { map, catchError, tap, switchMap, timeout, take } from 'rxjs/operators';
import { ReceiptInfo } from './meta-field-usage.service';

export interface SearchValue {
  value: string;
  valueLower: string;
  createdAt: any;
  receipts?: ReceiptInfo[]; // Will be populated from the receipt-info subcollection
  id?: string; // Document ID in Firestore
}

export interface SearchOptions {
  limit?: number;
  searchTerm?: string;
  includeReceipts?: boolean; // Whether to include receipt info
}

@Injectable({
  providedIn: 'root'
})
export class FieldSearchValuesService {
  private readonly COLLECTION_NAME = 'metaFields';
  private readonly MAX_RESULTS = 100;
  private readonly MAX_RECEIPTS = 50; // Maximum receipts to fetch for a value

  constructor(private firestore: AngularFirestore) {
   }

  /**
   * Gets all search values for a specific meta field
   * @param metaFieldId - The ID of the meta field
   * @deprecated Use querySearchValues instead for better performance
   */
  getSearchValues(metaFieldId: string): Observable<SearchValue[]> {
    if (!metaFieldId) {
      console.warn('No metaFieldId provided for getSearchValues');
      return of([]);
    }

    return this.firestore.collection(this.COLLECTION_NAME)
      .doc(metaFieldId)
      .collection<SearchValue>('search-values')
      .valueChanges()
      .pipe(
        tap(values => console.log(`Retrieved ${values.length} values for field ${metaFieldId}`)),
        map(values => values.sort((a, b) =>
          a.value.toLowerCase().localeCompare(b.value.toLowerCase())
        )),
        catchError(error => {
          console.error('Error fetching search values:', error);
          return of([]);
        })
      );
  }

  /**
   * Queries search values based on user input with pagination
   * @param metaFieldId - The ID of the meta field
   * @param options - Search options (limit, search term, includeReceipts)
   */
  querySearchValues(metaFieldId: string, options: SearchOptions = {}): Observable<SearchValue[]> {
    if (!metaFieldId) {
      console.warn('No metaFieldId provided for querySearchValues');
      return of([]);
    }

    const { limit = this.MAX_RESULTS, searchTerm = '', includeReceipts = true } = options;
    console.log(`Querying values for field ${metaFieldId}, term: "${searchTerm}", limit: ${limit}`);

    let query = this.firestore.collection(this.COLLECTION_NAME)
      .doc(metaFieldId)
      .collection<SearchValue>('search-values', ref => {
        let baseQuery = ref.orderBy('valueLower').limit(limit);

        if (searchTerm && searchTerm.trim() !== '') {
          const term = searchTerm.toLowerCase().trim();
          // Use a range query for prefix search
          return baseQuery
            .where('valueLower', '>=', term)
            .where('valueLower', '<=', term + '\uf8ff');
        }

        return baseQuery;
      });

    return query.snapshotChanges().pipe(
      map(actions => actions.map(a => {
        const data = a.payload.doc.data() as SearchValue;
        const id = a.payload.doc.id;
        return { ...data, id };
      })),
      // If requested, fetch receipt info for each search value
      switchMap(values => {
        if (includeReceipts && values.length > 0) {
          return forkJoin(
            values.map(value => this.getReceiptsForValue(metaFieldId, value))
          );
        }
        return of(values);
      }),
      tap(values => console.log(`Got ${values.length} results for query`)),
      catchError(error => {
        console.error('Error querying search values:', error);
        return of([]);
      })
    );
  }

  /**
   * Fetch receipt info for a search value from the receipt-info subcollection
   * @param metaFieldId - ID of the meta field
   * @param value - The search value to fetch receipts for
   */
  private getReceiptsForValue(metaFieldId: string, value: SearchValue): Observable<SearchValue> {
    if (!value.id) {
      return of(value);
    }
    console.log('Fetching receipts for value:', value);
    return this.firestore.collection(this.COLLECTION_NAME)
      .doc(metaFieldId)
      .collection('search-values')
      .doc(value.id)
      .collection<ReceiptInfo>('receipt-info', ref => ref.limit(this.MAX_RECEIPTS))
      .valueChanges()
      .pipe(
        // Add timeout to prevent infinite loading
        timeout(5000),
        map(receipts => ({
          ...value,
          receipts
        })),
        catchError(error => {
          console.error(`Error fetching receipts for value ${value.value}:`, error);
          // Return the value without receipts to prevent loading forever
          return of({
            ...value,
            receipts: []
          });
        }),
        // Ensure the observable completes
        take(1)
      );
  }

  /**
   * Gets distinct values for autocomplete suggestions
   * @param metaFieldId - The ID of the meta field
   * @param searchTerm - The search term to filter values
   * @param limit - Maximum number of suggestions to return
   */
  getAutocompleteSuggestions(metaFieldId: string, searchTerm: string, limit = 5): Observable<string[]> {
    return this.querySearchValues(metaFieldId, { searchTerm, limit }).pipe(
      map(values => values.map(v => v.value))
    );
  }
}
