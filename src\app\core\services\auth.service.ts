import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { AngularFireAuth } from '@angular/fire/compat/auth';
import { Observable, map, from, switchMap, catchError, throwError } from 'rxjs';
import { environment } from 'src/environments/environment';
import firebase from 'firebase/compat/app';

@Injectable({
  providedIn: 'root',
})
export class AuthService {
  signInWithCustomToken(adminToken: string | null) {
    return this.fireAuth.signInWithCustomToken(adminToken ?? '');
  }
  private user$: Observable<firebase.User | null>;

  baseUrl = environment.mainFiscomm.baseUrlMain;

  constructor(private fireAuth: AngularFireAuth, private http: HttpClient) {
    this.user$ = fireAuth.authState;
  }

  signIn(email: string, password: string) {
    return this.fireAuth.signInWithEmailAndPassword(email, password);
  }

  signOut() {
    return this.fireAuth.signOut().then(this.clearCache);
  }

  private clearCache() {
    // Clearing cached images and files
    caches.keys().then((cacheNames) => {
      for (const cacheName of cacheNames) {
        caches.delete(cacheName);
      }
    });

    // Clearing browser storage
    localStorage.clear();
    sessionStorage.clear();

    // Force a reload to ensure the updated cache is used
    window.location.reload();
  }

  get isAuthenticated(): Observable<boolean> {
    return this.user$.pipe(map((user: any) => !!user));
  }

  getUserId(): Observable<string | null> {
    return this.user$.pipe(map((user: any) => (user ? user.uid : null)));
  }

  resetPassword(email: any) {
    return this.http.post(this.baseUrl + '/change-password/request', {
      email: email,
    });
  }

  confirmResetPassword(code: string, password: string) {
    return this.fireAuth.confirmPasswordReset(code, password);
  }

  /**
   * Changes the user's password
   * @param currentPassword The user's current password
   * @param newPassword The new password to set
   * @returns An Observable that completes when the password is changed
   */
  changePassword(currentPassword: string, newPassword: string): Observable<any> {
    return from(this.fireAuth.currentUser).pipe(
      switchMap(user => {
        if (!user) {
          return throwError(() => new Error('User not authenticated'));
        }

        // Create credential with current email and password
        const credential = firebase.auth.EmailAuthProvider.credential(
          user.email as string,
          currentPassword
        );

        // Re-authenticate the user
        return from(user.reauthenticateWithCredential(credential)).pipe(
          switchMap(() => from(user.updatePassword(newPassword))),
          catchError(error => {
            console.error('Password update error:', error);

            // Handle specific Firebase auth errors
            if (error.code === 'auth/wrong-password') {
              return throwError(() => ({ code: 'auth/wrong-password' }));
            } else if (error.code === 'auth/requires-recent-login') {
              return throwError(() => ({ code: 'auth/requires-recent-login' }));
            }

            return throwError(() => error);
          })
        );
      }),
      catchError(error => {
        console.error('Auth error:', error);
        return throwError(() => error);
      })
    );
  }
}
