<mat-sidenav-container fullscreen *transloco="let t" id="dashboard">
  <mat-sidenav #sidenav [mode]="isMobile ? 'over' : 'side'" class="example-sidenav"
    [ngStyle]="{ 'width.em': sidenavWidth }" [opened]="sideNavOpened">
    <mat-nav-list class="">
      <mat-list-item (click)="openFiscommLanding()" [class]="
          sidenavWidth < 9
            ? 'd-flex flex-column align-items-center mb-4'
            : 'mb-4'
        ">
        <img src="assets/fiscomm-e-light.png" class="img-fluid no-effect" width="40px" alt="" />
        <div fxFlex="10"></div>
        <div *ngIf="sidenavWidth > 9" class="sidenav-item d-flex justify-content-between w-100">
          <div class="col-10">
            <img src="assets/fiscomm-light.png" class="img-fluid no-effect" alt="" />
          </div>
        </div>
      </mat-list-item>

      <mat-list-item [class]="
          sidenavWidth < 9 ? 'd-flex flex-column align-items-center ' : ''
        " routerLinkActive="active" (click)="toggleSidenav('navigation')" routerLink="pocetna">
        <mat-icon>home</mat-icon>
        <div fxFlex="10"></div>
        <div *ngIf="sidenavWidth > 9" class="sidenav-item">
          <h5>{{ t("dashboard.home") }}</h5>
        </div>
      </mat-list-item>

      <div [class]="
          sidenavWidth < 9
            ? 'd-flex justify-content-center divider '
            : 'divider '
        ">
        <small *ngIf="sidenavWidth > 9" class="text-center w-100">{{
          t("dashboard.receipts")
          }}</small>
      </div>

      <mat-list-item [class]="
          sidenavWidth < 9 ? 'd-flex flex-column align-items-center ' : ''
        " routerLinkActive="active" (click)="toggleSidenav('navigation')" routerLink="pregled-racuna">
        <mat-icon>receipt</mat-icon>
        <div fxFlex="10"></div>
        <div *ngIf="sidenavWidth > 9" class="sidenav-item">
          <h5>{{ t("dashboard.receipts_page") }}</h5>
        </div>
      </mat-list-item>

      <mat-list-item [class]="
          sidenavWidth < 9 ? 'd-flex flex-column align-items-center ' : ''
        " routerLinkActive="active" (click)="toggleSidenav('navigation')" routerLink="izvestaji">
        <mat-icon>bar_chart</mat-icon>
        <div fxFlex="10"></div>
        <div *ngIf="sidenavWidth > 9" class="sidenav-item">
          <h5>{{ t("dashboard.reports") }}</h5>
        </div>
      </mat-list-item>


      <mat-list-item [class]="
          sidenavWidth < 9 ? 'd-flex flex-column align-items-center ' : ''
        " routerLinkActive="active" (click)="toggleSidenav('navigation')" routerLink="proizvodi">
        <mat-icon>inventory_2</mat-icon>
        <div fxFlex="10"></div>
        <div *ngIf="sidenavWidth > 9" class="sidenav-item">
          <h5>{{ t("dashboard.products") }}</h5>
        </div>
      </mat-list-item>

      <mat-list-item [class]="
          sidenavWidth < 9 ? 'd-flex flex-column align-items-center ' : ''
        " routerLinkActive="active" (click)="toggleSidenav('navigation')" routerLink="kasiri">
        <mat-icon>people</mat-icon>
        <div fxFlex="10"></div>
        <div *ngIf="sidenavWidth > 9" class="sidenav-item">
          <h5>{{ t("dashboard.cashiers") }}</h5>
        </div>
      </mat-list-item>

      <mat-list-item [class]="
          sidenavWidth < 9 ? 'd-flex flex-column align-items-center ' : ''
        " routerLinkActive="active" (click)="toggleSidenav('navigation')" routerLink="proizvoljna-polja">
        <mat-icon>view_list</mat-icon>
        <div fxFlex="10"></div>
        <div *ngIf="sidenavWidth > 9" class="sidenav-item">
          <h5>{{ t("dashboard.custom_fields") }}</h5>
        </div>
      </mat-list-item>

      <!-- NEXT_VERSION -->
      <!--
      <div
        [class]="
          sidenavWidth < 9
            ? 'd-flex justify-content-center divider '
            : 'divider '
        "
      >
        <small *ngIf="sidenavWidth > 9" class="text-center w-100"
          >Fakture</small
        >
      </div>
      <mat-list-item
        [class]="
          sidenavWidth < 9 ? 'd-flex flex-column align-items-center ' : ''
        "
        routerLinkActive="active"
        (click)="toggleSidenav('navigation')"
        routerLink="e-fakture"
      >
        <mat-icon>description</mat-icon>
        <div fxFlex="10"></div>
        <div *ngIf="sidenavWidth > 9" class="sidenav-item">
          <h5>E-fakture</h5>
        </div>
      </mat-list-item> -->

      <ng-container *ngIf="currentUser?.integrationType">
        <div [class]="
            sidenavWidth < 9
              ? 'd-flex justify-content-center divider '
              : 'divider '
          ">
          <small *ngIf="sidenavWidth > 9" class="text-center w-100">Integracije</small>
        </div>

        <mat-list-item *ngIf="currentUser.integrationType == 'merchant-pro'" [class]="
            sidenavWidth < 9 ? 'd-flex flex-column align-items-center ' : ''
          " (click)="toggleSidenav('navigation')" routerLink="merchant-pro" routerLinkActive="active">
          <mat-icon>store</mat-icon>
          <div fxFlex="10"></div>
          <div *ngIf="sidenavWidth > 9" class="sidenav-item">
            <h5>Merchant Pro</h5>
          </div>
        </mat-list-item>
      </ng-container>
      <div [class]="
          sidenavWidth < 9
            ? 'd-flex justify-content-center divider '
            : 'divider '
        ">
        <small *ngIf="sidenavWidth > 9" class="text-center w-100">Podrška</small>
      </div>

      <!-- <mat-list-item
        [class]="
          sidenavWidth < 9 ? 'd-flex flex-column align-items-center ' : ''
        "
        routerLinkActive="active"
        (click)="toggleSidenav('navigation')"
        routerLink="placanje"
      >
        <mat-icon>payment</mat-icon>
        <div fxFlex="10"></div>
        <div *ngIf="sidenavWidth > 9" class="sidenav-item">
          <h5>{{ t("dashboard.payments") }}</h5>
        </div>
      </mat-list-item> -->

      <!-- tiketi -->
      <!-- <mat-list-item
        [class]="
          sidenavWidth < 9 ? 'd-flex flex-column align-items-center ' : ''
        "
        routerLinkActive="active"
        (click)="toggleSidenav('navigation')"
        routerLink="tiketi"
      >
        <mat-icon>support</mat-icon>
        <div fxFlex="10"></div>
        <div *ngIf="sidenavWidth > 9" class="sidenav-item">
          <h5>Tiketi</h5>
        </div>
        <span class="ms-auto count">{{ ticketCount }}</span>
      </mat-list-item> -->

      <mat-list-item (click)="toggleSidenav('navigation')" routerLink="podesavanja" [class]="
          sidenavWidth < 9 ? 'd-flex flex-column align-items-center ' : ''
        " routerLinkActive="active">
        <mat-icon>settings</mat-icon>
        <div fxFlex="10"></div>
        <div *ngIf="sidenavWidth > 9" class="sidenav-item">
          <h5>{{ t("dashboard.settings") }}</h5>
        </div>
      </mat-list-item>

      <div [class]="
          sidenavWidth < 9
            ? 'd-flex justify-content-center divider '
            : 'divider '
        "></div>

      <mat-list-item class="d-flex justify-content-center" *ngIf="sidenavWidth < 9" (click)="toggleSidenav()">
        <mat-icon class="fiscomm-teal-color">chevron_right</mat-icon>
      </mat-list-item>
      <mat-list-item class="d-flex justify-content-center" *ngIf="sidenavWidth > 9" (click)="toggleSidenav()">
        <mat-icon>chevron_left</mat-icon>
      </mat-list-item>
    </mat-nav-list>

    <mat-nav-list class="">
      <mat-list-item [class]="
                sidenavWidth < 9 ? 'd-flex flex-column align-items-center ' : ''
              " (click)="openCreateReceiptDialog()">
        <mat-icon>add_circle</mat-icon>
        <div fxFlex="10"></div>
        <div *ngIf="sidenavWidth > 9" class="sidenav-item">
          <h5>{{ t("dashboard.create_receipt") }}</h5>
        </div>
      </mat-list-item>
      <mat-list-item [class]="
          sidenavWidth < 9 ? 'd-flex flex-column align-items-center ' : ''
        ">
        <div class="d-flex flex-row align-items-center">
          <mat-icon [matMenuTriggerFor]="menu">translate</mat-icon>
          <mat-menu #menu="matMenu" [class]="sidenavWidth < 9 ? 'd-block' : 'd-none'">
            <button mat-menu-item *ngFor="let lang of languages" [class]="lang.value == activeLang ? 'activeMenu' : ''"
              (click)="setLanguageMenu(lang.value)">
              {{ lang.label }}
            </button>
          </mat-menu>
          <div *ngIf="sidenavWidth > 9" class="sidenav-item" id="langSelect">
            <app-fiscomm-select [control]="languageControl" [options]="languages"></app-fiscomm-select>
          </div>
        </div>
      </mat-list-item>

      <mat-list-item [ngClass]="
          sidenavWidth < 9 ? 'd-flex flex-column align-items-center ' : ''
        " (click)="signOut()" routerLinkActive="active">
        <mat-icon>logout</mat-icon>
        <div fxFlex="10"></div>
        <div *ngIf="sidenavWidth > 9" class="sidenav-item">
          <h5>{{ t("dashboard.log_out") }}</h5>
        </div>
      </mat-list-item>
    </mat-nav-list>
  </mat-sidenav>

  <mat-sidenav-content id="sidenav-content" class="resizable" [ngStyle]="{ 'margin-left.em': sidenavWidth }">
    <div class="responsive-nav d-flex align-items-center" *ngIf="isMobile" (click)="toggleNavigation()">
      <mat-icon class="fiscomm-teal-color">menu</mat-icon>
    </div>
    <div>
      <router-outlet></router-outlet>
    </div>
  </mat-sidenav-content>
</mat-sidenav-container>
