<div class="dialog-container" *transloco="let t">
  <h2 mat-dialog-title>{{ t('receipts.send_to_email') }}</h2>
  <mat-dialog-content>
    <form [formGroup]="emailForm">
      <mat-form-field appearance="outline" class="w-100">
        <mat-label>{{ t('common.email') }}</mat-label>
        <input matInput formControlName="email" type="email" required>
        <mat-error *ngIf="emailForm.get('email')?.hasError('required')">
          {{ t('common.email_required') }}
        </mat-error>
        <mat-error *ngIf="emailForm.get('email')?.hasError('email')">
          {{ t('common.invalid_email') }}
        </mat-error>
      </mat-form-field>
    </form>
  </mat-dialog-content>
  <mat-dialog-actions align="end">
    <button mat-button (click)="onCancel()" [disabled]="loading">
      {{ t('common.cancel') }}
    </button>
    <app-success-btn
      [disabled]="emailForm.invalid || loading"
      (click)="onSubmit()"
      [text]="t('common.send')"

    >
    </app-success-btn>
  </mat-dialog-actions>
</div>
