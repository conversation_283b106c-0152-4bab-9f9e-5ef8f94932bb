import { Injectable } from '@angular/core';
import {
  HttpRequest,
  <PERSON>ttpHandler,
  HttpInterceptor
} from '@angular/common/http';
import { Auth } from '@angular/fire/auth';
import { from,lastValueFrom } from 'rxjs';

@Injectable()
export class FiscommAuthInterceptor implements HttpInterceptor {

  // baseUrl = environment.main.baseUrl;
  constructor(private auth: Auth) { }

  intercept(req: HttpRequest<any>, next: <PERSON>ttp<PERSON>and<PERSON>) {

    return from(this.handle(req, next))
  }

  async handle(req: HttpRequest<any>, next: HttpHandler) {

    const user = this.auth.currentUser;
    const API_KEY = await user?.getIdToken();

    return lastValueFrom(next.handle(req.clone({
      // url: `${this.baseUrl}/${req.url}`,
      setHeaders: { Authorization: `Bearer ${API_KEY}` }
    })))
  }
}
