.form-group {
  margin-bottom: 1rem;
  width: 100%;
}

.full-width {
  width: 100%;
}

.possible-value-row {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;

  app-fiscomm-input {
    flex: 1;
  }

  button {
    margin-left: 0.5rem;
  }
}

h3 {
  margin-bottom: 1rem;
  font-size: 1rem;
  font-weight: 500;
}

.default-value-checkbox {
  margin-top: 1rem;
  display: block;
}

.checkbox-group {
  margin-top: 15px;
}

mat-checkbox {
  margin-bottom: 0.5rem;
  display: block;
  padding: 5px 0;
}

::ng-deep .mat-checkbox-inner-container {
  margin-right: 10px !important;
}

::ng-deep .mat-checkbox-layout {
  white-space: normal !important;
}

::ng-deep .mat-checkbox-label {
  font-size: 14px;
  line-height: 1.4;
}

mat-dialog-content {
  min-height: 300px;
  max-height: 70vh;
  overflow-y: auto;
}

::ng-deep .mat-dialog-container {
  padding: 24px;
}
