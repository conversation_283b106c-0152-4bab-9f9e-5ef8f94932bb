import { Component, Inject } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { ReceiptsService } from 'src/app/shared/services/backend/receipts/receipts.service';

@Component({
  selector: 'app-send-receipt-email-dialog',
  templateUrl: './send-receipt-email-dialog.component.html',
  styleUrls: ['./send-receipt-email-dialog.component.scss']
})
export class SendReceiptEmailDialogComponent {
  emailForm: FormGroup;
  loading = false;

  constructor(
    private fb: FormBuilder,
    private dialogRef: MatDialogRef<SendReceiptEmailDialogComponent>,
    private receiptsService: ReceiptsService,
    @Inject(MAT_DIALOG_DATA) public data: { receiptId: string }
  ) {
    this.emailForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]]
    });
  }

  onSubmit() {
    if (this.emailForm.valid) {
      this.loading = true;
      this.receiptsService
        .sendReceiptToEmail(this.data.receiptId, this.emailForm.get('email')?.value)
        .subscribe({
          next: () => {
            this.dialogRef.close(true);
          },
          error: (error) => {
            console.error('Error sending email:', error);
            this.loading = false;
          }
        });
    }
  }

  onCancel() {
    this.dialogRef.close();
  }
}
