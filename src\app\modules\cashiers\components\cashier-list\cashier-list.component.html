<div class="table-container" *transloco="let t">
  <div *ngIf="!cashiers || cashiers.length === 0" class="no-data">
    <p>{{ t('cashiers.no_cashiers') }}</p>
  </div>

  <table mat-table [dataSource]="cashiers" class="cashiers-table mat-elevation-z2" *ngIf="cashiers && cashiers.length > 0">
    <!-- Name Column -->
    <ng-container matColumnDef="name">
      <th mat-header-cell *matHeaderCellDef>{{ t('cashiers.name') }}</th>
      <td mat-cell *matCellDef="let cashier">{{ cashier.name }}</td>
    </ng-container>

    <!-- Cashier Code Column -->
    <ng-container matColumnDef="cashierCode">
      <th mat-header-cell *matHeaderCellDef>{{ t('cashiers.cashier_code') }}</th>
      <td mat-cell *matCellDef="let cashier">{{ cashier.cashierCode || '-' }}</td>
    </ng-container>

    <!-- Status Column -->
    <ng-container matColumnDef="status">
      <th mat-header-cell *matHeaderCellDef>{{ t('cashiers.status') }}</th>
      <td mat-cell *matCellDef="let cashier">
        <span class="status-badge" [ngClass]="{ 'active': cashier.active, 'inactive': !cashier.active }">
          {{ cashier.active ? t('cashiers.active') : t('cashiers.inactive') }}
        </span>
      </td>
    </ng-container>

    <!-- Notes Column -->
    <ng-container matColumnDef="notes">
      <th mat-header-cell *matHeaderCellDef>{{ t('cashiers.notes') }}</th>
      <td mat-cell *matCellDef="let cashier">{{ cashier.notes || '-' }}</td>
    </ng-container>

    <!-- Created Date Column -->
    <ng-container matColumnDef="createdAt">
      <th mat-header-cell *matHeaderCellDef>{{ t('cashiers.created_at') }}</th>
      <td mat-cell *matCellDef="let cashier">{{ cashier.createdAt | date }}</td>
    </ng-container>

    <!-- Actions Column -->
    <ng-container matColumnDef="actions">
      <th mat-header-cell *matHeaderCellDef></th>
      <td mat-cell *matCellDef="let cashier">
        <mat-icon class="edit-icon" style="margin-right: 15px; cursor: pointer;" (click)="actionEmitter.emit({action:'edit',data:cashier})">edit</mat-icon>
        <mat-icon *ngIf="defaultCashierName === cashier.name" class="star-icon" style="margin-right: 15px; cursor: pointer;" (click)="onToggleDefault(cashier)">star</mat-icon>
        <mat-icon *ngIf="defaultCashierName !== cashier.name" class="star-outline-icon" style="margin-right: 15px; cursor: pointer;" (click)="onToggleDefault(cashier)">star_border</mat-icon>
        <mat-icon class="delete-icon" style="cursor: pointer;" (click)="actionEmitter.emit({action:'delete',data:cashier})">delete</mat-icon>
      </td>
    </ng-container>

    <tr mat-header-row *matHeaderRowDef="displayColumns"></tr>
    <tr mat-row *matRowDef="let row; columns: displayColumns;" class="element-row"></tr>
  </table>
</div>
