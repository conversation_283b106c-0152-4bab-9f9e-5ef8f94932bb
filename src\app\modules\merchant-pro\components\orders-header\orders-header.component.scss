.buttons{
    background-color: white;
    padding-top: 8px;
    padding-bottom: 8px;
    border-bottom: solid #E1E5F2 3px;
    div{
        margin-top: 5px;
        p{
            margin-bottom: 0;
            margin-left: 8px;
            font-size:13px;
            text-wrap: nowrap;
        }

        span.icon{
            cursor:pointer;
            border-radius: 8px;
            width: 50px;
            height: 40px;
            background-color: #D5DCF2;
            .mat-icon{
                color:#205372;
            }
            display: flex;
            justify-content: center;
            align-items: center;
        }
        span.icon:hover{
            background-color: #d5dcf2ae;  
        }
    }
}

.header{
    ::ng-deep{
        .mat-form-field{
            font-size: 12px !important;
        }
        .mat-form-field-appearance-outline .mat-form-field-outline{
            background-color:#E1E5F2
        }
        .mat-chip{
            font-size: 12px !important;
        }
    }  
}


.btn{
    background-color:#008BFF;
    border: none;
    cursor: pointer;
    color: white;
    justify-content: center;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    padding: 0rem 1rem;
    border-radius: 0.25rem;
    height: 2.6rem;
    width: 100%;
}