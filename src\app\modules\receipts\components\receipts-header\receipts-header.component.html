<div class="container-fluid filters" id="receiptsHeader" *transloco="let t">
  <form>
    <div class="row align-items-center">
      <div class="col-lg-2">
        <app-fiscomm-input
          appearance="outline"
          placeholder="{{ t('receipts.input.receipt_number') }}"
          [control]="formService.getInvoiceNumberPos()"
        >
        </app-fiscomm-input>
      </div>
      <div class="col-lg-2">
        <app-fiscomm-input
          appearance="outline"
          placeholder="Broj računa"
          [control]="formService.getInvoiceNumber()"
        >
        </app-fiscomm-input>
      </div>
      <div class="col-lg-2">
        <app-fiscomm-select
          placeholder="{{ t('receipts.input.receipt_type') }}"
          [enableNone]="true"
          [options]="selectData.getInvoiceTypes()"
          [control]="formService.getInvoiceType()"
        >
        </app-fiscomm-select>
      </div>
      <div class="col-lg-2">
        <app-fiscomm-select
          placeholder="{{ t('receipts.input.transaction_type') }}"
          [enableNone]="true"
          [options]="selectData.getTransactionTypes()"
          [control]="formService.getTransactionType()"
        >
        </app-fiscomm-select>
      </div>
      <div class="col-lg-2">
        <app-fiscomm-select
          placeholder="{{ t('receipts.input.payment_type') }}"
          [enableNone]="true"
          [options]="selectData.getPaymentTypes()"
          [control]="formService.getPaymentType()"
        >
        </app-fiscomm-select>
      </div>
      <div class="col-lg-2 datePicker">
        <mat-form-field appearance="outline" class="w-100">
          <mat-label>{{ t("receipts.input.date_from_to") }}:</mat-label>
          <mat-date-range-input [rangePicker]="picker">
            <input
              matStartDate
              placeholder="Start date"
              [formControl]="formService.getDateAfter()"
            />
            <input
              matEndDate
              placeholder="End date"
              [formControl]="formService.getDateBefore()"
            />
          </mat-date-range-input>
          <mat-datepicker-toggle
            matIconSuffix
            [for]="picker"
          ></mat-datepicker-toggle>
          <mat-date-range-picker #picker></mat-date-range-picker>
        </mat-form-field>
      </div>
      <div
        class="col-lg-12 d-flex justify-content-center mt-2"
        id="advanced"
        *ngIf="customFields.length > 0"
      >
        <span class="d-flex" (click)="toggleAdvanced()"
          ><p>{{ t("receipts.additonal_filters") }}</p>
          <mat-icon>{{ icon }}</mat-icon></span
        >
      </div>
    </div>
    <div
      class="row expandRow"
      [@collapse]="showAdvanced ? 'expanded' : 'collapsed'"
    >
      <ng-container *ngIf="showAdvanced">
        <div class="col-lg-3" *ngFor="let field of customFields; let i = index">
          <app-field-value-autocomplete
            [fieldId]="field.key"
            [placeholder]="field.name || field.key"
            [formControl]="formService.getMetaFieldControl(field.key)"
            [limit]="20"
          >
          </app-field-value-autocomplete>
        </div>
      </ng-container>
    </div>
    <div class="row align-items-center buttons">
      <div class="col-lg-2 col-md-6">
        <app-success-btn
          text="{{ t('search') }}"
          [disabled]="!isSearchChanged"
          icon="search"
          (click)="actionEmitter.emit({ action: 'Search' })"
        ></app-success-btn>
      </div>
      <div class="col-lg-2 col-md-6">
        <app-expand-btn
          text="{{ t('actions') }}"
          [disabled]="!this.bulkActionsService.getIsEnoughSelected()"
          [menuItems]="menuItems"
        ></app-expand-btn>
      </div>
      <div class="col-lg-4">
        <app-success-btn
          icon="add"
          text="{{ t('receipts.new_receipt') }}"
          (click)="actionEmitter.emit({ action: 'Add receipt' })"
        ></app-success-btn>
      </div>

      <div class="col-lg-2 col-md-6 d-flex">
        <span class="icon" (click)="actionEmitter.emit({ action: 'Refresh' })">
          <mat-icon>update</mat-icon>
        </span>
        <span>
          <p>{{ t("updated") }} :</p>
          <p>{{ refreshTime | fiscommDate }}</p>
        </span>
      </div>
      <!-- NEXT_VERSION -->
      <!-- <div
        class="col-lg-2 col-md-6 d-flex align-items-center justify-content-end"
      >
        <input type="checkbox" />
        <p>Trening mod</p>
      </div> -->
    </div>
  </form>
  <p class="mt-4 mb-0">
    Ukupan broj racuna : {{ receiptsCount }} (osvežava se jednom dnevno)
  </p>
</div>
