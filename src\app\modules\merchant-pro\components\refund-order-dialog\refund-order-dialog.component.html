
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12 d-flex justify-content-end">
            <button class="x-button" mat-dialog-close>
                <mat-icon >close</mat-icon>
              </button>
        </div>
    </div>

    <div mat-dialog-content id="test">
        <div class="col-md-7" id="leftPart">
            <form class="row" id="identification" [formGroup]="getRefundForm()">
                <div class="col-md-6">
                    <app-fiscomm-select [control]="getControl('buyerIdPrefix')" placeholder="Izaberite tip dokumenta" [options]="types"></app-fiscomm-select>
                </div>
                <div class="col-md-6">
                    <app-fiscomm-input [control]="getControl('buyerIdSufix')" placeholder="Unesite broj dokumenta"></app-fiscomm-input>
                </div>
            </form>
            <div class="row">
                <div class="col-md-12">
                    <table >
                        <thead>
                            <th>Naziv</th>
                            <th><PERSON><PERSON><PERSON></th>
                            <th>Cena</th>
                            <th>Cena ukupno</th>
                        </thead>
                        <tbody>
                            <tr *ngFor="let item of data.line_items;let i = index">
                                <input type="hidden" [formControl]="getItemControl(i,'name')">
                                <input type="hidden" [formControl]="getItemControl(i,'labels')">
                                <td>{{item.name}}</td>
                                <td>
                                    <app-fiscomm-input type="number" 
                                    placeholder="Original: {{item.quantity}}"
                                    [control]="getItemControl(i,'quantity')"></app-fiscomm-input>
                                </td>
                                <td>
                                    <app-fiscomm-input type="number" 
                                    placeholder="Original: {{item.unit_price_gross}}" suffix="RSD"
                                    [control]="getItemControl(i,'price')"></app-fiscomm-input>
                                </td>
                                <td>
                                    <app-fiscomm-input type="number" 
                                    placeholder="Original: {{item.unit_price_gross * item.quantity}}" suffix="RSD"
                                    [control]="getItemControl(i,'totalAmount')"></app-fiscomm-input>
                                </td>
                            </tr>
                            <tr>
                                <input type="hidden" [formControl]="getItemControl(data.line_items.length,'name')">
                                <input type="hidden" [formControl]="getItemControl(data.line_items.length,'labels')">
                                <td>Isporuka:</td>
                                <td>
                                    <app-fiscomm-input type="number" 
                                    placeholder="Original: {{1}}"
                                    [control]="getItemControl(data.line_items.length,'quantity')"></app-fiscomm-input>
                                </td>
                                <td>
                                    <app-fiscomm-input type="number" 
                                    placeholder="Original: {{data.shipping_amount}}" suffix="RSD"
                                    [control]="getItemControl(data.line_items.length,'price')"></app-fiscomm-input>
                                </td>
                                <td>
                                    <app-fiscomm-input type="number" 
                                    placeholder="Original: {{data.shipping_amount}}" suffix="RSD"
                                    [control]="getItemControl(data.line_items.length,'totalAmount')"></app-fiscomm-input>
                                </td>
                            </tr>
                        </tbody>
                        <tfoot>
                            <td>UKUPNO:</td>
                            <td colspan="2"></td>
                            <td class="d-flex justify-content-end">{{getTotalPrice() | rsdCurrency:'RSD ':2:'.':',':3 }}</td>
                        </tfoot>
                    </table>
                </div>
            </div>
            <div class="row refundRow">
                <div class="col-md-12"><button (click)="createRefund()">REFUNDIRAJ</button></div> 
            </div>
        </div>
        <div class="col-md-6">
            
        </div>
    </div>

  </div>
  