<div class="col-12 col-md-12 d-flex flex-column justify-content-center align-items-center" *transloco="let t">
    <form *ngIf="loginForm" [formGroup]="loginForm" class="form w-100 d-flex flex-column  justify-content-center align-items-center" (submit)="login()">
        <app-fiscomm-input appearance="outline" class="w-100 input" placeholder="email"[control]="getEmailControl()"></app-fiscomm-input>
        <app-fiscomm-input appearance="outline" class="w-100 input" type="password" placeholder="password" [control]="getPasswordControl()"></app-fiscomm-input>
        <button type="submit" class="w-50 m-auto mt-4 btn-primary" mat-raised-button>Login</button>
        <p class="text-center mt-4 p-0 m-0">{{t('login_page.forgot_password')}}</p>
        <a class="text-center" (click)="openResetDialog()">{{t('login_page.pass_reset')}}</a>
    </form>
</div>

