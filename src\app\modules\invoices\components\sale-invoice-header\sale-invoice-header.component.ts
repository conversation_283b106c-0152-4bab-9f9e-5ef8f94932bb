import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { FormControl } from '@angular/forms';

@Component({
  selector: 'app-sale-invoice-header',
  templateUrl: './sale-invoice-header.component.html',
  styleUrls: ['./sale-invoice-header.component.scss'],
})
export class SaleInvoiceHeaderComponent implements OnInit {
  today = new Date();
  dateFromCtrl: FormControl = new FormControl();
  dateToCtrl: FormControl = new FormControl();

  @Output() dateChange: EventEmitter<any> = new EventEmitter();
  constructor() {}

  ngOnInit(): void {
    this.dateFromCtrl.valueChanges.subscribe((value) =>
      this.dateChange.emit({ dateFrom: value })
    );
    this.dateToCtrl.valueChanges.subscribe((value) =>
      this.dateChange.emit({ dateTo: value })
    );
  }
}
