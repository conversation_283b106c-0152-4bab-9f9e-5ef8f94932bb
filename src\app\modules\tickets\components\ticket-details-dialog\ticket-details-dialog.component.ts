import { Component, Inject, OnInit } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
import { TicketService } from 'src/app/shared/services/backend/tickets/ticket.service';

@Component({
  selector: 'app-ticket-details-dialog',
  templateUrl: './ticket-details-dialog.component.html',
  styleUrls: ['./ticket-details-dialog.component.scss'],
})
export class TicketDetailsDialogComponent implements OnInit {
  comments: any[] = [];
  requester: any;
  textControl: FormControl = new FormControl();

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: any,
    private ticketService: TicketService
  ) {}

  ngOnInit(): void {
    this.getTicket();
  }

  getTicket() {
    this.ticketService.getTicket(this.data.id).subscribe((response: any) => {
      this.comments = response.data.comments.map((comment: any) => {
        return {
          ...comment,
          body: this.findAndLinkifyUrls(this.findAndAddMailTo(comment.body)),
        };
      });
      this.requester = response.data.requester;
    });
  }

  findAndLinkifyUrls(text: string): string {
    // Regular expression to match links in the text
    const urlRegex = /https?:\/\/[^\s/$.?#].[^\s]*/g;
    return text.replace(
      urlRegex,
      (url) => `<a href="${url}" target="_blank">${url}</a>`
    );
  }

  findAndAddMailTo(text: string): string {
    // Regular expression to match email addresses in the text
    const emailRegex = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g;
    return text.replace(
      emailRegex,
      (email) => `<a href="mailto:${email}">${email}</a>`
    );
  }

  addComment() {
    this.ticketService
      .postComment(this.data.id, this.textControl.value)
      .subscribe(() => {
        this.textControl.setValue('');
        this.getTicket();
      });
  }
}
