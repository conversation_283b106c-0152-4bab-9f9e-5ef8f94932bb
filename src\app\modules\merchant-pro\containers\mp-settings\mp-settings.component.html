<div class="container-fluid p-lg-5 p-md-3">
  <div class="row nav">
    <app-mp-navbar></app-mp-navbar>
  </div>
  <div class="row mainRow" *transloco="let t">
    <div class="container-fluid mainContainer" *ngIf="formInitalized">
      <form [formGroup]="merchantProForm">
        <div class="row">
          <h5>{{ t("settings_page.base_settings") }}</h5>
          <div class="col-md-4 d-flex align-items-center">
            <input type="checkbox" formControlName="isActive" />
            <p>{{ t("settings_page.activate_integration") }}</p>
          </div>
          <div class="col-md-8">
            <app-fiscomm-input
              [control]="getControl('baseUrl')"
              placeholder="{{ t('settings_page.input.base_link') }}"
            ></app-fiscomm-input>
          </div>
        </div>
        <div class="row">
          <div class="col-md-4 d-flex align-items-center">
            <p>{{ t("input.tax_type") }}</p>
          </div>
          <div class="col-md-8">
            <app-fiscomm-select
              [control]="getControl('defaultProductsLabel')"
              [options]="taxRatesOptions"
              placeholder="{{ t('tax') }}"
            >
            </app-fiscomm-select>
          </div>
        </div>
        <div class="row">
          <h5>{{ t("settings_page.credential_settings") }}</h5>
          <div class="col-md-12">
            <app-fiscomm-input
              [control]="getControl('apiKey')"
              placeholder="{{ t('settings_page.input.api_key') }}"
              type="password"
            >
            </app-fiscomm-input>
          </div>
          <div class="col-md-12">
            <app-fiscomm-input
              [control]="getControl('apiSecret')"
              placeholder="{{ t('settings_page.input.api_secret') }}"
              type="password"
            >
            </app-fiscomm-input>
          </div>
        </div>
        <div class="row">
          <h5>{{ t("settings_page.fiscalization_settings") }}</h5>
          <div class="col-md-12">
            <app-fiscomm-select
              [control]="getControl('fiscalisationTrigger')"
              [options]="fiscalisationTriggers"
              placeholder="{{ t('settings_page.input.fiscalisation_trigger') }}"
            ></app-fiscomm-select>
          </div>
        </div>
        <div class="row">
          <h5>{{ t("settings_page.email_settings") }}</h5>
          <div class="col-md-4 d-flex align-items-center">
            <input type="checkbox" formControlName="isEmailActive" />
            <p>{{ t("settings_page.activate_email") }}</p>
          </div>
          <div class="col-md-8">
            <app-fiscomm-input
              [control]="getControl('shopTitle')"
              placeholder="{{ t('settings_page.store_name') }}"
            ></app-fiscomm-input>
          </div>
        </div>
        <div class="row d-flex justify-content-center mt-5">
          <div class="col-md-6">
            <button (click)="updateSettings()">{{ t("save") }}</button>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>
