<div class="container-fluid" *transloco="let t">
  <!-- Desktop Navigation -->
  <nav
    mat-tab-nav-bar
    [tabPanel]="tabPanel"
    class="centered-links desktop-nav"
    *ngIf="!isMobile">
    <a
      mat-tab-link
      [active]="activeLink == ''"
      routerLink="/podesavanja">
      {{ t("dashboard.base") }}
    </a>
    <a
      mat-tab-link
      [active]="activeLink == 'izvestaji'"
      routerLink="/podesavanja/izvestaji">
      {{ t("dashboard.reports") }}
    </a>
    <a
      mat-tab-link
      [active]="activeLink == 'racuni'"
      routerLink="/podesavanja/racuni">
      {{ t("dashboard.receipts") }}
    </a>
    <a
      mat-tab-link
      [active]="activeLink == 'kasa'"
      routerLink="/podesavanja/kasa">
      Podešavanje kase
    </a>
    <!-- NEXT_VERSION -->
    <!-- <a mat-tab-link
        [active]="activeLink=='e-fakture'" routerLink="/podesavanja/e-fakture">{{t('dashboard.e_invoices')}}</a> -->
    <a
      mat-tab-link
      [active]="activeLink == 'password'"
      routerLink="/podesavanja/password">
      Promena lozinke
    </a>
  </nav>

  <!-- Mobile Navigation -->
  <div class="mobile-nav" *ngIf="isMobile">
    <div class="mobile-nav-header" (click)="toggleDropdown()">
      <div class="active-item">
        <mat-icon class="nav-icon">{{ getActiveItem().icon }}</mat-icon>
        <span class="nav-text">
          {{ getActiveItem().labelKey.includes('.') ? t(getActiveItem().labelKey) : getActiveItem().labelKey }}
        </span>
      </div>
      <mat-icon class="dropdown-arrow" [class.rotated]="isDropdownOpen">
        expand_more
      </mat-icon>
    </div>

    <div class="mobile-dropdown" [class.open]="isDropdownOpen">
      <a
        *ngFor="let item of navigationItems"
        class="dropdown-item"
        [class.active]="activeLink === item.activeValue"
        [routerLink]="item.route"
        (click)="closeDropdown()">
        <mat-icon class="item-icon">{{ item.icon }}</mat-icon>
        <span class="item-text">
          {{ item.labelKey.includes('.') ? t(item.labelKey) : item.labelKey }}
        </span>
      </a>
    </div>
  </div>

  <mat-tab-nav-panel #tabPanel></mat-tab-nav-panel>
</div>
