import { ComponentFixture, TestBed } from '@angular/core/testing';

import { MetaFieldInputComponent } from './meta-field-input.component';

describe('MetaFieldInputComponent', () => {
  let component: MetaFieldInputComponent;
  let fixture: ComponentFixture<MetaFieldInputComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ MetaFieldInputComponent ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(MetaFieldInputComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
