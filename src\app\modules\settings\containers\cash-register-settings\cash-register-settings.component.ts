import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { take } from 'rxjs';
import { AuthService } from 'src/app/core/services/auth.service';
import { Cashier } from 'src/app/models/cashier.model';
import { ClientService } from 'src/app/shared/services/backend/clients/client.service';
import { TaxService } from 'src/app/shared/services/backend/system/tax.service';
import { CashierService } from 'src/app/shared/services/firestore/cashier.service';
import { CommonFirestoreService } from 'src/app/shared/services/firestore/common-firestore.service';
import { SelectTypesDataService } from 'src/app/shared/services/select-types-data.service';

@Component({
  selector: 'app-cash-register-settings',
  templateUrl: './cash-register-settings.component.html',
  styleUrls: ['./cash-register-settings.component.scss'],
})
export class CashRegisterSettingsComponent implements OnInit {
  form: FormGroup = new FormGroup({});
  uid: string = '';
  userData?: any;
  taxRateLabels: any;
  cashiers: Cashier[] = [];

  constructor(
    private fb: FormBuilder,
    private clientService: ClientService,
    private authService: AuthService,
    private commonFirestoreService: CommonFirestoreService,
    public selectDataService: SelectTypesDataService,
    private taxService: TaxService,
    private cashierService: CashierService
  ) {
    this.form = this.fb.group({
      invoiceType: '',
      transactionType: '',
      taxLabel: '',
      receiptFooter: '',
      defaultPaymentMethod: '',
      defaultCashier: null,
    });

    this.getTaxLabels();
    this.loadCashiers();
  }

  ngOnInit(): void {
    this.authService
      .getUserId()
      .pipe(take(1))
      .subscribe((uid: any) => {
        this.uid = uid;
        this.loadSettings();
      });
  }

  private loadCashiers() {
    this.cashierService.getActiveCashiers().subscribe(cashiers => {
      this.cashiers = cashiers;
    });
  }

  private getTaxLabels() {
    this.taxService.getTaxRates$().subscribe((response) => {
      this.taxRateLabels = response.map((el: any) => ({
        value: el.value.label,
        label: el.label,
      }));
    });
  }

  private getUserData() {
    this.clientService.getClient(this.uid).subscribe((client) => {
      this.initalizeSettings(client);
      this.userData = client;
    });
  }

  private initalizeSettings(data: any) {
    if (data.defaultSettings) {
      this.form.get('invoiceType')?.setValue(data.defaultSettings.invoiceType);
      this.form.get('taxLabel')?.setValue(data.defaultSettings.taxLabel);
      this.form
        .get('transactionType')
        ?.setValue(data.defaultSettings.transactionType);
      this.form.get('receiptFooter')?.setValue(data.defaultSettings.receiptFooter);
      this.form.get('defaultPaymentMethod')?.setValue(data.defaultSettings.defaultPaymentMethod);

      if (data.defaultSettings.defaultCashier) {
        this.form.get('defaultCashier')?.setValue(data.defaultSettings.defaultCashier);
      }
    }
  }

  getControl(name: string) {
    return this.form.get(name) as FormControl;
  }

  private loadSettings() {
    this.commonFirestoreService
      .listenToSettingsChange$(this.uid)
      .pipe(take(1))
      .subscribe(() => this.getUserData());
  }

  saveSettings() {
    this.userData.defaultSettings = this.form.value;

    this.clientService
      .updateClient(
        this.uid,
        this.userData,
        'Vaša podešavanja su uspešno sačuvana.'
      )
      .subscribe();
  }

  resetSettings() {
    this.form.reset();
    this.saveSettings();
  }
}
