.table-container {
  width: 100%;
  overflow-x: auto;
}

.custom-fields-table {
  width: 100%;
}

.field-name {
  display: flex;
  align-items: center;

  .field-description {
    margin-left: 8px;
    cursor: help;
  }
}

.status-badge {
  padding: 4px 8px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;

  &.active {
    background-color: #e6f7ed;
    color: #0a8043;
  }

  &.inactive {
    background-color: #f7e8e8;
    color: #d32f2f;
  }
}

.empty-row {
  text-align: center;
  padding: 16px;
  color: rgba(0, 0, 0, 0.54);
}

th.mat-header-cell {
  font-weight: 500;
}

td.mat-cell {
  padding: 12px 8px;
}

.mat-column-required,
.mat-column-status {
  text-align: center;
}

.actions-container {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

// Drag and drop styling
.drag-handle {
  cursor: move;

  .drag-indicator {
    color: rgba(0, 0, 0, 0.54);
  }
}

.draggable-row {
  &:hover {
    background-color: rgba(0, 0, 0, 0.04);
  }
}

.cdk-drag-preview {
  box-sizing: border-box;
  border-radius: 4px;
  box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2),
              0 8px 10px 1px rgba(0, 0, 0, 0.14),
              0 3px 14px 2px rgba(0, 0, 0, 0.12);
  background-color: white;
  opacity: 0.8;
}

.cdk-drag-placeholder {
  opacity: 0.3;
}

.cdk-drag-animating {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.custom-fields-table .cdk-drop-list-dragging .draggable-row:not(.cdk-drag-placeholder) {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}
