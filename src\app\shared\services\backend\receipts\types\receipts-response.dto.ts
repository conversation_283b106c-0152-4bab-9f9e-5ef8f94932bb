export type TaxRatesDto = {
  currentTaxRates: CurrentTaxRates;
  allTaxRates: CurrentTaxRates[];
};

type CurrentTaxRates = {
  validFrom: string;
  groupId: number;
  taxCategories: TaxCategory[];
};

type TaxCategory = {
  name: string;
  categoryType: number;
  taxRates: TaxRate[];
  orderId: number;
};

type TaxRate = {
  rate: number;
  label: string;
};

export type MetaFieldsDto = {
  _id: string;
  uid: string;
  key: string;
  metaFieldType?: string;
  enabled?: boolean;
  values: (Customer | string | string)[];
  name?: string;
  isRequired?: boolean;
};

export type MetaFiledReceipt = {
  id?: string;
  key: string;
  value: string;
  invoice_id?: string;
};

export type Customer = {
  customerJMBG: string;
  customerEmail: string;
  value: string;
  customerName: string;
};

export type ReceiptsDto = {
  current_page: number;
  data: Receipts[];
  first_page_url: string;
  from: number;
  next_page_url: string;
  path: string;
  per_page: number;
  prev_page_url?: any;
  to: number;
};

export type Receipts = {
  id: number;
  invoice_type_id: number;
  transaction_type_id: number;
  journal: string;
  cashier?: any;
  invoice_number: string;
  signed_by: string;
  invoice_counter: string;
  district: string;
  mrc: string;
  tin: string;
  buyer_id?: any;
  uid: string;
  invoice_number_pos?: any;
  referent_document_number?: any;
  tax_group_revision: string;
  invoice_counter_extension: string;
  address: string;
  buyer_cost_center_id?: any;
  requested_by: string;
  business_name: string;
  location_name: string;
  referent_document_dt: string;
  sdc_date_time: string;
  encrypted_internal_data: string;
  signature: string;
  messages: string;
  qr_code_file_url: string;
  invoice_pdf_url: string;
  verification_url: string;
  total_amount: string;
  transaction_type_counter: string;
  total_counter: string;
  created_at: string;
  updated_at?: any;
  invoice_type: Invoicetype;
  transaction_type: Transactiontype;
  items: Item[];
  tax_items: Taxitem[];
  meta_fields: MetaFiledReceipt[];
  payments: Payment[];
  advance_chain: Receipts[];
  finalized: boolean;
  traveler?: Customer;
  destination?: string;
  [key: string]: any;
  requestItems?: any;
};

type Payment = {
  id: number;
  payment_type_id: number;
  invoice_id: number;
  amount: string;
  created_at: string;
  updated_at?: any;
  payment_type: Paymenttype;
};

type Paymenttype = {
  id: number;
  payment_type: string;
  friendly_name: string;
};

type Taxitem = {
  id: number;
  invoice_id: number;
  label: string;
  rate: string;
  category_type: string;
  category_name: string;
  amount: string;
  created_at: string;
  updated_at?: any;
};

export type Item = {
  unitPrice: number;
  totalAmount: number;
  quantity: number;
  name: string;
  labels: string[];
};

type Transactiontype = {
  id: number;
  transaction_type: string;
  friendly_name: string;
};

type Invoicetype = {
  id: number;
  invoice_type: string;
  friendly_name: string;
};

export type ColumnsDto = string[];

export type MetaFieldValueDto = {
  _id: string;
  uid: string;
  key: string;
  metaFieldType: string;
  enabled: boolean;
  values: (MetaFieldValue | string)[];
};

type MetaFieldValue = {
  customerJMBG: string;
  customerEmail: string;
  value: string;
  customerName: string;
};
