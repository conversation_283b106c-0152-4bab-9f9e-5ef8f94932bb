import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { BulkActionsService } from 'src/app/shared/services/bulk-actions.service';

@Component({
  selector: 'app-biling-header',
  templateUrl: './biling-header.component.html',
  styleUrls: ['./biling-header.component.scss'],
})
export class BilingHeaderComponent implements OnInit {
  @Output() actionEmitter: EventEmitter<any> = new EventEmitter();
  @Output() bulkActionEmitter: EventEmitter<any> = new EventEmitter();

  @Input() refreshTime: Date = new Date();
  @Input() isSearchChanged: boolean = false;

  menuItems = [
    {
      label: 'Preuzmi',
      action: () => this.bulkActionEmitter.emit({ action: 'bulk-download' }),
      enabled: true,
    },
  ];

  monthsSelect: any = [
    { value: 1, label: 'Januar' },
    { value: 2, label: 'Februar' },
    { value: 3, label: 'Mart' },
    { value: 4, label: 'April' },
    { value: 5, label: 'Maj' },
    { value: 6, label: 'Jun' },
    { value: 7, label: 'Jul' },
    { value: 8, label: 'Avgust' },
    { value: 9, label: 'Septembar' },
    { value: 10, label: 'Oktobar' },
    { value: 11, label: 'Novembar' },
    { value: 12, label: 'Decembar' },
  ];

  constructor(public bulkActionService: BulkActionsService) {}

  ngOnInit(): void {}
}
