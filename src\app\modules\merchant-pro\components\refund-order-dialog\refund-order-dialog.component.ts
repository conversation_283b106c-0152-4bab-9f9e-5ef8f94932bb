import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
import { SelectTypesDataService } from 'src/app/shared/services/select-types-data.service';
import {
  FormBuilder,
  FormGroup,
  FormControl,
  FormArray,
  Validators,
} from '@angular/forms';
import {
  Lineitem,
  Order,
} from 'src/app/shared/services/backend/merchant-pro/types/orders-response.dto';
import { CreateReceipt } from 'src/app/shared/services/backend/receipts/types/receipts-request.dto';
import { Receipts } from 'src/app/shared/services/backend/receipts/types/receipts-response.dto';
import { ReceiptsService } from 'src/app/shared/services/backend/receipts/receipts.service';
@Component({
  selector: 'app-refund-order-dialog',
  templateUrl: './refund-order-dialog.component.html',
  styleUrls: ['./refund-order-dialog.component.scss'],
})
export class RefundOrderDialogComponent implements OnInit {
  types: any[] = [];
  refundForm: FormGroup = new FormGroup({});
  receipt: Receipts | any;

  constructor(
    private selectDataService: SelectTypesDataService,
    @Inject(MAT_DIALOG_DATA) public data: Order,
    private fb: FormBuilder,
    private receiptService: ReceiptsService
  ) {}

  ngOnInit(): void {
    this.getOrderReceipt();
    this.initalizeForm();
    this.types = this.selectDataService.getBuyerIdDocumentTypes();
    this.updateItemTotal();
  }

  initalizeForm() {
    this.refundForm = this.fb.group({
      items: this.fb.array([]),
      buyerIdPrefix: [
        this.getBuyerIdPrefix(this.receipt.buyer_id),
        Validators.required,
      ],
      buyerIdSufix: [
        this.getBuyerIdSufix(this.receipt.buyer_id),
        Validators.required,
      ],
    });
    this.data.line_items.forEach((item) => {
      this.addItem(item);
    });
    let tmp = this.data.shipping_amount as number;
    this.addItem({ unit_price_gross: tmp, quantity: 1 });
  }

  private getOrderReceipt() {
    this.receipt = this.data.receipts?.find(
      (receipt) =>
        receipt.invoice_type.invoice_type == 'Normal' &&
        receipt.transaction_type.transaction_type == 'Sale'
    );
  }

  updateItemTotal() {
    this.getItemsFormArray().controls.forEach((control) => {
      control
        .get('quantity')
        ?.valueChanges.subscribe(() => this.calculateTotalPrice(control));
      control
        .get('unitPrice')
        ?.valueChanges.subscribe(() => this.calculateTotalPrice(control));
    });
  }

  private calculateTotalPrice(control: any) {
    const item = control.get('totalAmount');
    const quantity = control.get('quantity').value;
    const price = control.get('unitPrice').value;
    item.setValue(quantity * price);
  }

  getRefundForm() {
    return this.refundForm as FormGroup;
  }

  getControl(name: string) {
    return this.refundForm.get(name) as FormControl;
  }

  addItem(item: any) {
    const row = this.fb.group({
      name: this.getItemName(item),
      labels: this.findItemLabel(this.getItemName(item)),
      unitPrice: item.unit_price_gross,
      quantity: item.quantity,
      totalAmount: item.unit_price_gross * item.quantity,
    });
    this.getItemsFormArray().controls.push(row);
  }

  getItemControl(index: number, name: string) {
    const temp = this.getItemsFormArray().controls[index];
    if (temp) return temp.get(name) as FormControl;
    return new FormControl();
  }

  private getItemsFormArray() {
    return this.refundForm.get('items') as FormArray;
  }

  getTotalPrice() {
    let sum = 0;
    this.getItemsFormArray().controls.forEach((item) => {
      sum += parseFloat(item.get('totalAmount')?.value);
    });
    return sum;
  }

  createRefund() {
    const refundDto = this.convertToRefundDto(
      this.receipt,
      this.makeId(),
      this.extractItems()
    );
    this.receiptService.createReceipt(refundDto).subscribe();
  }

  private getItemName(item: any) {
    if (!item.name) return 'Isporuka';
    return item.name;
  }

  private extractItems() {
    let items: any = [];
    this.getItemsFormArray().controls.forEach((item) => {
      items.push(item.value);
    });
    return items;
  }

  private makeId() {
    return `${this.refundForm.get('buyerIdPrefix')?.value}:${
      this.refundForm.get('buyerIdSufix')?.value
    }`;
  }

  private getBuyerIdPrefix(id: string) {
    if (id) return id.split(':')[0];
    return '';
  }
  private getBuyerIdSufix(id: string) {
    if (id) return id.split(':')[1];
    return '';
  }

  private findItemLabel(name: string) {
    let item = this.receipt.items.find((item: any) => item.name == name);
    if (item) return [item.labels];
    return '';
  }

  private convertToRefundDto(receipt: Receipts, buyerId: string, items: any) {
    let tmp: CreateReceipt;

    return (tmp = {
      referentDocumentNumber: receipt.invoice_number,
      referentDocumentDT: receipt.sdc_date_time,
      invoiceType: receipt.invoice_type.invoice_type,
      items: items,
      metaFields: receipt.meta_fields,
      payment: receipt.payments as any,
      transactionType: 'Refund',
      sdcDateTime: receipt.sdc_date_time,
      buyerId: buyerId,
      cashier: receipt.cashier,
      buyerCostCenterId: receipt.buyer_cost_center_id,
      invoiceNumberPos: receipt.invoice_number_pos,
      advanceChain: receipt.advance_chain,
    });
  }
}
