import { Component, OnInit } from '@angular/core';
import { Observable, take } from 'rxjs';
import { AuthService } from 'src/app/core/services/auth.service';
import { ClientService } from 'src/app/shared/services/backend/clients/client.service';

@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss']
})
export class HomeComponent implements OnInit {
  clientData: any;
  clientData$!: Observable<any>;

  constructor(
    private clientService: ClientService,
    private authService: AuthService
  ) { }

  ngOnInit(): void {
      this.authService
        .getUserId()
        .pipe(take(1))
        .subscribe((uid: any) => {
        this.getClientData(uid);
      });
  }

  private getClientData(uid: string) {

    this.clientData$ = this.clientService.getClient(uid);
  }
}
