import { Injectable } from '@angular/core';
import { AngularFireAuth } from '@angular/fire/compat/auth';
import { AngularFirestore } from '@angular/fire/compat/firestore';
import * as firebase from 'firebase/app';
import { collection } from 'firebase/firestore';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class CommonFirestoreService {
  public me$ = new Observable((subscriber) => {
    subscriber.next(null);
    subscriber.next({ test: 'test' });
  });

  constructor(private firestore: AngularFirestore) {}

  listenUserData$(userId: string) {
    return this.firestore.collection('users').doc(userId).snapshotChanges();
  }

  listenToSettingsChange$(userId: string) {
    return this.firestore.collection('users').doc(userId).valueChanges();
  }

  saveUserData(
    userId: string,
    data: {
      billing: {
        customerMB: string;
        emails: string;
        customerName: string;
        customerBillingDistrict: string;
        customerPIB: string;
        customerAddress: string;
        customerPostalCode: string;
        monthlyInstallmentPrice: string;
      };
    }
  ) {
    return this.firestore.collection('users').doc(userId).update(data);
  }

  listenToReportsChange$(userId: string) {

    return this.firestore.collection('user-reports').get();
  }

}
