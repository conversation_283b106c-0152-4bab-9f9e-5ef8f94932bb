import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Routes } from '@angular/router';
import { SharedModule } from 'src/app/shared/shared.module';
import { CashiersComponent } from './containers/cashiers/cashiers.component';
import { CashierListComponent } from './components/cashier-list/cashier-list.component';
import { AddCashierDialogComponent } from './components/add-cashier-dialog/add-cashier-dialog.component';
import { MatTooltipModule } from '@angular/material/tooltip';
import { CashiersHeaderComponent } from './components/cashiers-header/cashiers-header.component';

const routes: Routes = [
  {
    path: '',
    component: CashiersComponent
  }
];

@NgModule({
  declarations: [
    CashiersComponent,
    CashierListComponent,
    AddCashierDialogComponent,
    CashiersHeaderComponent
  ],
  imports: [
    CommonModule,
    SharedModule,
    MatTooltipModule,
    RouterModule.forChild(routes)
  ],
  exports: [
    CashierListComponent,
    CashiersHeaderComponent
  ]
})
export class CashiersModule { }
