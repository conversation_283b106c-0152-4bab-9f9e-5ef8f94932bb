import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { BreakpointObserver } from '@angular/cdk/layout';

@Component({
  selector: 'app-settings-navbar',
  templateUrl: './settings-navbar.component.html',
  styleUrls: ['./settings-navbar.component.scss']
})
export class SettingsNavbarComponent implements OnInit {

  activeLink: string = '';
  isMobile: boolean = false;
  isDropdownOpen: boolean = false;

  navigationItems = [
    {
      route: '/podesavanja',
      labelKey: 'dashboard.base',
      activeValue: '',
      icon: 'settings'
    },
    {
      route: '/podesavanja/izvestaji',
      labelKey: 'dashboard.reports',
      activeValue: 'izvestaji',
      icon: 'bar_chart'
    },
    {
      route: '/podesavanja/racuni',
      labelKey: 'dashboard.receipts',
      activeValue: 'racuni',
      icon: 'receipt'
    },
    {
      route: '/podesavanja/kasa',
      labelKey: 'Pode<PERSON><PERSON><PERSON> kase',
      activeValue: 'kasa',
      icon: 'point_of_sale'
    },
    {
      route: '/podesavanja/password',
      labelKey: 'Promena lozinke',
      activeValue: 'password',
      icon: 'lock'
    }
  ];

  constructor(
    private activeRoute: ActivatedRoute,
    private breakpointObserver: BreakpointObserver
  ) {
    // Monitor screen size changes
    this.breakpointObserver.observe(['(max-width: 768px)']).subscribe((result) => {
      this.isMobile = result.matches;
      if (!this.isMobile) {
        this.isDropdownOpen = false; // Close dropdown when switching to desktop
      }
    });
  }

  ngOnInit(): void {
    this.activeRoute.url.subscribe(url => {
      if(url[0])
        this.activeLink = url[0].path;
    });
  }

  toggleDropdown(): void {
    this.isDropdownOpen = !this.isDropdownOpen;
  }

  closeDropdown(): void {
    this.isDropdownOpen = false;
  }

  getActiveItem() {
    return this.navigationItems.find(item => item.activeValue === this.activeLink) || this.navigationItems[0];
  }
}
