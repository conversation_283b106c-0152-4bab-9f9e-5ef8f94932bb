import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-settings-navbar',
  templateUrl: './settings-navbar.component.html',
  styleUrls: ['./settings-navbar.component.scss']
})
export class SettingsNavbarComponent implements OnInit {

  activeLink: string = '';

  constructor(private activeRoute: ActivatedRoute) { }

  ngOnInit(): void {
    this.activeRoute.url.subscribe(url => {
      if(url[0])
        this.activeLink = url[0].path;
    });
  }
}
