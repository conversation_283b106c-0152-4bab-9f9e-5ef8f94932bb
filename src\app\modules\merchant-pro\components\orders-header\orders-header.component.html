<div class="container-fluid header" *transloco="let t">
    <div class="row">
        <app-mp-navbar style="padding: 0px;"></app-mp-navbar>
    </div>
    <div class="row align-items-center buttons">
        <div class="col-lg-2 col-md-6">
            <app-expand-btn 
            text="{{t('group_actions')}}"
            [disabled]="!this.bulkActionService.getIsEnoughSelected()" 
            [menuItems]="menuItems"></app-expand-btn>
         
        </div>
        <div class="col-lg-4 col-md-6">
            <mat-form-field class="example-chip-list w-100" appearance="outline">
                <mat-label>{{t('orders_page.search_by_order_number')}}</mat-label>
                <mat-chip-list #chipList >
                    <mat-chip *ngFor="let invoiceNumberFilter of invoiceNumberFilters"
                        (removed)="removeInvoiceNumberFilter(invoiceNumberFilter)">
                        {{ invoiceNumberFilter }}
                        <button matChipRemove>
                            <mat-icon>cancel</mat-icon>
                        </button>
                    </mat-chip>
                    <input placeholder="Nova porudzbina..." [matChipInputFor]="chipList"
                        [matChipInputSeparatorKeyCodes]="separatorKeysCodes" [matChipInputAddOnBlur]="true"
                        (matChipInputTokenEnd)="addInvoiceNumberFilter($event)"/>
                </mat-chip-list>
            </mat-form-field>
        </div>
        <div class="col-lg-2 col-md-6"></div>
        <div class="col-lg-2 col-md-6 d-flex align-items-center justify-content-end">
            <input type="checkbox"><p>{{t('training_mode')}}</p>
        </div>
        <div class="col-lg-2 col-md-6 d-flex">
            <span class="icon" (click)="actionEmitter.emit({action:'Refresh'})">
                <mat-icon>update</mat-icon>
            </span>
            <span>
                <p> {{t('updated')}}: </p>
                <p>{{refreshTime | fiscommDate}}</p>
            </span>
    
        </div>
    </div>
</div>