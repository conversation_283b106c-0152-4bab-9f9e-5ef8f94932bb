import { Component, Inject, NgZone, OnInit, Pipe, PipeTransform } from '@angular/core';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MerchantProService } from 'src/app/components/merchant-pro/merchant-pro.service';

@Pipe({ name: 'areAllOrdersFinished' })
export class AreAllOrdersFinishedPipe implements PipeTransform {
  transform(input: any[]): any {
    return input.map(o => o.fiscalisationStatus).includes('in_progress');
  }
}
@Component({
  selector: 'app-multiple-order-fiscalisation-dialog',
  templateUrl: './multiple-order-fiscalisation-dialog.component.html',
  styleUrls: ['./multiple-order-fiscalisation-dialog.component.scss'],

})
export class MultipleOrderFiscalisationDialogComponent implements OnInit {

  fiscalisationStatus = 'not_started';
  constructor(@Inject(MAT_DIALOG_DATA) public data: any, private merchantProService: MerchantProService) {
    this.data.orders = this.data.orders.map((o: any) => ({ ...o, fiscalisationStatus: 'not_started' }))
  }

  ngOnInit(): void {
  }

  startFiscalisation() {
    this.fiscalisationStatus = 'in_progress';
    this.data.orders = this.data.orders.map((o: any) => ({ ...o, fiscalisationStatus: 'in_progress' }));
    this.data.orders.forEach(async (order: any, i: number) => {
      setTimeout(async () => {
        const fiscalisedInvoice = await this.merchantProService.fiscaliseInvoice({ order, trainingMode: this.data.trainingMode });
        order.fiscalisationStatus = 'done';
        this.fiscalisationStatus = this.data.orders.map((o: any) => o.fiscalisationStatus).includes('in_progress') ? 'in_progress' : 'done';
      }, i  * 250)
    })
  }

}
