<div class="field-values-dialog" *transloco="let t">
  <!-- add close icon here -->
  <button mat-icon-button (click)="close()" class="close-button" >
    <mat-icon>close</mat-icon>
  </button>
  <h2 mat-dialog-title>{{ t('custom_fields.values') }} "{{ data.field.name }}"</h2>

  <div mat-dialog-content>
    <mat-form-field appearance="outline" class="search-field">
      <mat-label>{{ t('custom_fields.search_values') }}</mat-label>
      <input matInput [formControl]="searchControl" [placeholder]="t('custom_fields.type_to_search')">
      <mat-icon matSuffix>search</mat-icon>
      <mat-hint *ngIf="!loading && totalResults > 0">{{ t('custom_fields.showing_results', { count: totalResults }) }}</mat-hint>
    </mat-form-field>

    <div class="values-container">
      <div *ngIf="loading" class="loading-container">
        <mat-spinner diameter="40"></mat-spinner>
        <p>{{ t('custom_fields.loading_values') }}</p>
      </div>

      <ng-container *ngIf="!loading">
        <mat-list *ngIf="searchValues.length > 0">
          <h3 mat-subheader>Saved Values ({{ searchValues.length }})</h3>

          <ng-container *ngFor="let item of searchValues">
            <mat-list-item>
              <mat-icon mat-list-icon>label</mat-icon>
              <div class="value-header">
                <span class="value-text">{{ item.value }}</span>
                <button mat-icon-button *ngIf="item.receipts && item.receipts.length"
                        [matBadge]="item.receipts?.length" matBadgePosition="above after"
                        matBadgeColor="accent" matBadgeSize="small"
                        (click)="toggleReceiptsList(item)"
                        [attr.aria-label]="'Toggle receipts list'" class="toggle-button">
                  <mat-icon>receipt</mat-icon>
                </button>
              </div>
              <div mat-line class="created-at">Added {{ item.createdAt?.toDate() | date:'medium' }}</div>
            </mat-list-item>

            <!-- Receipts expansion panel -->
            <div [@expandReceipts]="getAnimationState(item)"
                class="receipts-container"
                *ngIf="item.receipts && item.receipts.length > 0">
              <mat-card class="receipts-list">
                <mat-card-title class="receipts-title">Receipts with value "{{ item.value }}"</mat-card-title>
                <mat-divider></mat-divider>
                <mat-card-content>
                  <mat-list dense>
                    <mat-list-item *ngFor="let receipt of item.receipts">
                      <div mat-line>
                        <strong>Invoice #:</strong> {{ receipt.invoiceNumber || 'N/A' }}
                      </div>
                      <div mat-line>
                        <strong>Date:</strong> {{ receipt.sdcDateTime ? (receipt.sdcDateTime | date:'medium') : 'N/A' }}
                      </div>
                      <div mat-line class="receipt-links" *ngIf="receipt.invoicePdfUrl || receipt.verificationUrl">
                        <a *ngIf="receipt.invoicePdfUrl" mat-button color="primary" [href]="receipt.invoicePdfUrl" target="_blank">
                          <mat-icon class="link-icon">picture_as_pdf</mat-icon> PDF
                        </a>
                        <a *ngIf="receipt.verificationUrl" mat-button color="accent" [href]="receipt.verificationUrl" target="_blank">
                          <mat-icon class="link-icon">verified</mat-icon> Verify
                        </a>
                      </div>
                      <mat-divider></mat-divider>
                    </mat-list-item>
                  </mat-list>
                </mat-card-content>
              </mat-card>
            </div>
          </ng-container>
        </mat-list>

        <div *ngIf="searchValues.length === 0" class="no-values">
          <p *ngIf="searchControl.value">{{ t('custom_fields.no_values_found') }} "{{ searchControl.value }}"</p>
          <p *ngIf="!searchControl.value">{{ t('custom_fields.no_values') }}</p>
        </div>

        <div *ngIf="hasMoreResults" class="load-more-container">
          <button mat-button color="primary" (click)="loadMore()">
            {{ t('common.load_more') }}
          </button>
        </div>
      </ng-container>
    </div>
  </div>

</div>
