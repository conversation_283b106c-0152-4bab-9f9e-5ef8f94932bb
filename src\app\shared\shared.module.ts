import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

//material
import { MatIconModule } from '@angular/material/icon';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatListModule } from '@angular/material/list';
import { MatSelectModule } from '@angular/material/select';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatButtonModule } from '@angular/material/button';
import { MatTableModule } from '@angular/material/table';
import {
  MatPaginatorIntl,
  MatPaginatorModule,
} from '@angular/material/paginator';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatTabsModule } from '@angular/material/tabs';
import { MatSortModule } from '@angular/material/sort';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatMenuModule } from '@angular/material/menu';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatChipsModule } from '@angular/material/chips';
import { MatNativeDateModule, MatPseudoCheckboxModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatSliderModule } from '@angular/material/slider';

//forms
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { FiscommInputComponent } from './components/form/fiscomm-input/fiscomm-input.component';
import { FiscommSelectComponent } from './components/form/fiscomm-select/fiscomm-select.component';
import { FiscommTooltipComponent } from './components/form/fiscomm-tooltip/fiscomm-tooltip.component';

import { MatDialogModule } from '@angular/material/dialog';
import { ConfirmDialogComponent } from './dialogs/components/confirm-dialog/confirm-dialog.component';

import { HTTP_INTERCEPTORS } from '@angular/common/http';
import { FiscommAuthInterceptor } from '../core/interceptors/fiscomm-auth.interceptor';
import { RsdCurrencyPipe } from './pipes/rsd-currency.pipe';
import { CustomPaginatorIntl } from './paginator/custom-paginator';
import { TableSkeletonComponent } from './components/table-skeleton/table-skeleton.component';

import { SuccessBtnComponent } from './components/buttons/success-btn/success-btn.component';
import { DangerBtnComponent } from './components/buttons/danger-btn/danger-btn.component';
import { ExpandBtnComponent } from './components/buttons/expand-btn/expand-btn.component';
import { FiscommDatePipe } from './pipes/fiscomm-date.pipe';
import { DatePipe } from '@angular/common';
import { SpinnerDialogComponent } from './dialogs/components/spinner-dialog/spinner-dialog.component';
import { PageTitleComponent } from './components/page-title/page-title.component';
import { ContactBtnComponent } from './components/buttons/contact-btn/contact-btn.component';
import { PillComponent } from './components/pill/pill.component';
import { TranslocoModule } from '@ngneat/transloco';
import { ResetPasswordDialogComponent } from './dialogs/components/reset-password-dialog/reset-password-dialog.component';
import { FiscommPaginatorComponent } from './components/fiscomm-paginator/fiscomm-paginator.component';
import { DecimalPlacesLimitDirective } from './directives/decimal-places-limit.directive';
import { InitalsAvatarComponent } from './components/initals-avatar/initals-avatar.component';
import { EmailSlicePipe } from './pipes/email-slice.pipe';
import { PrimaryBtnComponent } from './components/buttons/primary-btn/primary-btn.component';
import { CyrillicToLatinPipe } from './pipes/cyrillic-to-latin.pipe';
import { ErrorSnackbarMessageComponent } from './components/error-snackbar-message/error-snackbar-message.component';
import { ClipboardModule } from '@angular/cdk/clipboard';
import { LayoutModule } from '@angular/cdk/layout';
import { MinimizedTabsComponent } from './components/minimized-tabs/minimized-tabs.component';
import { PdfDialogComponent } from './dialogs/components/pdf-dialog/pdf-dialog.component';
import { TimerPipe } from './pipes/timer.pipe';
import { SendReceiptEmailDialogComponent } from './dialogs/components/send-receipt-email-dialog/send-receipt-email-dialog.component';
import { CashierAutocompleteComponent } from './components/cashier-autocomplete/cashier-autocomplete.component';
import { AddCustomFieldDialogComponent } from './dialogs/components/add-custom-field-dialog/add-custom-field-dialog.component';
import { FieldValueAutocompleteComponent } from './components/field-value-autocomplete/field-value-autocomplete.component';
import { CurrencyPriceInputComponent } from './components/currency-price-input/currency-price-input.component';

@NgModule({
  declarations: [
    FiscommInputComponent,
    FiscommSelectComponent,
    DecimalPlacesLimitDirective,
    FiscommTooltipComponent,
    ConfirmDialogComponent,
    RsdCurrencyPipe,
    TableSkeletonComponent,
    SuccessBtnComponent,
    PrimaryBtnComponent,
    DangerBtnComponent,
    ExpandBtnComponent,
    FiscommDatePipe,
    TimerPipe,
    ExpandBtnComponent,
    SpinnerDialogComponent,
    PageTitleComponent,
    ContactBtnComponent,
    PillComponent,
    ResetPasswordDialogComponent,
    FiscommPaginatorComponent,
    InitalsAvatarComponent,
    EmailSlicePipe,
    PrimaryBtnComponent,
    CyrillicToLatinPipe,
    ErrorSnackbarMessageComponent,
    MinimizedTabsComponent,
    PdfDialogComponent,
    SendReceiptEmailDialogComponent,
    CashierAutocompleteComponent,
    AddCustomFieldDialogComponent,
    FieldValueAutocompleteComponent,
    CurrencyPriceInputComponent
  ],
  imports: [
    //Material shared moudles
    CommonModule,
    MatIconModule,
    MatSidenavModule,
    MatListModule,
    MatFormFieldModule,
    MatTooltipModule,
    MatSelectModule,
    MatInputModule,
    MatButtonModule,
    MatDialogModule,
    MatExpansionModule,
    MatTabsModule,
    MatAutocompleteModule,
    MatMenuModule,
    MatProgressSpinnerModule,
    MatProgressBarModule,
    MatNativeDateModule,
    MatDatepickerModule,
    MatChipsModule,
    MatSlideToggleModule,
    MatSliderModule,

    //table stuff
    MatTableModule,
    MatSortModule,
    MatPaginatorModule,

    //Forms
    ReactiveFormsModule,
    FormsModule,

    //cdk
    LayoutModule,

    //transloco
    TranslocoModule,
  ],
  exports: [
    //transloco
    TranslocoModule,

    CommonModule,
    ReactiveFormsModule,
    FormsModule,

    //dialogs

    //material
    MatIconModule,
    MatSidenavModule,
    MatListModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatDialogModule,
    MatExpansionModule,
    MatTabsModule,
    MatAutocompleteModule,
    MatMenuModule,
    MatProgressSpinnerModule,
    MatProgressBarModule,
    MatChipsModule,
    MatNativeDateModule,
    MatDatepickerModule,
    MatSlideToggleModule,
    MatSliderModule,

    //cdk
    ClipboardModule,
    LayoutModule,
    //table stuff
    MatTableModule,
    MatSortModule,
    MatPaginatorModule,

    //fiscomm helpers
    FiscommInputComponent,
    FiscommSelectComponent,
    FiscommTooltipComponent,

    //other shared components
    TableSkeletonComponent,
    SpinnerDialogComponent,
    PageTitleComponent,
    PillComponent,
    FiscommPaginatorComponent,
    InitalsAvatarComponent,
    MinimizedTabsComponent,
    CurrencyPriceInputComponent,

    //pipes
    RsdCurrencyPipe,
    FiscommDatePipe,
    TimerPipe,
    EmailSlicePipe,
    CyrillicToLatinPipe,

    //buttons
    SuccessBtnComponent,
    PrimaryBtnComponent,
    DangerBtnComponent,
    ExpandBtnComponent,
    ContactBtnComponent,
    CashierAutocompleteComponent,
    AddCustomFieldDialogComponent,
    FieldValueAutocompleteComponent
  ],
  providers: [
    DatePipe,
    {
      provide: HTTP_INTERCEPTORS,
      useClass: FiscommAuthInterceptor,
      multi: true,
    },
    { provide: MatPaginatorIntl, useClass: CustomPaginatorIntl },
  ],
})
export class SharedModule {}
