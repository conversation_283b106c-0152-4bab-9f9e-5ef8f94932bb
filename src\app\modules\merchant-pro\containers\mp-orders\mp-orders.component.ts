import { Component, OnInit } from '@angular/core';
import { PageEvent } from '@angular/material/paginator';
import { Observable, map } from 'rxjs';
import { FormCreateReceiptService } from 'src/app/modules/receipts/services/form-create-receipt.service';
import { DialogService } from 'src/app/shared/dialogs/dialog.service';
import { MerchantProService } from 'src/app/shared/services/backend/merchant-pro/merchant-pro.service';
import { OrdersQueryParams } from 'src/app/shared/services/backend/merchant-pro/types/orders-request.dto';
import {
  Order,
  OrdersDto,
} from 'src/app/shared/services/backend/merchant-pro/types/orders-response.dto';
import { ReceiptsService } from 'src/app/shared/services/backend/receipts/receipts.service';
import {
  Receipts,
  ReceiptsDto,
} from 'src/app/shared/services/backend/receipts/types/receipts-response.dto';

@Component({
  selector: 'app-mp-orders',
  templateUrl: './mp-orders.component.html',
  styleUrls: ['./mp-orders.component.scss'],
})
export class MpOrdersComponent implements OnInit {
  constructor(
    private mpService: MerchantProService,
    private receiptsService: ReceiptsService,
    private dialogService: DialogService,
    private createFormService: FormCreateReceiptService
  ) {}

  orders$: Observable<Order[]> = new Observable();
  receipts: Receipts[] = [];

  //paging info
  perPage: number = 10;
  pageIndex: number = 0;
  pageSizeOptions = [10, 15, 20];
  total: number = 10;

  params: OrdersQueryParams = {
    page: this.pageIndex,
    size: this.perPage,
    filter: undefined,
  };

  refreshTime: Date = new Date();
  columns$: Observable<string[]> = new Observable();

  disabled = {
    invoiceType: true,
    transactionType: true,
    referentDocumentNumber: true,
    invoiceNumberPos: true,
    metaFields: true,
    prefixBuyerCostCenterId: true,
    sufixBuyerCostCenterId: true,
  };

  ngOnInit(): void {
    this.getOrders();
    this.getSystemColumns();
  }

  private getSystemColumns() {
    this.columns$ = this.mpService.getColumnSettings();
  }

  filter(value: any) {
    if (value.length > 0)
      this.params.filter = JSON.stringify({
        filter_name: 'order.id',
        values: [value],
      });
    else this.params.filter = undefined;
    this.getOrders();
  }

  getOrders() {
    this.refreshTime = new Date();
    this.orders$ = this.mpService.getOrders(this.params).pipe(
      map((response) => {
        this.getReceiptsForOrders(response);
        this.total = response.total;
        return response.orders;
      })
    );
  }

  private getReceiptsForOrders(response: OrdersDto) {
    const orderIds = response.orders.map((order) => order.id).join(',');
    this.receiptsService
      .getReceipts({ invoiceNumberPos: orderIds })
      .subscribe((resp) => {
        console;
        this.receipts = resp.data;
        this.mapReceiptsToOrders(response);
      });
  }

  private mapReceiptsToOrders(response: OrdersDto) {
    response.orders.map((order) => {
      order.receipts = this.receipts.filter(
        (receipt) => receipt.invoice_number_pos == order.id
      );
    });
  }

  handlePageEvent(e: PageEvent) {
    this.params.page = e.pageIndex;
    this.params.size = e.pageSize;
    this.getOrders();
  }

  handleActions(event: any) {
    switch (event.action.toLowerCase()) {
      case 'fiscalize':
        this.fiscalize(event.data.id);
        break;
      case 'refund':
        this.sendDataToDialog(this.getOrderReceipt(event.data));
        this.openRefundDialog();
        break;
      case 'refresh':
        this.getOrders();
        break;
      case 'bulk-fiscalize':
        this.openBulkFiscalize();
        break;
      case 'bulk-refund':
        this.openBulkActionDialog('order-refund');
        break;
    }
  }

  private getOrderReceipt(data: Order) {
    return data.receipts?.find(
      (receipt) =>
        receipt.invoice_type.invoice_type == 'Normal' &&
        receipt.transaction_type.transaction_type == 'Sale'
    );
  }

  private sendDataToDialog(data: any) {
    this.createFormService.setReceiptFormData(
      data,
      data.invoice_type.invoice_type,
      'Refund',
      this.disabled
    );
  }

  private openRefundDialog() {
    const dialogRef = this.dialogService.openCreateReceiptDialog(
      { width: '90%', height: '90%' },
      { disabled: this.disabled }
    );
    this.afterClose(dialogRef);
  }

  private afterClose(dialogRef: any) {
    dialogRef.afterClosed().subscribe((isCreateReceipt: boolean) => {
      if (isCreateReceipt) {
        const submitValue = this.createFormService.getPreparedReceiptData();
        this.receiptsService
          .createReceipt(submitValue)
          .subscribe(() => this.getOrders());
      }
    });
  }

  private fiscalize(id: string) {
    this.mpService.fiscalise(id).subscribe((x) => {
      this.getOrders();
    });
  }

  private openBulkFiscalize() {
    const dialogRef = this.dialogService.openBulkOrderFiscalizeDialog({
      width: '80%',
      height: '90%',
    });
  }

  private openBulkActionDialog(data: any) {
    const dialogRef = this.dialogService.openBulkActionDialog(
      {
        width: '80%',
        height: '90%',
      },
      data
    );
  }
}
