<div class="container-fluid p-lg-5 p-md-3" *transloco="let t">
  <app-page-title text="{{ t('dashboard.payments') }}"></app-page-title>
  <app-biling-header (bulkActionEmitter)="handleBulkActions($event)">
  </app-biling-header>
  <app-billing-table
    *ngIf="data && data.length > 0"
    (actionEmitter)="handleActions($event)"
    [systemColumns]="columns"
    [data]="data"
  >
  </app-billing-table>
  <app-fiscomm-paginator
    *ngIf="data"
    [chunkSize]="data.length"
    [pageSizes]="[10, 15, 20]"
    (pageChange)="handlePageEvent($event)"
  ></app-fiscomm-paginator>
</div>
