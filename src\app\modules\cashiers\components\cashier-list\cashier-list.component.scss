.table-container {
  width: 100%;
  overflow-x: auto;

  .no-data {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100px;
    background-color: #f9f9f9;
    border-radius: 4px;

    p {
      color: #666;
      font-style: italic;
    }
  }

  .cashiers-table {
    width: 100%;

    .status-badge {
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 500;
      text-transform: uppercase;

      &.active {
        background-color: #e6f7ed;
        color: #00875a;
      }

      &.inactive {
        background-color: #ffebe6;
        color: #de350b;
      }
    }

    .element-row {
      cursor: pointer;

      &:hover {
        background-color: rgba(0, 0, 0, 0.04);
      }
    }

    mat-icon {
      transition: color 0.3s ease;

      &:hover {
        color: #3f51b5;
      }
    }
  }
}

// Icon color styles
.edit-icon {
  color: #2196F3; // Blue
  &:hover {
    color: #0D47A1; // Darker blue on hover
  }
}

.star-icon {
  color: #FFB400; // Gold/yellow for default star
  &:hover {
    color: #FF9800; // Orange on hover
  }
}

.star-outline-icon {
  color: #757575; // Grey for non-default star outline
  &:hover {
    color: #FFB400; // Gold/yellow on hover
  }
}

.delete-icon {
  color: #F44336; // Red
  &:hover {
    color: #B71C1C; // Darker red on hover
  }
}
