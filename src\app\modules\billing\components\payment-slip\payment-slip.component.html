<ng-container *transloco="let t">
    <h4>{{t('billing.information')}}</h4>
    <div class="row" id="paymentPaper">
        <div class="col-6 mt-4" id="paymentLeft">
            <p>uplatilac</p>
            <textarea readonly [value]="data?.payer"></textarea>
            <p>svrha uplate</p>
            <textarea readonly [value]="data?.paymentPurpouse"></textarea>
            <p>primalac</p>
            <textarea readonly [value]="data?.recipient"></textarea>
            <input readonly type="text" [value]="data?.payerSignature" class="underline">
            <p>potpis i pecat uplatioca</p>
        </div>
        <div class="col-6" id="paymentRight">
            <div class="row">
                <div id="payPass" class="col-3 d-flex flex-column">
                    <p>sifra placanja</p>
                    <input readonly type="text" [value]="data?.paymentPass">
                </div>
                <div class="col-3 d-flex flex-column mt-4">
                    <p>valuta</p>
                    <input readonly type="text" [value]="data?.currency">
                </div>
                <div class="col-6 d-flex flex-column mt-4">
                    <p>iznos</p>
                    <input readonly type="text" [value]="data?.amount">
                </div>
            </div>
            <div class="row">
                <div class="col-12">
                    <p>racun primaoca</p>
                    <input readonly type="text" [value]="data?.recipientAcc">
                </div>
            </div>
            <div class="row">
                <p>model i poziv na broj (odobrenje)</p>
                <span class="d-flex justify-content-around">
                    <div class="col-3 d-flex flex-column pe-4"> <input readonly type="text" [value]="data?.model"></div>
                    <div class="col-9 d-flex flex-column"> <input readonly type="text" [value]="data?.referanceNumber"></div>
                </span>
            </div>
        </div>
        <div class="col-12 d-flex justify-content-around">
            <div class="col-5 d-flex justify-content-center flex-column align-items-center">
                <input readonly type="text" class="underline" [value]="data?.receptionDate">
                <p>mesto i datum prijema</p>
            </div>
            <div class="col-5 d-flex justify-content-center flex-column align-items-center">
                <input readonly type="text" class="underline" [value]="data?.currencyDate">
                <p>datum valute</p>
            </div>
    
        </div>
        <div class="col-12 d-flex justify-content-center ">
            <p>Obrazac br 1.</p>
        </div>
    </div>
    <p>({{t('billing.instruction_info')}})</p>
</ng-container> 

