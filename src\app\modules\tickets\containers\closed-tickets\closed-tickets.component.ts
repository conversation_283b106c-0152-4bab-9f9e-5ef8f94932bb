import { Component, OnInit } from '@angular/core';
import { Subscription } from 'rxjs';
import { PaginatorEvent } from 'src/app/shared/components/fiscomm-paginator/fiscomm-paginator.component';
import { DialogService } from 'src/app/shared/dialogs/dialog.service';
import { TicketService } from 'src/app/shared/services/backend/tickets/ticket.service';

@Component({
  selector: 'app-closed-tickets',
  templateUrl: './closed-tickets.component.html',
  styleUrls: ['./closed-tickets.component.scss'],
})
export class ClosedTicketsComponent implements OnInit {
  columns = ['id', 'naslov', 'prioritet', 'status', 'odgovoreno', 'Details'];
  tickets: any;

  //paging info
  perPage: number = 10;
  pageIndex: number = 0;
  pageSizeOptions = [10, 15, 20];
  to: number = 10;

  params: any = {
    page: this.pageIndex + 1,
    perPage: this.perPage,
  };

  dialogRefSubscription?: Subscription;

  constructor(
    private ticketService: TicketService,
    private dialogService: DialogService
  ) {}

  ngOnInit(): void {
    this.getTickets();
    this.dialogRefSubscription = this.dialogService
      .getNewTicketDialogRef$()
      .subscribe((ref) => {
        if (ref.componentInstance)
          ref.componentInstance.newTicketEmitter.subscribe((newTicket: any) => {
            // trenutno back nevraca sva polaj da bi se ovo radilo uemsto getTickets
            // this.tickets = [
            //   newTicket,
            //   ...this.tickets.slice(0, this.perPage - 1),
            // ];
            this.params.page = 1;
            this.getTickets();
          });
      });
  }

  private getTickets() {
    this.ticketService
      .getTickets({ status: 'closed' })
      .subscribe((response) => {
        this.tickets = response;
      });
  }

  handlePageEvent(e: PaginatorEvent) {
    this.params.page = e.page;
    this.params.perPage = e.perPage;
    this.getTickets();
  }

  ngOnDestroy() {
    if (this.dialogRefSubscription) {
      this.dialogRefSubscription.unsubscribe();
    }
  }
}
