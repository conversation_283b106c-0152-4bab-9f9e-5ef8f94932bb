import { Injectable } from '@angular/core';
import { ActivatedRoute, ActivatedRouteSnapshot, CanActivate, Router, RouterStateSnapshot, UrlTree } from '@angular/router';
import { Observable, map } from 'rxjs';
import { AuthService } from '../services/auth.service';

@Injectable({
  providedIn: 'root'
})
export class LoginPageGuard implements CanActivate {

  constructor(private authService: AuthService, private router: Router) { }

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot): Observable<boolean | UrlTree> | Promise<boolean | UrlTree> | boolean | UrlTree {
    return this.authService.isAuthenticated.pipe(map((isAuth: boolean) => {
      const queryParams = route.queryParams;
      if (queryParams['adminToken']) {
        this.authService.signInWithCustomToken(queryParams['adminToken'])
          .then((res) => {
            alert(`<PERSON><PERSON><PERSON><PERSON> ulogo<PERSON> kao ${res.user?.displayName}`)
            const redirectTo = this.extractRedirect(window.location.href);
            if (redirectTo)
              this.router.navigateByUrl(redirectTo);
            else
              this.router.navigateByUrl('/pocetna');

            window.location.reload();
          })
          .catch((err) => alert('Korisnik nije pronađen'));
      }
      return isAuth ? this.router.createUrlTree(['/pocetna']) : true;
    }));
  }

  private extractRedirect(url: string) {
    const urlObj = new URL(url);
    const searchParams = new URLSearchParams(urlObj.search);
    return searchParams.get('redirect_to');
  }
}
