<mat-form-field appearance="outline" class="field-value-autocomplete">
  <mat-label>{{ placeholder }}</mat-label>
  <input type="text"
         matInput
         [formControl]="inputControl"
         [matAutocomplete]="auto">
  <mat-autocomplete #auto="matAutocomplete" (optionSelected)="optionSelected($event.option.value)">
    <mat-option *ngFor="let option of filteredOptions | async" [value]="option">
      {{ option }}
    </mat-option>
  </mat-autocomplete>
  <mat-spinner matSuffix *ngIf="inputControl.value && !(filteredOptions | async)?.length" diameter="20"></mat-spinner>
</mat-form-field>
