import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { CustomField } from 'src/app/models/custom-field.model';
import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';
import { MatDialog } from '@angular/material/dialog';
import { FieldValuesDialogComponent } from '../field-values-dialog/field-values-dialog.component';
import { TranslocoService } from '@ngneat/transloco';

@Component({
  selector: 'app-custom-field-list',
  templateUrl: './custom-field-list.component.html',
  styleUrls: ['./custom-field-list.component.scss']
})
export class CustomFieldListComponent implements OnInit {
  @Input() customFields: CustomField[] = [];
  @Output() action = new EventEmitter<{action: string, data: any}>();

  displayedColumns: string[] = ['order', 'name', 'type', 'required', 'values', 'status', 'actions'];

  constructor(
    private dialog: MatDialog,
    private translocoService: TranslocoService
  ) { }

  ngOnInit(): void {
  }

  onDrop(event: CdkDragDrop<CustomField[]>): void {
    moveItemInArray(this.customFields, event.previousIndex, event.currentIndex);
    this.action.emit({
      action: 'reorder',
      data: this.customFields
    });
  }

  onEdit(field: CustomField): void {
    this.action.emit({
      action: 'edit',
      data: field
    });
  }

  onDelete(field: CustomField): void {
    this.action.emit({
      action: 'delete',
      data: field
    });
  }

  onAdd(): void {
    this.action.emit({
      action: 'add',
      data: null
    });
  }

  onViewValues(field: CustomField): void {
    this.dialog.open(FieldValuesDialogComponent, {
      data: { field },
      width: '600px',
    });
  }

  getFieldTypeLabel(type: string): string {
    return this.translocoService.translate(`custom_fields.field_type_${type.toLowerCase()}`);
  }

  formatPossibleValues(values: string[] | undefined): string {
    if (!values || values.length === 0) return '-';
    return values.join(', ');
  }
}
