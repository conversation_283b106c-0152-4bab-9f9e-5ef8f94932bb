<mat-autocomplete
  [displayWith]="displayWith"
  #auto="matAutocomplete"
  *ngIf="data.length > 0 && control && data"
>
  <mat-option *ngFor="let el of filteredOptions" [value]="getValue(el)">
    {{ displayFn(el) }}
  </mat-option>
</mat-autocomplete>

<app-fiscomm-input
  [placeholder]="placeholder"
  [appearance]="appearance"
  [control]="control"
  [type]="type"
  [tooltip]="tooltip"
  [autoComplete]="auto"
></app-fiscomm-input>
