<div
  class="container-fluid ps-lg-5 pe-lg-5 pt-lg-4 pb-lg-4 ps-md-3 pe-lmd-3 pt-md-2 pb-md-2"
  *transloco="let t"
>
  <app-page-title text="{{ t('settings') }}"></app-page-title>
  <app-settings-navbar></app-settings-navbar>
  <app-settings-container title="{{ t('settings_page.password_settings') }}">
    <ng-container content>
      <div class="row">
        <form [formGroup]="passwordForm" (ngSubmit)="changePassword()">
          <!-- Current password -->
          <div class="col-md-12 mb-3">
            <label class="form-label">{{ t('settings_page.current_password') }}</label>
            <div class="input-group">
              <input
                class="form-control"
                [type]="hideCurrentPassword ? 'password' : 'text'"
                formControlName="currentPassword"
              />
              <button
                class="btn btn-outline-secondary"
                type="button"
                (click)="hideCurrentPassword = !hideCurrentPassword"
              >
                <mat-icon>{{ hideCurrentPassword ? "visibility_off" : "visibility" }}</mat-icon>
              </button>
            </div>
            <div class="text-danger" *ngIf="currentPassword.invalid && (currentPassword.dirty || currentPassword.touched)">
              <small *ngIf="currentPassword.errors?.['required']">
                {{ t('validation.required') }}
              </small>
            </div>
          </div>

          <!-- New password -->
          <div class="col-md-12 mb-3">
            <label class="form-label">{{ t('settings_page.new_password') }}</label>
            <div class="input-group">
              <input
                class="form-control"
                [type]="hideNewPassword ? 'password' : 'text'"
                formControlName="newPassword"
              />
              <button
                class="btn btn-outline-secondary"
                type="button"
                (click)="hideNewPassword = !hideNewPassword"
              >
                <mat-icon>{{ hideNewPassword ? "visibility_off" : "visibility" }}</mat-icon>
              </button>
            </div>
            <div class="text-danger" *ngIf="newPassword.invalid && (newPassword.dirty || newPassword.touched)">
              <small *ngIf="newPassword.errors?.['required']">
                {{ t('validation.required') }}
              </small>
              <small *ngIf="newPassword.errors?.['minlength']">
                {{ t('validation.password_length') }}
              </small>
            </div>
          </div>

          <!-- Confirm password -->
          <div class="col-md-12 mb-3">
            <label class="form-label">{{ t('settings_page.confirm_password') }}</label>
            <div class="input-group">
              <input
                class="form-control"
                [type]="hideConfirmPassword ? 'password' : 'text'"
                formControlName="confirmPassword"
              />
              <button
                class="btn btn-outline-secondary"
                type="button"
                (click)="hideConfirmPassword = !hideConfirmPassword"
              >
                <mat-icon>{{ hideConfirmPassword ? "visibility_off" : "visibility" }}</mat-icon>
              </button>
            </div>
            <div class="text-danger" *ngIf="confirmPassword.invalid && (confirmPassword.dirty || confirmPassword.touched)">
              <small *ngIf="confirmPassword.errors?.['required']">
                {{ t('validation.required') }}
              </small>
              <small *ngIf="confirmPassword.errors?.['mismatch']">
                {{ t('validation.password_mismatch') }}
              </small>
            </div>
          </div>

          <!-- Error and success messages -->
          <div class="col-md-12 mb-3" *ngIf="errorMessage">
            <div class="alert alert-danger">{{ errorMessage }}</div>
          </div>
          <div class="col-md-12 mb-3" *ngIf="successMessage">
            <div class="alert alert-success">{{ successMessage }}</div>
          </div>
        </form>
      </div>
    </ng-container>
    <ng-container buttons>
      <div class="row">
        <div class="col-12">
          <app-success-btn
            text="{{ t('settings_page.change_password') }}"
            (click)="changePassword()"
            [disabled]="passwordForm.invalid"
          ></app-success-btn>
        </div>
      </div>
    </ng-container>
  </app-settings-container>
</div>
