import { Injectable } from '@angular/core';
import {
  MatDialog,
  MatDialogConfig,
  MatDialogRef,
} from '@angular/material/dialog';
import { ConfirmDialogComponent } from './components/confirm-dialog/confirm-dialog.component';
import { CreateReceiptDialogComponent } from 'src/app/modules/receipts/components/create-receipt-dialog/create-receipt-dialog.component';
import { SpinnerDialogComponent } from './components/spinner-dialog/spinner-dialog.component';
import { BulkActionDialogComponent } from 'src/app/shared/components/bulk-action-dialog/bulk-action-dialog.component';
import { BulkFiscalizeDialogComponent } from 'src/app/modules/merchant-pro/components/bulk-fiscalize-dialog/bulk-fiscalize-dialog.component';
import { EmailTemplateDialogComponent } from './components/email-template-dialog/email-template-dialog.component';
import { RefundOrderDialogComponent } from 'src/app/modules/merchant-pro/components/refund-order-dialog/refund-order-dialog.component';
import { CreateProductDialogComponent } from 'src/app/modules/products/components/create-product-dialog/create-product-dialog.component';
import { BillingDetailsDialogComponent } from 'src/app/modules/billing/components/billing-details-dialog/billing-details-dialog.component';
import { ResetPasswordDialogComponent } from './components/reset-password-dialog/reset-password-dialog.component';
import { BehaviorSubject, ReplaySubject } from 'rxjs';
import { TicketDetailsDialogComponent } from 'src/app/modules/tickets/components/ticket-details-dialog/ticket-details-dialog.component';
import { NewTicketDialogComponent } from 'src/app/modules/tickets/components/new-ticket-dialog/new-ticket-dialog.component';
import { CreateInvoiceDialogComponent } from 'src/app/modules/invoices/components/create-invoice-dialog/create-invoice-dialog.component';
import { PdfDialogComponent } from './components/pdf-dialog/pdf-dialog.component';
import { UpdateProductDialogComponent } from '../../modules/products/components/update-product-dialog/update-product-dialog.component';
import { SendReceiptEmailDialogComponent } from './components/send-receipt-email-dialog/send-receipt-email-dialog.component';
import { AddCashierDialogComponent } from 'src/app/modules/cashiers/components/add-cashier-dialog/add-cashier-dialog.component';
import { AddCustomFieldDialogComponent } from './components/add-custom-field-dialog/add-custom-field-dialog.component';
import { DuplicateProductsDialogComponent } from 'src/app/modules/products/components/duplicate-products-dialog/duplicate-products-dialog.component';
import { ImportTemplateDialogComponent } from 'src/app/modules/products/components/import-template-dialog/import-template-dialog.component';
import { Type } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class DialogService {

  constructor(private dialog: MatDialog) {}

  private openDialog<T>(component: Type<T>, config: MatDialogConfig): MatDialogRef<T> {
    return this.dialog.open(component, config);
  }

  openConfirmationDialog(message: string, title?: string): MatDialogRef<any> {
    const config: MatDialogConfig = {
      data: {
        message: message,
        title: title,
      },
    };
    return this.openDialog(ConfirmDialogComponent, config);
  }

  openCreateReceiptDialog(
    sizeParams: SizeParams,
    data?: any
  ): MatDialogRef<any> {
    const config: MatDialogConfig = {
      hasBackdrop: true,
      width: sizeParams.width,
      height: sizeParams.height,
      data: data,
      panelClass: 'create-receipt',
    };
    return this.openDialog(CreateReceiptDialogComponent, config);
  }

  openBulkActionDialog(sizeParams: SizeParams, data?: any): MatDialogRef<any> {
    const config: MatDialogConfig = {
      width: sizeParams.width,
      height: sizeParams.height,
      data: data,
    };
    return this.openDialog(BulkActionDialogComponent, config);
  }

  openBulkOrderFiscalizeDialog(
    sizeParams: SizeParams,
    data?: any
  ): MatDialogRef<any> {
    const config: MatDialogConfig = {
      width: sizeParams.width,
      height: sizeParams.height,
      data: data,
    };
    return this.openDialog(BulkFiscalizeDialogComponent, config);
  }

  openLoadingDialog() {
    const config: MatDialogConfig = {
      disableClose: true,
    };
    return this.dialog.open(SpinnerDialogComponent, config);
  }

  openEmailTemplateDialog() {
    const config: MatDialogConfig = {};
    return this.dialog.open(EmailTemplateDialogComponent, config);
  }

  openRefundOrderDialog(sizeParams: SizeParams, data?: any) {
    const config: MatDialogConfig = {
      width: sizeParams.width,
      height: sizeParams.height,
      data: data,
    };
    return this.dialog.open(RefundOrderDialogComponent, config);
  }

  openCreateProductDialog(sizeParams: SizeParams, data?: any) {
    const config: MatDialogConfig = {
      width: sizeParams.width,
      height: sizeParams.height,
      data: data,
    };
    return this.openDialog(CreateProductDialogComponent, config);
  }

  openUpdateProductDialog(dialogConfig: { width: string; height: string; data: any; }) {
    const config: MatDialogConfig = {
      width: dialogConfig.width,
      height: dialogConfig.height,
      data: dialogConfig.data,
    };

    return this.openDialog(UpdateProductDialogComponent, config);
  }

  openBillingDetailsDialog(sizeParams: SizeParams, data?: any) {
    const config: MatDialogConfig = {
      width: sizeParams.width,
      height: sizeParams.height,
      data: data,
    };
    return this.openDialog(BillingDetailsDialogComponent, config);
  }

  openResetPasswordDialog(sizeParams: SizeParams, data?: any) {
    const config: MatDialogConfig = {
      width: sizeParams.width,
      height: sizeParams.height,
      data: data,
    };
    return this.openDialog(ResetPasswordDialogComponent, config);
  }

  private ticketDialogRefSubject = new ReplaySubject<any>(1);
  openNewTicketDialog(sizeParams: SizeParams, data?: any): MatDialogRef<any> {
    const config: MatDialogConfig = {
      width: sizeParams.width,
      height: sizeParams.height,
      panelClass: 'new-ticket',
      data: data,
    };
    let dialogRef = this.openDialog(NewTicketDialogComponent, config);
    this.ticketDialogRefSubject.next(dialogRef);
    return dialogRef;
  }

  openTicketDetailsDialog(sizeParams: SizeParams, data?: any) {
    const config: MatDialogConfig = {
      width: sizeParams.width,
      height: sizeParams.height,
      panelClass: 'ticket-details',
      data: data,
    };
    return this.openDialog(TicketDetailsDialogComponent, config);
  }

  getNewTicketDialogRef$() {
    return this.ticketDialogRefSubject.asObservable();
  }

  openCreateInvoiceDialog(sizeParams: SizeParams, data?: any) {
    const config: MatDialogConfig = {
      width: sizeParams.width,
      height: sizeParams.height,
      data: data,
      panelClass: 'create-invoice',
    };
    return this.openDialog(CreateInvoiceDialogComponent, config);
  }

  openPdfDialog(sizeParams: SizeParams, data?: any) {
    const config: MatDialogConfig = {
      width: sizeParams.width,
      height: sizeParams.height,
      data: data,
    };
    return this.openDialog(PdfDialogComponent, config);
  }

  openSendReceiptEmailDialog(receiptId: string): MatDialogRef<any> {
    const config: MatDialogConfig = {
      width: '400px',
      data: { receiptId }
    };
    return this.openDialog(SendReceiptEmailDialogComponent, config);
  }

  openAddCashierDialog(dialogConfig: { width: string; height: string; data?: any; }) {
    const config: MatDialogConfig = {
      width: dialogConfig.width,
      height: dialogConfig.height,
      data: dialogConfig.data,
    };
    return this.openDialog(AddCashierDialogComponent, config);
  }

  openEditCashierDialog(dialogConfig: { width: string; height: string; data: any; }) {
    const config: MatDialogConfig = {
      width: dialogConfig.width,
      height: dialogConfig.height,
      data: { cashier: dialogConfig.data },
    };
    return this.openDialog(AddCashierDialogComponent, config);
  }

  openAddCustomFieldDialog(dialogConfig: { width?: string; height?: string; data?: any; }) {
    const config: MatDialogConfig = {
      width: dialogConfig.width || '600px',
      height: dialogConfig.height || '700px',
      data: dialogConfig.data,
      disableClose: true
    };
    return this.openDialog(AddCustomFieldDialogComponent, config);
  }

  openEditCustomFieldDialog(dialogConfig: { width?: string; height?: string; data: any; }) {
    const config: MatDialogConfig = {
      width: dialogConfig.width || '600px',
      height: dialogConfig.height || '700px',
      data: dialogConfig.data,
      disableClose: true
    };
    return this.openDialog(AddCustomFieldDialogComponent, config);
  }

  openDuplicateProductsDialog(data: { duplicates: any[], totalProducts: number }): MatDialogRef<any> {
    const config: MatDialogConfig = {
      width: '900px',
      maxHeight: '90vh',
      data: data,
      disableClose: true
    };
    return this.openDialog(DuplicateProductsDialogComponent, config);
  }

  openImportTemplateDialog(): MatDialogRef<ImportTemplateDialogComponent> {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.width = '800px';
    dialogConfig.maxHeight = '90vh';

    return this.openDialog(ImportTemplateDialogComponent, dialogConfig);
  }
}

export interface SizeParams {
  width: string;
  height: string;
}
