import { Component, OnInit } from '@angular/core';
import { DialogService } from 'src/app/shared/dialogs/dialog.service';

@Component({
  selector: 'app-home-invoice',
  templateUrl: './home-invoice.component.html',
  styleUrls: ['./home-invoice.component.scss'],
})
export class HomeInvoiceComponent implements OnInit {
  constructor(private dialogService: DialogService) {}

  ngOnInit(): void {}

  openCreateInvoice() {
    this.dialogService.openCreateInvoiceDialog({ width: '80%', height: '80%' });
  }
}
