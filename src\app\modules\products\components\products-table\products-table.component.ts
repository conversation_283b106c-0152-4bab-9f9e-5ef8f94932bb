import { Component, Input, OnInit, EventEmitter, Output } from '@angular/core';
import { SelectionModel } from '@angular/cdk/collections';
import { AVAILABLE_CURRENCIES, CURRENCY_ORDER } from 'src/app/shared/constants/available_currencies.const';
import { Product } from 'src/app/shared/models/product.model';

interface Action {
  action: string;
  data: any;
}

interface CurrencyDisplay {
  code: string;
  price: number;
  symbol: string;
}

interface CurrencyInfo {
  symbol: string;
  name: string;
  symbol_native: string;
  code: string;
  name_plural: string;
  [key: string]: any;
}

@Component({
  selector: 'app-products-table',
  templateUrl: './products-table.component.html',
  styleUrls: ['./products-table.component.scss']
})
export class ProductsTableComponent implements OnInit {

  @Input() displayColumns: string[] = [];
  @Input() data: Product[] = [];

  @Output() actionEmitter: EventEmitter<Action> = new EventEmitter();
  @Output() bulkDeleteEmitter = new EventEmitter<any[]>();

  selection = new SelectionModel<any>(true, []);
  selectedProducts = new Set<any>();
  availableCurrencies: { [key: string]: CurrencyInfo } = AVAILABLE_CURRENCIES;
  currencyOrder = CURRENCY_ORDER;

  constructor() { }

  ngOnInit(): void {
    // Add taxLabel column if not already included
    if (!this.displayColumns.includes('taxLabel') && this.displayColumns.includes('productCode')) {
      // Find the position of productCode and insert taxLabel after it
      const prodCodeIndex = this.displayColumns.indexOf('productCode');
      if (prodCodeIndex !== -1) {
        this.displayColumns.splice(prodCodeIndex + 1, 0, 'taxLabel');
      }
    }
  }

  isAllSelected() {
    const numSelected = this.selection.selected.length;
    const numRows = this.data.length;
    return numSelected === numRows;
  }

  toggleAllRows() {
    if (this.isAllSelected()) {
      this.selection.clear();
      this.selectedProducts.clear();
    } else {
      this.data.forEach(row => {
        this.selection.select(row);
        this.selectedProducts.add(row);
      });
    }
  }

  toggleRow(row: any) {
    this.selection.toggle(row);
    if (this.selection.isSelected(row)) {
      this.selectedProducts.add(row);
    } else {
      this.selectedProducts.delete(row);
    }
  }

  deleteSelected() {
    this.actionEmitter.emit({action: 'bulk-delete', data: Array.from(this.selectedProducts)});
  }

  getAdditionalCurrencies(product: Product): CurrencyDisplay[] {
    if (!product.prices) return [];

    // Filter to only include currencies from CURRENCY_ORDER
    return Object.entries(product.prices)
      .filter(([code]) => this.currencyOrder.includes(code))
      .map(([code, price]) => {
        return {
          code,
          price,
          symbol: this.availableCurrencies[code]?.symbol || code
        };
      })
      // Sort based on the order in CURRENCY_ORDER
      .sort((a, b) => {
        const indexA = this.currencyOrder.indexOf(a.code);
        const indexB = this.currencyOrder.indexOf(b.code);
        return indexA - indexB;
      });
  }

  getTaxLabelDisplay(taxLabel: any): string {
    if (!taxLabel) return '-';
    return taxLabel.label + ' (' + taxLabel.rate + '%)';
  }
}
