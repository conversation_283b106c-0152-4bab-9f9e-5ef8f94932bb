import { BreakpointObserver } from '@angular/cdk/layout';
import { Component, OnInit } from '@angular/core';
import { DialogService } from 'src/app/shared/dialogs/dialog.service';
import { ProductsService } from 'src/app/shared/services/backend/products/products.service';
import { MatSnackBar } from '@angular/material/snack-bar';
import { TranslocoService } from '@ngneat/transloco';
import { CURRENCY_ORDER } from 'src/app/shared/constants/available_currencies.const';

@Component({
  selector: 'app-products',
  templateUrl: './products.component.html',
  styleUrls: ['./products.component.scss'],
})
export class ProductsComponent implements OnInit {

  columns = ['select', 'Naziv', 'Cena', 'gtin', 'productCode', 'taxLabel', 'createdAt', 'Akcija'];

  products: any[] = [];
  constructor(
    private productService: ProductsService,
    private dialogService: DialogService,
    private breakpointObserver: BreakpointObserver,
    private snackBar: MatSnackBar,
    private translocoService: TranslocoService
  ) {}

  ngOnInit(): void {
    this.getProducts();
  }

  private getProducts(cached: boolean = true) {
    this.productService
      .getAllProducts(cached)
      .subscribe((products) => (this.products = products));
  }

  handleActions(event: any) {
    switch (event.action.toLowerCase()) {
      case 'delete':
        this.deleteProduct(event.data);
        break;
      case 'add':
        this.openAddDialog();
        break;
      case 'edit':
        this.openEditDialog(event.data);
        break;
      case 'import':
        this.importProducts(event.data);
        break;
      case 'bulk-delete':
        this.deleteProducts(event.data);
        break;
    }
  }

  private importProducts(products: any[]) {
    // Process products to extract currency prices
    const processedProducts = products.map(product => {
      const processedProduct = {
        name: product.name,
        price: product.price,
        gtin: product.gtin || '',
        productCode: product.productCode || '',
        prices: {} as {[key: string]: number}
      };

      // Extract all price_ fields as currency prices
      Object.keys(product).forEach(key => {
        if (key.startsWith('price_')) {
          const currency = key.replace('price_', '');
          if (CURRENCY_ORDER.includes(currency) && currency !== 'RSD') {
            // Only add if it's a supported currency and has a value
            if (product[key] !== undefined && product[key] !== null && product[key] !== '') {
              processedProduct.prices[currency] = parseFloat(product[key]);
            }
          }
        }
      });

      return processedProduct;
    });

    // First, remove duplicates from the imported products themselves
    const uniqueImportedProducts = this.removeDuplicatesFromImported(processedProducts);

    // Validate the imported data
    const validProducts = uniqueImportedProducts.filter(product => {
      return product.name && product.price;
    });

    if (validProducts.length === 0) {
      this.snackBar.open(
        this.translocoService.translate('items.import_error'),
        'OK',
        { duration: 3000 }
      );
      return;
    }

    // Check for duplicates with existing products
    const duplicates = validProducts.filter(product => {
      return this.products.some(existingProduct =>
        (product.gtin && existingProduct.gtin === product.gtin) ||
        (product.productCode && existingProduct.productCode === product.productCode) ||
        (product.name && existingProduct.name === product.name)
      );
    }).map(product => {
      const existingProduct = this.products.find(p =>
        (product.gtin && p.gtin === product.gtin) ||
        (product.productCode && p.productCode === product.productCode) ||
        (product.name && p.name === product.name)
      );

      // Determine the type of conflict (prioritize gtin, then productCode, then name)
      let conflictType = 'name';
      if (product.gtin && existingProduct?.gtin === product.gtin) {
        conflictType = 'gtin';
      } else if (product.productCode && existingProduct?.productCode === product.productCode) {
        conflictType = 'productCode';
      }

      return {
        ...product,
        existingProduct,
        conflictType
      };
    });

    const nonDuplicateProducts = validProducts.filter(product =>
      !duplicates.some(d =>
        (d.gtin && d.gtin === product.gtin) ||
        (d.productCode && d.productCode === product.productCode) ||
        (d.name && d.name === product.name)
      )
    );

    if (duplicates.length > 0) {
      const dialogRef = this.dialogService.openDuplicateProductsDialog({
        duplicates,
        totalProducts: validProducts.length
      });

      dialogRef.afterClosed().subscribe(result => {
        if (result === undefined) {
          // User clicked cancel or closed the dialog
          this.snackBar.open(
            this.translocoService.translate('items.import_cancelled'),
            'OK',
            { duration: 3000 }
          );
          return;
        }

        if (!result) {
          return;
        }

        if (result && result.length > 0) {
          this.importValidProducts([...nonDuplicateProducts, ...result]);
        } else {
          this.importValidProducts(nonDuplicateProducts);
        }
      });
    } else {
      this.importValidProducts(nonDuplicateProducts);
    }
  }

  private removeDuplicatesFromImported(products: any[]): any[] {
    const uniqueProducts = new Map<string, any>();

    products.forEach(product => {
      // First, try to use GTIN or product code as the key
      const identifierKey = product.gtin || product.productCode;

      if (identifierKey) {
        // If we haven't seen this GTIN/product code before, add it
        if (!uniqueProducts.has(identifierKey)) {
          uniqueProducts.set(identifierKey, product);
        }
      } else if (product.name) {
        // If no GTIN or product code, use product name as the key
        const nameKey = `name-${product.name.toLowerCase().trim()}`;
        if (!uniqueProducts.has(nameKey)) {
          uniqueProducts.set(nameKey, product);
        }
      } else {
        // If no identifiers at all, add it with a unique key
        uniqueProducts.set(`no-key-${Date.now()}-${Math.random()}`, product);
      }
    });

    return Array.from(uniqueProducts.values());
  }

  private importValidProducts(products: any[]) {
    let successCount = 0;
    let errorCount = 0;

    products.forEach(product => {
      if (product.existingProduct) {
        // For existing products, merge the data
        const updatedProduct = {
          ...product.existingProduct,
          name: product.name,
          price: product.price,
          gtin: product.gtin || '',
          productCode: product.productCode || '',
          // Merge prices - keep existing prices and update/add new ones
          prices: {
            ...(product.existingProduct.prices || {}),
            ...product.prices
          }
        };

        this.productService.updateProduct(updatedProduct).subscribe({
          next: () => successCount++,
          error: () => errorCount++,
          complete: () => {
            if (successCount + errorCount === products.length) {
              this.getProducts(false);
              this.showImportResults(successCount, errorCount);
            }
          }
        });
      } else {
        // For new products
        this.productService.addNewProduct({
          name: product.name,
          price: product.price,
          gtin: product.gtin || '',
          productCode: product.productCode || '',
          prices: product.prices || {}
        }).subscribe({
          next: () => successCount++,
          error: () => errorCount++,
          complete: () => {
            if (successCount + errorCount === products.length) {
              this.getProducts(false);
              this.showImportResults(successCount, errorCount);
            }
          }
        });
      }
    });
  }

  private showImportResults(successCount: number, errorCount: number) {
    this.snackBar.open(
      this.translocoService.translate('items.import_success', {
        success: successCount,
        error: errorCount
      }),
      'OK',
      { duration: 3000 }
    );
  }

  private deleteProduct(product: any) {
    this.productService
      .deleteProduct(product)
      .subscribe(() => this.getProducts(false));
  }

  private deleteProducts(products: any[]) {
    const dialogRef = this.dialogService.openConfirmationDialog(
      this.translocoService.translate('items.delete_confirm_multiple', { count: products.length })
    );

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        let successCount = 0;
        let errorCount = 0;

        products.forEach(product => {
          this.productService.deleteProduct(product).subscribe({
            next: () => successCount++,
            error: () => errorCount++,
            complete: () => {
              if (successCount + errorCount === products.length) {
                this.getProducts(false);
                this.snackBar.open(
                  this.translocoService.translate('items.delete_success_multiple', {
                    success: successCount,
                    error: errorCount
                  }),
                  'OK',
                  { duration: 3000 }
                );
              }
            }
          });
        });
      }
    });
  }

  private openEditDialog(product: any) {
    let dialogConfig = {
      width: '50%',
      height: 'fit-content',
      data: product,
    };

    this.breakpointObserver
      .observe(['(max-width: 576px)'])
      .subscribe((result) => {
        if (result.matches) {
          dialogConfig.width = '90%';
        }
      });

    const dialogRef = this.dialogService.openUpdateProductDialog(dialogConfig);
    this.afterClose(dialogRef);
  }

  private openAddDialog() {
    let dialogConfig = {
      width: '50%%',
      height: 'fit-content'
    };

    this.breakpointObserver
      .observe(['(max-width: 576px)'])
      .subscribe((result) => {
        if (result.matches) {
          dialogConfig.width = '90%';
        }
      });

    const dialogRef = this.dialogService.openCreateProductDialog(dialogConfig);
    this.afterClose(dialogRef);
  }

  private afterClose(dialogRef: any) {
    dialogRef.afterClosed().subscribe((isCreated: boolean) => {
      if (isCreated) this.getProducts(false);
    });
  }
}
