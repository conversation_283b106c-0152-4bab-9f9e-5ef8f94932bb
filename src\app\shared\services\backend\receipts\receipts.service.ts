import { Injectable } from '@angular/core';
import { Observable, isEmpty, shareReplay, map } from 'rxjs';
import { environment } from 'src/environments/environment';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import {
  ColumnsDto,
  Customer,
  MetaFieldsDto,
  Receipts,
  ReceiptsDto,
  TaxRatesDto,
} from './types/receipts-response.dto';
import {
  CreateReceipt,
  ReceiptsQueryParams,
} from './types/receipts-request.dto';

@Injectable({
  providedIn: 'root',
})
export class ReceiptsService {
  baseURL: string = environment.mainFiscomm.baseUrl;
  baseUrlSelfcare: string = environment.mainSelfcare.baseUrl;

  headers = new HttpHeaders().set('intercept-msg', 'Akcija uspela');
  noLoadingHeader = new HttpHeaders().set('Fiscomm-No-Loading', 'true');

  private cachedMetaFields$?: Observable<MetaFieldsDto[]>;

  constructor(private http: HttpClient) {}

  getMetaFields(): Observable<MetaFieldsDto[]> {
    if (!this.cachedMetaFields$) {
      this.cachedMetaFields$ = this.http
        .get<MetaFieldsDto[]>(this.baseUrlSelfcare + `/meta-field/`)
        .pipe(
          shareReplay({ bufferSize: 1, refCount: true }),
          map((fields) => this.setupMetaFields(fields))
        );
    }
    return this.cachedMetaFields$;
  }

  private setupMetaFields(fields: MetaFieldsDto[]): MetaFieldsDto[] {
    return fields
      .filter((field) => field.enabled)
      .map((field) => {
        field.values = field.values.filter((el) => el != '');
        return field;
      });
  }

  getReceiptsColumns(): Observable<ColumnsDto> {
    return this.http.get<ColumnsDto>(
      this.baseUrlSelfcare + `/settings/receipts/columns`
    );
  }

  getReceipts(queryParams: ReceiptsQueryParams): Observable<ReceiptsDto> {
    const params = this.convertToHttpParams(queryParams);
    return this.http.get<ReceiptsDto>(this.baseUrlSelfcare + `/receipt`, {
      params: params,
      headers: this.noLoadingHeader,
    });
  }

  getReceiptsCount(searchParams?: any): Observable<number> {
    return this.http.get<any>(environment.admin.baseUrl + '/invoices/count', {
      params: searchParams,
    });
  }

  private convertToHttpParams(params: any): HttpParams {
    let httpParams = new HttpParams();
    for (const key in params) {
      if (params[key] !== undefined && params[key] !== null) {
        if (typeof params[key] === 'object') {
          httpParams = httpParams.append(key, JSON.stringify(params[key]));
        } else {
          httpParams = httpParams.append(key, params[key]);
        }
      }
    }
    return httpParams;
  }

  createReceipt(receipt: CreateReceipt, reportProgress: boolean = false) {
    return this.http.post<Receipts>(
      this.baseUrlSelfcare + '/receipt/fiscalise',
      receipt,
      {
        headers: this.headers,
        reportProgress: reportProgress,
        observe: 'events',
      }
    );
  }

  createTestReceipt() {
    return this.http.post<Receipts>(
      this.baseUrlSelfcare + '/receipt/fiscalise',
      this.getTestReceiptData(),
      { headers: this.headers }
    );
  }

  sendReceiptToEmail(receiptId: string, email: string) {
    return this.http.post(
      this.baseUrlSelfcare + `/receipt/${receiptId}/send-email`,
      { email },
      { headers: this.headers }
    );
  }

  getTestReceiptData() {
    return {
      invoiceType: 'Normal',
      transactionType: 'Sale',
      invoiceNumberPos: '2131232',
      payment: [
        {
          amount: '200',
          paymentType: 'Cash',
        },
      ],
      items: [
        {
          unitPrice: '200',
          totalAmount: 200,
          quantity: '1',
          name: 'Test item',
          label: 'F',
          labels: ['F'],
        },
      ],
      metaFields: [],
      buyerId: null,
      buyerCostCenterId: null,
    };
  }
}
