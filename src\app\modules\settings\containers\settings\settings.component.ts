import { Component, OnInit, ViewChild } from '@angular/core';
import { AngularFireAuth } from '@angular/fire/compat/auth';
import { CommonFirestoreService } from 'src/app/shared/services/firestore/common-firestore.service';
import { FormControl, FormArray } from '@angular/forms';
import { take } from 'rxjs';
import { ClientService } from 'src/app/shared/services/backend/clients/client.service';
import { AuthService } from 'src/app/core/services/auth.service';

@Component({
  selector: 'app-settings',
  templateUrl: './settings.component.html',
  styleUrls: ['./settings.component.scss'],
})
export class SettingsComponent implements OnInit {
  userData?: any;

  generalSettingsData: any = {};

  errors = {
    address: false,
    municipality: false,
    postalCode: false,
  };

  emailsArr: FormControl[] = [];
  municipality: FormControl = new FormControl('');
  adress: FormControl = new FormControl('');
  postalCode: FormControl = new FormControl('');

  uid: string = '';

  constructor(
    private commonFirestoreService: CommonFirestoreService,
    private clientService: ClientService,
    private authService: AuthService
  ) {}

  ngOnInit(): void {
    this.authService
      .getUserId()
      .pipe(take(1))
      .subscribe((uid: any) => {
        this.uid = uid;
        this.loadSettings();
      });
  }

  private loadSettings() {
    this.commonFirestoreService
      .listenToSettingsChange$(this.uid)
      .pipe(take(1))
      .subscribe(() => this.getUserData());
  }

  private getUserData() {
    this.clientService.getClient(this.uid).subscribe((client) => {
      this.initalizeGeneralSettings(client);
      this.userData = client;
    });
  }

  private initalizeGeneralSettings(data: any) {
    this.generalSettingsData = {
      mb: data?.billing?.customerMB || '',
      emails: data?.billing?.emails || [''],
      name: data?.billing?.customerName || '',
      municipality: data?.billing?.customerBillingDistrict || '',
      vatRegNum: data?.billing?.customerPIB || '',
      address: data?.billing?.customerAddress || '',
      postalCode: data?.billing?.customerPostalCode || '',
      monthlyPrice: data?.billing?.monthlyInstallmentPrice || 0,
    };
    this.municipality.setValue(this.generalSettingsData.municipality);
    this.adress.setValue(this.generalSettingsData.address);
    this.postalCode.setValue(this.generalSettingsData.postalCode);

    this.generalSettingsData.emails.forEach((el: any) => {
      this.emailsArr.push(new FormControl(el));
    });
  }

  addEmail() {
    this.generalSettingsData.emails.push('');
    this.emailsArr?.push(new FormControl(''));
  }

  removeEmail(index: number) {
    this.generalSettingsData.emails.splice(index, 1);
    this.emailsArr.splice(index, 1);
  }

  getEmailControl(index: number) {
    return this.emailsArr[index];
  }

  saveGeneralSettings() {
    if (this.userData) {
      if (!this.userData.billing) {
        this.userData.billing = {
          customerBillingDistrict: this.municipality.value,
          customerAddress: this.adress.value,
          customerPostalCode: this.postalCode.value,
          emails: [],
        };
      }

      this.userData.billing.emails = [];
      this.emailsArr.forEach((control) => {
        this.userData?.billing?.emails.push(control.value);
      });
      this.userData.billing = {
        ...this.userData.billing,
        customerBillingDistrict: this.municipality.value,
        customerAddress: this.adress.value,
        customerPostalCode: this.postalCode.value,
      };
      this.clientService
        .updateClient(
          this.uid,
          this.userData,
          'Vaša podešavanja su uspešno sačuvana.'
        )
        .subscribe();
    }
  }
}
