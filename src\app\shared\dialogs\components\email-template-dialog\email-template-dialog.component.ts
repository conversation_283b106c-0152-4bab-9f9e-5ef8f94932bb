import { Component, OnInit } from '@angular/core';
import { AngularFirestore } from '@angular/fire/compat/firestore';
import { AngularFireAuth } from '@angular/fire/compat/auth';
import { MerchantProService } from 'src/app/shared/services/backend/merchant-pro/merchant-pro.service';

declare var unlayer: any

@Component({
  selector: 'app-email-template-dialog',
  templateUrl: './email-template-dialog.component.html',
  styleUrls: ['./email-template-dialog.component.scss']
})
export class EmailTemplateDialogComponent implements OnInit {
  currentUser:any;
  constructor(private authService: AngularFireAuth, private mpService: MerchantProService) {
  }

  ngOnInit(): void {
    this.authService.onAuthStateChanged((user) => {
      this.currentUser = user;
      this.mpService.getSettings({ uid: this.currentUser.uid }).then((settings$) => {
        settings$.subscribe((settings: any) => {
          const unlayerSettings = {
            id: 'editor',
            design: settings.emailTemplate.json,
            user: {
              id: this.currentUser.uid
            },
            projectId: 73483,
            templateId: settings.emailTemplate.json ? null : "157133"
          }
          unlayer.init(unlayerSettings);
          if (settings.emailTemplate.json) unlayer.loadDesign(settings.emailTemplate.json);
        })
      })
    })
  }
  preview() {
    unlayer.exportHtml(function (data: any) {
      var html = data.html;
      console.log(html);
    })
  }

  save() {
    unlayer.exportHtml(async (data: any) => {
      var json = data.design;
      var html = data.html;
      console.log(html, json);
      await this.mpService.updateSettings({
        uid: this.currentUser.uid,
        settings: {
          emailTemplate: {
            json: JSON.stringify(json), html
          }
        }
      })
    })

  }
}
