<ng-container *transloco="let t">
  <div class="import-template-dialog">
    <h2 mat-dialog-title>{{ t('items.import_template_title') }}</h2>

    <mat-dialog-content>
      <p>{{ t('items.import_template_description') }}</p>

      <div class="template-table">
        <table mat-table [dataSource]="columns" class="mat-elevation-z2">
          <ng-container matColumnDef="name">
            <th mat-header-cell *matHeaderCellDef>{{ t('name') }}</th>
            <td mat-cell *matCellDef="let column">{{ column.name }}</td>
          </ng-container>

          <ng-container matColumnDef="description">
            <th mat-header-cell *matHeaderCellDef>{{ t('description') }}</th>
            <td mat-cell *matCellDef="let column">{{ column.description }}</td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>
      </div>

      <div class="currency-explanation">
        <h3>{{ t('items.import_template_currency_title') }}</h3>
        <p>{{ t('items.import_template_currency_description') }}</p>
        <ul>
          <ng-container *ngFor="let currency of supportedCurrencies">
          <li *ngIf="currency !== 'RSD'" >
            <span >price_{{ currency }} - {{ t('currencies.' + currency.toLowerCase()) }}</span>
          </li>
          </ng-container>
        </ul>
      </div>

      <div class="example-section">
        <h3>{{ t('example') }}</h3>
        <pre class="example-code">
name    price   price_EUR   price_USD   gtin           productCode
"Product 1" 100.00  10.00       11.50       "1234567890123"  "P001"
"Product 2" 250.50  25.50       28.95       "2345678901234"  "P002"
"Product 3" 75.25                            "3456789012345"  "P003"</pre>
      </div>

      <p class="note">{{ t('items.import_template_note') }}</p>
      <p class="note">{{ t('items.import_template_currency_note') }}</p>
    </mat-dialog-content>

    <mat-dialog-actions align="end">
      <button mat-button (click)="onClose()">{{ t('close') }}</button>
    </mat-dialog-actions>
  </div>
</ng-container>
