import { BreakpointObserver } from '@angular/cdk/layout';
import { Component, OnInit } from '@angular/core';
import { CustomField } from 'src/app/models/custom-field.model';
import { DialogService } from 'src/app/shared/dialogs/dialog.service';
import { CustomFieldService } from 'src/app/shared/services/firestore/custom-field.service';
import { MatDialog } from '@angular/material/dialog';
import { AddCustomFieldDialogComponent } from 'src/app/shared/dialogs/components/add-custom-field-dialog/add-custom-field-dialog.component';
import { ConfirmDialogComponent } from 'src/app/shared/dialogs/components/confirm-dialog/confirm-dialog.component';
import { take } from 'rxjs/operators';

@Component({
  selector: 'app-custom-fields',
  templateUrl: './custom-fields.component.html',
  styleUrls: ['./custom-fields.component.scss']
})
export class CustomFieldsComponent implements OnInit {
  customFields: CustomField[] = [];

  constructor(
    private customFieldService: CustomFieldService,
    private dialogService: DialogService,
    private breakpointObserver: BreakpointObserver,
    private dialog: MatDialog
  ) { }

  ngOnInit(): void {
    this.loadCustomFields();
  }

  loadCustomFields(): void {
    this.customFieldService.getCustomFields().pipe(take(1)).subscribe(
      (customFields) => this.customFields = customFields
    );
  }

  handleActions(event: { action: string, data: any }): void {
    switch (event.action) {
      case 'add':
        this.openAddDialog();
        break;
      case 'edit':
        this.openEditDialog(event.data);
        break;
      case 'delete':
        this.openDeleteDialog(event.data);
        break;
      case 'deleteAll':
        this.openDeleteAllDialog();
        break;
      case 'reorder':
        this.updateFieldOrder(event.data);
        break;
    }
  }

  // Save the new order of fields to the database
  updateFieldOrder(fields: CustomField[]): void {
    this.customFieldService.updateFieldOrder(fields).pipe(take(1)).subscribe(
      () => this.loadCustomFields()
    );
  }

  openDeleteAllDialog(): void {
    this.dialog.open(ConfirmDialogComponent, {
      data: {
        title: 'Delete All Custom Fields',
        message: 'Are you sure you want to delete all custom fields? This action cannot be undone.',
        confirmText: 'Delete All',
        cancelText: 'Cancel'
      }
    }).afterClosed().subscribe(result => {
      if (result) this.deleteAllCustomFields();
    });
  }

  openDeleteDialog(customField: CustomField): void {
    // Don't even show the dialog if the field has been used in receipts
    if (customField.isReceiptCreated) {
      this.dialog.open(ConfirmDialogComponent, {
        data: {
          title: 'Cannot Delete Field',
          message: `The field "${customField.name}" has been used in receipts and cannot be deleted to maintain data integrity.`,
          confirmText: 'OK',
          showCancel: false
        }
      });
      return;
    }

    this.dialog.open(ConfirmDialogComponent, {
      data: {
        title: 'Delete Custom Field',
        message: `Are you sure you want to delete the field "${customField.name}"? This action cannot be undone.`,
        confirmText: 'Delete',
        cancelText: 'Cancel'
      }
    }).afterClosed().subscribe(result => {
      if (result && customField.id) {
        this.customFieldService.deleteCustomField(customField.id).pipe(take(1)).subscribe({
          next: () => this.loadCustomFields(),
          error: (error) => {
            // Handle error in case field has been used in receipts since we checked
            this.dialog.open(ConfirmDialogComponent, {
              data: {
                title: 'Delete Failed',
                message: error.message,
                confirmText: 'OK',
                showCancel: false
              }
            });
          }
        });
      }
    });
  }

  private deleteAllCustomFields(): void {
    // Delete all custom fields
    this.customFieldService.deleteAllCustomFields().pipe(take(1)).subscribe(() => this.loadCustomFields());
  }

  openAddDialog(): void {
    this.dialog.open(AddCustomFieldDialogComponent, {
      width: '500px',
      data: {
        mode: 'add'
      }
    }).afterClosed().subscribe(result => {
      if (result) this.loadCustomFields();
    });
  }

  openEditDialog(customField: CustomField): void {
    this.dialog.open(AddCustomFieldDialogComponent, {
      width: '500px',
      data: {
        mode: 'edit',
        customField: customField
      }
    }).afterClosed().subscribe(result => {
      if (result) this.loadCustomFields();
    });
  }
}
