import { NgModule } from '@angular/core';
import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';

//firestore/firebase
import { provideAuth } from '@angular/fire/auth';
import { getAuth } from 'firebase/auth';
import { AngularFireModule } from '@angular/fire/compat';
import { provideFirestore, getFirestore } from '@angular/fire/firestore';
import { provideFirebaseApp } from '@angular/fire/app';
import { initializeApp } from 'firebase/app';
import { HttpClientModule } from '@angular/common/http';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { TranslocoRootModule } from './transloco-root.module';
import { MatSnackBarModule } from '@angular/material/snack-bar';

@NgModule({
  declarations: [AppComponent],
  imports: [
    BrowserAnimationsModule,
    AppRoutingModule,
    HttpClientModule,
    MatSnackBarModule,
    //firebase stuff
    AngularFireModule,
    AngularFireModule.initializeApp({
      apiKey: 'AIzaSyAnuQTQWRgxeXxiewjG3CUI6j_BjXNX3UM',
      authDomain: 'fiscal-38558.firebaseapp.com',
      projectId: 'fiscal-38558',
      storageBucket: 'fiscal-38558.appspot.com',
      messagingSenderId: '************',
      appId: '1:************:web:8d291e2691bfefcf0ad2f0',
    }),
    provideFirebaseApp(() =>
      initializeApp({
        apiKey: 'AIzaSyAnuQTQWRgxeXxiewjG3CUI6j_BjXNX3UM',
        authDomain: 'fiscal-38558.firebaseapp.com',
        projectId: 'fiscal-38558',
        storageBucket: 'fiscal-38558.appspot.com',
        messagingSenderId: '************',
        appId: '1:************:web:8d291e2691bfefcf0ad2f0',
      })
    ),
    provideAuth(() => getAuth()),
    provideFirestore(() => getFirestore()),
    TranslocoRootModule,
  ],
  exports: [MatSnackBarModule],
  providers: [],
  bootstrap: [AppComponent],
})
export class AppModule {}
