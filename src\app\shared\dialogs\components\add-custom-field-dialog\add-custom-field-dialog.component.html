<h2 mat-dialog-title>{{ dialogTitle }}</h2>
<div mat-dialog-content *transloco="let t">
  <form [formGroup]="customFieldForm">
    <div class="form-group">
      <app-fiscomm-input
        [label]="t('custom_fields.field_name')"
        [control]="nameControl"
        [placeholder]="t('custom_fields.enter_field_name')"
        required="true">
      </app-fiscomm-input>
    </div>

    <div class="form-group">
      <app-fiscomm-input
        [label]="t('custom_fields.field_description')"
        [control]="descriptionControl"
        [placeholder]="t('custom_fields.enter_description')">
      </app-fiscomm-input>
    </div>

    <!-- <div class="form-group">
      <app-fiscomm-select
        placeholder="Field Type"
        [control]="fieldTypeControl"
        [options]="fieldTypes">
      </app-fiscomm-select>
    </div> -->

    <div class="form-group" *ngIf="isSelectType()">
      <h3>{{ t('custom_fields.possible_values') }}</h3>
      <div formArrayName="possibleValues">
        <div *ngFor="let value of possibleValues.controls; let i = index" class="possible-value-row">
          <app-fiscomm-input
            [control]="getPossibleValueControl(i)"
            [placeholder]="t('custom_fields.enter_value')"
            [label]="t('custom_fields.value') + ' ' + (i + 1)">
          </app-fiscomm-input>
          <button type="button" mat-icon-button color="warn" (click)="removePossibleValue(i)"
                  *ngIf="possibleValues.length > 1">
            <mat-icon>delete</mat-icon>
          </button>
        </div>
        <button type="button" mat-stroked-button color="primary" (click)="addPossibleValue()">
          <mat-icon>add</mat-icon> {{ t('custom_fields.add_option') }}
        </button>
      </div>
    </div>

    <div class="form-group">
      <app-fiscomm-input
        *ngIf="fieldTypeControl.value !== 'boolean'"
        [label]="t('custom_fields.default_value')"
        [control]="defaultValueControl"
        [placeholder]="t('custom_fields.enter_value')">
      </app-fiscomm-input>

      <!-- <mat-checkbox *ngIf="fieldTypeControl.value === 'boolean'"
                    formControlName="defaultValue"
                    class="default-value-checkbox">
        Default Value
      </mat-checkbox> -->

      <div class="status-toggle" *ngIf="fieldTypeControl.value === 'boolean'">
        <mat-label class="me-3">{{ t('custom_fields.default_value') }}:</mat-label>
        <mat-slide-toggle formControlName="defaultValue" color="primary">
          {{ customFieldForm.get('defaultValue')?.value ? t('common.yes') : t('common.no') }}
        </mat-slide-toggle>
      </div>
    </div>

    <div class="form-group checkbox-group">

      <div class="status-toggle">
        <mat-label class="me-3">{{ t('custom_fields.required_field') }}:</mat-label>
        <mat-slide-toggle formControlName="isRequired" color="primary">
          {{ customFieldForm.get('isRequired')?.value ? t('custom_fields.required') : t('custom_fields.optional') }}
        </mat-slide-toggle>
      </div>

      <div class="status-toggle">
        <mat-label class="me-3">{{ t('custom_fields.status') }}:</mat-label>
        <mat-slide-toggle formControlName="isActive" color="primary">
          {{ customFieldForm.get('isActive')?.value ? t('custom_fields.active') : t('custom_fields.inactive') }}
        </mat-slide-toggle>
      </div>
    </div>

  </form>
</div>

<div mat-dialog-actions align="end" *transloco="let t">
  <button mat-button (click)="cancel()" type="button">{{ t('custom_fields.cancel') }}</button>
  <button mat-raised-button color="primary" (click)="save()" [disabled]="customFieldForm.invalid" type="submit">
    {{ isEditMode ? t('custom_fields.update') : t('custom_fields.save') }}
  </button>
</div>
