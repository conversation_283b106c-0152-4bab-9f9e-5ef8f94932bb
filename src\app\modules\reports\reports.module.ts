import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Routes } from '@angular/router';
import { ReportsComponent } from './containers/reports/reports.component';
import { SharedModule } from 'src/app/shared/shared.module';


const routes: Routes = [
  {
    path:'',
    component: ReportsComponent
  },
];


@NgModule({
  declarations: [
    ReportsComponent
  ],
  imports: [
    CommonModule,
    SharedModule,
    RouterModule.forChild(routes)
  ]
})
export class ReportsModule { }
