<div class="tab-container" *ngIf="minimizedModals.length > 0" [@slideInOut]>
  <div
    *ngFor="let tab of minimizedModals; let i = index"
    class="tab"
    (click)="openModal(tab.minimizedId)"
  >
    <p>
      {{ tab.name }}
      <span *ngIf="minimizedModals.length > 1 && modalNameCountArr[i] > 0"
        >({{ modalNameCountArr[i] + 1 }})</span
      >
      <mat-icon class="icon" (click)="deleteModal(tab.minimizedId)"
        >close</mat-icon
      >
    </p>
  </div>

  <mat-icon class="icon end" (click)="emptyTabs()">close</mat-icon>
</div>
