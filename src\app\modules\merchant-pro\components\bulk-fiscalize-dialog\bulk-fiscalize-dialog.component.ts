import { Component, OnInit } from '@angular/core';
import { BulkActionsService } from 'src/app/shared/services/bulk-actions.service';
import { MerchantProService } from 'src/app/shared/services/backend/merchant-pro/merchant-pro.service';
import { Order } from 'src/app/shared/services/backend/merchant-pro/types/orders-response.dto';

@Component({
  selector: 'app-bulk-fiscalize-dialog',
  templateUrl: './bulk-fiscalize-dialog.component.html',
  styleUrls: ['./bulk-fiscalize-dialog.component.scss']
})
export class BulkFiscalizeDialogComponent implements OnInit {

  constructor(private bulkActionService: BulkActionsService,private mpService:MerchantProService) { }
  
  orders: Order[] = [];
  orderStatus: string[] = [];
  currentIndex: number = 0;
  allDone: string='waiting';

  ngOnInit(): void {
    this.orders = this.bulkActionService.getSelectedNotFiscalizedElements();
    for (let order of this.orders) 
      this.orderStatus.push('waiting'); 
  }

  startFiscalization() {
    setInterval(() => {
      if (this.currentIndex < this.orders.length) {
        this.allDone = 'progress';
        this.orderStatus[this.currentIndex] = 'progress';

        this.mpService.fiscalise(this.orders[this.currentIndex].id)
          .subscribe((result: any) => {
            this.orderStatus[statusIndex++] = 'done';
            if (this.orderStatus.every(status => status == 'done'))
              this.allDone = 'done';
          });
        this.currentIndex++;
      }
    }, 100);

    let statusIndex = 0;
    this.bulkActionService.emptyAllElements();
  }

}
