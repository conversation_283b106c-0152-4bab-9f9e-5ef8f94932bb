$background-color: #edf5fd;

.invoices-bulk {
  background-color: white;
  border-radius: 0.5rem;
  position: relative;

  .x-button {
    position: absolute;
    top: 0;
    right: 0;
    cursor: pointer;
    background-color: transparent;
    border: none;
    .mat-icon {
      width: 2rem;
      height: 2rem;
      font-size: 2rem;
    }
  }

  .title {
    border-bottom: 4px solid $background-color;
    margin-bottom: 1rem;
    padding: 1rem;
    h1 {
      margin: 0;
      font-weight: bold;
    }
  }

  .filter-group {
    display: flex;
    gap: 1rem;
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .space {
    height: 1rem;
  }
}

.modal-buttons {
  display: flex;
  justify-content: center;
  gap: 1rem;

  .close-button,
  .create-button {
    background-color: #eb8e88;
    border: none;
    cursor: pointer;
    color: white;
    font-size: 1rem;
    font-weight: bold;
    display: flex;
    align-items: center;
    padding: 0rem 1rem;
    border-radius: 0.25rem;
    height: 2.6rem;
    padding-left: 4rem;
    padding-right: 4rem;
  }

  .create-button {
    background-color: #3dccaa;
  }

  .create-button.disabled {
    background-color: gray;
  }

  .hidden {
    display: none;
  }

  .mat-progress-spinner {
    margin-left: 1rem;
  }
}

//tablee---
$background-color: #f1f3f9;

.bulk-table {
  border-collapse: collapse;
  border-radius: 0.5rem;
  width: 100%;
  text-align: center;
  thead {
    border-top: 4px solid $background-color;
    border-bottom: 4px solid $background-color;
    & > tr {
      background-color: white;
    }
  }
  & > tbody {
    & > tr:nth-child(even) {
      background-color: #f0f2fc;
    }
    & > tr:nth-child(odd) {
      background-color: white;
    }
  }

  td {
    padding: 0.6rem 1rem !important;
    border: none;
  }

  th {
    font-size: 1.1rem !important;
    padding: 0.6rem 1rem !important;
  }

  .status {
    font-weight: bold;
    padding: 1rem;
    padding-top: 0.4rem;
    padding-bottom: 0.4rem;
    background-color: #f8d96e;
    color: white;
    border-radius: 0.25rem;
    width: 7rem;
    margin: 0 auto;
  }

  .status.red {
    background-color: #eb8e88;
  }

  .status.green {
    background-color: #3dccaa;
  }
}