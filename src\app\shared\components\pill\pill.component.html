<span class="pill success" *ngIf="color == 'success'">
  <svg
    class="me-2"
    xmlns="http://www.w3.org/2000/svg"
    width="8"
    height="8"
    viewBox="0 0 8 8"
    fill="none"
  >
    <circle cx="4" cy="4" r="4" fill="#3DCCAA" />
  </svg>
  {{ text }}
</span>

<span class="pill primary" *ngIf="color == 'primary'">
  <svg
    class="me-2"
    xmlns="http://www.w3.org/2000/svg"
    width="8"
    height="8"
    viewBox="0 0 8 8"
    fill="none"
  >
    <circle cx="4" cy="4" r="4" fill="#4A85DB" />
  </svg>
  {{ text }}
</span>

<span class="pill danger" *ngIf="color == 'danger'">
  <svg
    class="me-2"
    xmlns="http://www.w3.org/2000/svg"
    width="8"
    height="8"
    viewBox="0 0 8 8"
    fill="none"
  >
    <circle cx="4" cy="4" r="4" fill="red" />
  </svg>
  {{ text }}
</span>

<span class="pill warning" *ngIf="color == 'warning'">
  <svg
    class="me-2"
    xmlns="http://www.w3.org/2000/svg"
    width="8"
    height="8"
    viewBox="0 0 8 8"
    fill="none"
  >
    <circle cx="4" cy="4" r="4" fill="#F9973C" />
  </svg>
  {{ text }}
</span>

<span class="pill yellow" *ngIf="color == 'yellow'">
  <svg
    class="me-2"
    xmlns="http://www.w3.org/2000/svg"
    width="8"
    height="8"
    viewBox="0 0 8 8"
    fill="none"
  >
    <circle cx="4" cy="4" r="4" fill="#F8D96E" />
  </svg>
  {{ text }}
</span>

<span class="pill dark" *ngIf="color == 'dark'">
  <svg
    class="me-2"
    xmlns="http://www.w3.org/2000/svg"
    width="8"
    height="8"
    viewBox="0 0 8 8"
    fill="none"
  >
    <circle cx="4" cy="4" r="4" fill="#939396" />
  </svg>
  {{ text }}
</span>
