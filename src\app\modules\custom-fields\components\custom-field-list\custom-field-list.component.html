<div class="table-container" *transloco="let t">
  <table mat-table [dataSource]="customFields" class="mat-elevation-z2 custom-fields-table"
         cdkDropList (cdkDropListDropped)="onDrop($event)">

    <!-- Order Column -->
    <ng-container matColumnDef="order">
      <th mat-header-cell *matHeaderCellDef>{{ t('custom_fields.order') }}</th>
      <td mat-cell *matCellDef="let field" class="order-cell">{{ field.order }}</td>
    </ng-container>

    <!-- Name Column -->
    <ng-container matColumnDef="name">
      <th mat-header-cell *matHeaderCellDef>{{ t('custom_fields.name') }}</th>
      <td mat-cell *matCellDef="let field">
        <div class="field-name">
          <span>{{ field.name }}</span>
          <span *ngIf="field.description" class="field-description" matTooltip="{{ field.description }}">
            <mat-icon>info</mat-icon>
          </span>
        </div>
      </td>
    </ng-container>

    <!-- Type Column -->
    <ng-container matColumnDef="type">
      <th mat-header-cell *matHeaderCellDef>{{ t('custom_fields.type') }}</th>
      <td mat-cell *matCellDef="let field">{{ getFieldTypeLabel(field.fieldType) }}</td>
    </ng-container>

    <!-- Required Column -->
    <ng-container matColumnDef="required">
      <th mat-header-cell *matHeaderCellDef>{{ t('custom_fields.required_field') }}</th>
      <td mat-cell *matCellDef="let field">
        <mat-icon *ngIf="field.isRequired" color="primary">check_circle</mat-icon>
        <mat-icon *ngIf="!field.isRequired" color="warn">cancel</mat-icon>
      </td>
    </ng-container>

    <!-- Possible Values Column -->
    <ng-container matColumnDef="values">
      <th mat-header-cell *matHeaderCellDef>{{ t('custom_fields.values') }}</th>
      <td mat-cell *matCellDef="let field">
        <span *ngIf="field.fieldType === 'select'">{{ formatPossibleValues(field.possibleValues) }}</span>
        <span *ngIf="field.fieldType !== 'select'">-</span>
      </td>
    </ng-container>

    <!-- Status Column -->
    <ng-container matColumnDef="status">
      <th mat-header-cell *matHeaderCellDef>{{ t('custom_fields.status') }}</th>
      <td mat-cell *matCellDef="let field">
        <span class="status-badge" [ngClass]="field.isActive ? 'active' : 'inactive'">
          {{ field.isActive ? t('custom_fields.active') : t('custom_fields.inactive') }}
        </span>
      </td>
    </ng-container>

    <!-- Actions Column -->
    <ng-container matColumnDef="actions">
      <th mat-header-cell *matHeaderCellDef>{{ t('custom_fields.actions') }}</th>
      <td mat-cell *matCellDef="let field">
        <div class="actions-container">
          <button mat-icon-button color="primary" (click)="onEdit(field)" matTooltip="{{ t('custom_fields.edit') }}">
            <mat-icon>edit</mat-icon>
          </button>
          <button mat-icon-button color="accent" (click)="onViewValues(field)" matTooltip="{{ t('custom_fields.view_receipts') }}">
            <mat-icon>visibility</mat-icon>
          </button>
          <button mat-icon-button color="warn" (click)="onDelete(field)"
                  [disabled]="field.isReceiptCreated"
                  [matTooltip]="field.isReceiptCreated ? t('custom_fields.cannot_delete_message') : t('custom_fields.delete')">
            <mat-icon>delete</mat-icon>
          </button>
          <button mat-icon-button class="drag-handle" matTooltip="{{ t('custom_fields.drag_to_reorder') }}">
            <mat-icon class="drag-indicator">drag_indicator</mat-icon>
          </button>
        </div>
      </td>
    </ng-container>

    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
    <tr mat-row *matRowDef="let row; columns: displayedColumns;"
        cdkDrag [cdkDragData]="row"
        class="draggable-row"></tr>

    <!-- Row shown when there is no data -->
    <tr class="mat-row" *matNoDataRow>
      <td class="mat-cell empty-row" [attr.colspan]="displayedColumns.length">
        {{ t('custom_fields.no_values') }}
      </td>
    </tr>
  </table>
</div>
