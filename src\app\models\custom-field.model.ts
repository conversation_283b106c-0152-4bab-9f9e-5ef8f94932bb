export type CustomFieldType = 'select' | 'text' | 'number' | 'date' | 'boolean';

export interface CustomField {
  id?: string;
  name: string;
  description?: string;
  fieldType: CustomFieldType;
  isRequired: boolean;
  possibleValues?: string[]; // For select type
  defaultValue?: any;
  order: number; // For display ordering
  isActive: boolean;
  createdAt?: Date | any; // Support both JS Date and Firestore Timestamp
  uid?: string;
  isReceiptCreated?: boolean; // Flag indicating the field has been used in receipts
}
