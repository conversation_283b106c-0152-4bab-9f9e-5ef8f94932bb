import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { take } from 'rxjs';
import { TranslocoService } from '@ngneat/transloco';
import { AuthService } from 'src/app/core/services/auth.service';

@Component({
  selector: 'app-password-settings',
  templateUrl: './password-settings.component.html',
  styleUrls: ['./password-settings.component.scss']
})
export class PasswordSettingsComponent implements OnInit {
  passwordForm: FormGroup;
  hideCurrentPassword = true;
  hideNewPassword = true;
  hideConfirmPassword = true;
  errorMessage = '';
  successMessage = '';

  constructor(
    private fb: FormBuilder,
    private authService: AuthService,
    private translocoService: TranslocoService
  ) {
    this.passwordForm = this.fb.group({
      currentPassword: ['', [Validators.required]],
      newPassword: ['', [Validators.required, Validators.minLength(6)]],
      confirmPassword: ['', [Validators.required]]
    }, { validator: this.passwordMatchValidator });
  }

  ngOnInit(): void {
  }

  // Validator to check if password and confirm password match
  private passwordMatchValidator(form: FormGroup) {
    const newPassword = form.get('newPassword')?.value;
    const confirmPassword = form.get('confirmPassword')?.value;

    if (newPassword !== confirmPassword) {
      form.get('confirmPassword')?.setErrors({ mismatch: true });
      return { mismatch: true };
    }

    return null;
  }

  // Get controls for easier access in the template
  get currentPassword() { return this.passwordForm.get('currentPassword') as FormControl; }
  get newPassword() { return this.passwordForm.get('newPassword') as FormControl; }
  get confirmPassword() { return this.passwordForm.get('confirmPassword') as FormControl; }

  changePassword() {
    this.errorMessage = '';
    this.successMessage = '';

    if (this.passwordForm.valid) {
      const { currentPassword, newPassword } = this.passwordForm.value;

      this.authService.changePassword(currentPassword, newPassword)
        .pipe(take(1))
        .subscribe({
          next: () => {
            this.successMessage = this.translocoService.translate('settings_page.password_changed');
            this.passwordForm.reset();
          },
          error: (err) => {
            // Handle different error cases
            if (err.code === 'auth/wrong-password') {
              this.errorMessage = this.translocoService.translate('settings_page.wrong_password');
            } else {
              this.errorMessage = this.translocoService.translate('settings_page.password_error');
            }
          }
        });
    }
  }
}
