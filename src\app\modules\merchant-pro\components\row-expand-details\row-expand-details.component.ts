import { Component, OnInit, Input } from '@angular/core';
import { MerchantProService } from 'src/app/shared/services/backend/merchant-pro/merchant-pro.service';
import {
  Lineitem,
  Order,
} from 'src/app/shared/services/backend/merchant-pro/types/orders-response.dto';

@Component({
  selector: 'app-row-expand-details',
  templateUrl: './row-expand-details.component.html',
  styleUrls: ['./row-expand-details.component.scss'],
})
export class RowExpandDetailsComponent implements OnInit {
  items: Lineitem[] = [];
  @Input() order: Order = {} as Order;
  orderFields: any[] = [];

  constructor(private mpService: MerchantProService) {}

  ngOnInit(): void {
    this.orderFields = this.mpService.getFilterOrderFields(this.order);
    this.items = this.order?.line_items;
  }
}
