import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Routes } from '@angular/router';
import { InvoicesComponent } from './containers/invoices/invoices.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { CreateInvoiceDialogComponent } from './components/create-invoice-dialog/create-invoice-dialog.component';
import { PurchaseInvoicesComponent } from './containers/purchase-invoices/purchase-invoices.component';
import { SaleInvoicesComponent } from './containers/sale-invoices/sale-invoices.component';
import { HomeInvoiceComponent } from './containers/home-invoice/home-invoice.component';
import { SaleInvoicesTableComponent } from './components/sale-invoices-table/sale-invoices-table.component';
import { SaleInvoiceHeaderComponent } from './components/sale-invoice-header/sale-invoice-header.component';

const routes: Routes = [
  {
    path: '',
    component: InvoicesComponent,
    children: [
      {
        path: '',
        component: HomeInvoiceComponent,
      },
      {
        path: 'ulazne',
        component: PurchaseInvoicesComponent,
      },
      {
        path: 'izlazne',
        component: SaleInvoicesComponent,
      },
    ],
  },
];

@NgModule({
  declarations: [
    InvoicesComponent,
    CreateInvoiceDialogComponent,
    HomeInvoiceComponent,
    PurchaseInvoicesComponent,
    SaleInvoicesComponent,
    SaleInvoicesTableComponent,
    SaleInvoiceHeaderComponent,
  ],
  imports: [CommonModule, SharedModule, RouterModule.forChild(routes)],
})
export class InvoicesModule {}
