::ng-deep {
  .ticket-details .mat-dialog-container {
    background-color: white;
  }
  .ticket-details .mat-dialog-title {
    font-size: 32px;
  }
}

h4 {
  font-size: 20px;
  padding-left: 0;
}

.statusRow {
  border-radius: 8px !important;
  padding: 10px 0px !important;
}

.img-container {
  width: 40px !important;
  height: 40px !important;
  border-radius: 100%;
  overflow: hidden;
}
.img-container img {
  width: 100%;
}

.comment-container {
  border: 1px solid #e1e5f2;
  padding: 15px;
  border-radius: 8px;
  p {
    margin: 0;
    margin-left: 7px;
  }
  .tag {
    background-color: #c3f0e5;
    border-radius: 20px;
    padding: 3px 20px;
  }
  .comment {
    padding-top: 15px;
    margin-top: 15px;
    border-top: 1px solid #e1e5f2;
  }
  .footer {
    padding-top: 15px;
    margin-top: 15px;
    border-top: 1px solid #e1e5f2;
  }
  .img-info {
    border: 2px solid #008bff;
    border-radius: 15px;
    padding: 0px 10px;
    cursor: pointer;
  }
  .download-icon {
    font-size: 20px;
    padding-left: 5px;
    padding-top: 3px;
    color: #008bff;
  }
  .mat-icon {
    cursor: pointer;
  }

  .smaller {
    padding-top: 5px;
    font-size: 14px;
    color: #4c626b;
  }

  pre {
    font-family: "Inter", sans-serif;
    font-size: 15px;
    max-width: 100%;
    white-space: pre-wrap;
  }
}

.tag-blue {
  background-color: #dbebfb !important;
  border-radius: 20px;
  padding: 3px 20px;
}

button {
  background-color: #008bff !important;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 5px;
  padding-inline: 20px;
}

textarea {
  width: 100%;
  border-radius: 4px;
  border: 1px solid #e1e5f2;
}

.date_text {
  color: #4c626b;
  font-size: 14px;
}
