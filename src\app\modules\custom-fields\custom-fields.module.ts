import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Routes } from '@angular/router';
import { SharedModule } from 'src/app/shared/shared.module';
import { CustomFieldsComponent } from './containers/custom-fields/custom-fields.component';
import { CustomFieldListComponent } from './components/custom-field-list/custom-field-list.component';
import { CustomFieldsHeaderComponent } from './components/custom-fields-header/custom-fields-header.component';
import { MatTooltipModule } from '@angular/material/tooltip';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { FieldValuesDialogComponent } from './components/field-values-dialog/field-values-dialog.component';
import { MatDialogModule } from '@angular/material/dialog';
import { MatListModule } from '@angular/material/list';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatBadgeModule } from '@angular/material/badge';
import { MatDividerModule } from '@angular/material/divider';
import { MatButtonModule } from '@angular/material/button';

const routes: Routes = [
  {
    path: '',
    component: CustomFieldsComponent
  }
];

@NgModule({
  declarations: [
    CustomFieldsComponent,
    CustomFieldListComponent,
    CustomFieldsHeaderComponent,
    FieldValuesDialogComponent
  ],
  imports: [
    CommonModule,
    SharedModule,
    MatTooltipModule,
    RouterModule.forChild(routes),
    DragDropModule,
    MatDialogModule,
    MatListModule,
    MatProgressSpinnerModule,
    ReactiveFormsModule,
    MatCardModule,
    MatBadgeModule,
    MatDividerModule,
    MatButtonModule
  ],
  exports: [
    CustomFieldListComponent,
    CustomFieldsHeaderComponent
  ]
})
export class CustomFieldsModule { }
