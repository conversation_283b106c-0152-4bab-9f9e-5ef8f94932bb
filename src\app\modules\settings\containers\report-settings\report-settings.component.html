<div
  class="container-fluid ps-lg-5 pe-lg-5 pt-lg-4 pb-lg-4 ps-md-3 pe-lmd-3 pt-md-2 pb-md-2"
  *transloco="let t"
>
  <app-page-title text="{{ t('settings') }}"></app-page-title>
  <app-settings-navbar></app-settings-navbar>

  <app-settings-container title="{{ t('settings_page.email_adress') }}">
    <ng-container content>
      <div class="row">
        <div class="col-12">
          <p>{{ t("settings_page.one_or_more_emails") }}</p>
        </div>
        <ng-container *ngFor="let email of reportsInfo.emails; let i = index">
          <div *ngIf="i > 0" class="col-md-11">
            <app-fiscomm-input
              [control]="getEmailControl(i)"
              placeholder="E-mail"
            ></app-fiscomm-input>
          </div>
          <div
            *ngIf="i > 0"
            class="col-md-1 d-flex align-items-center"
            (click)="removeEmail(i)"
          >
            <mat-icon>delete</mat-icon>
          </div>
          <div *ngIf="i == 0" class="col-md-12">
            <app-fiscomm-input
              [control]="getEmailControl(i)"
              placeholder="E-mail"
            ></app-fiscomm-input>
          </div>
        </ng-container>
        <div class="col-md-12">
          <a class="d-flex align-items-center" (click)="addEmail()">
            <mat-icon>add</mat-icon>{{ t("settings_page.additional_email") }}
          </a>
        </div>
      </div>
      <div class="row">
        <div class="col-md-12">
          <h4>{{ t("settings_page.auto_reports") }}</h4>
        </div>
        <div class="col-md-12">
          <mat-slide-toggle
            (change)="toggleChange('daily')"
            [checked]="reportsInfo.daily"
            color="primary"
            >{{ t("settings_page.input.daily_reports") }}</mat-slide-toggle
          >
        </div>
        <div class="col-md-12">
          <mat-slide-toggle
            (change)="toggleChange('weekly')"
            [checked]="reportsInfo.weekly"
            color="primary"
            >{{ t("settings_page.input.weekly_reports") }}</mat-slide-toggle
          >
        </div>
        <div class="col-md-12">
          <mat-slide-toggle
            (change)="toggleChange('monthly')"
            [checked]="reportsInfo.monthly"
            color="primary"
            >{{ t("settings_page.input.monthly_reports") }}</mat-slide-toggle
          >
        </div>
      </div>
    </ng-container>
    <ng-container buttons>
      <app-success-btn
        text="{{ t('settings_page.save_changes') }}"
        (click)="saveReportsSettings()"
      ></app-success-btn>
    </ng-container>
  </app-settings-container>
</div>
