.table-container {
  width: 100%;
  overflow-y: auto;
  overflow-x: auto; /* Add horizontal scrolling */
}

.orders-table {
  margin: 0px !important;
  th {
    font-family: "Inter" !important;
    font-weight: 600;
    padding-left: 20px;
    font-size: 14px;
    color: #022b3a;

    border-bottom: 3px solid #e1e5f2;
    width: 250px !important;
    text-wrap: nowrap !important;
  }
  td {
    padding-left: 20px;
    font-size: 13px;
    color: #022b3a;
    border-bottom: 1px solid #e1e5f2;
    width: 250px !important;
    text-wrap: nowrap !important;
  }
  .icons {
    display: flex;
    height: 100%;
    cursor: pointer;
  }
}
.expanded-row {
  background-color: #edf5fd;
}
.detail-row {
  background-color: #edf5fd;
}

tr.detail-row {
  height: 0;
}

tr.element-row:not(.expanded-row):hover {
  background: #edf5fd;
}

.element-row td {
  border-bottom-width: 0;
}

.element-detail {
  overflow: hidden;
  width: 100%;
}

.mat-table {
  table-layout: fixed;
  min-width: 300px;
}

.mat-header-cell.stickyEnd {
  position: sticky;
  left: 0;
  z-index: 1;
  background-color: red;
}

.icon {
  cursor: pointer;
  font-size: 20px;
  margin-top: 9px;
}

.element-row td {
  border-bottom-width: 0;
}

.element-detail {
  overflow: hidden;
  width: 100%;
}

::ng-deep.mat-menu-panel {
  max-height: 200px !important;
  max-width: 180px !important;
}

button {
  width: 100%;
  display: block;
  text-align: left;
}

.btn {
  background-color: #008bff;
  border: none;
  cursor: pointer;
  color: white;
  justify-content: center;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  padding: 0rem 1rem;
  border-radius: 0.25rem;
  height: 2.6rem;
  width: 100%;
}
