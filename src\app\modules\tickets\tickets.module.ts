import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TicketsComponent } from './containers/tickets/tickets.component';
import { RouterModule, Routes } from '@angular/router';
import { SharedModule } from 'src/app/shared/shared.module';
import { TicketsTableComponent } from './components/tickets-table/tickets-table.component';
import { TicketsMenuComponent } from './components/tickets-menu/tickets-menu.component';
import { InProgressTicketsComponent } from './containers/in-progress-tickets/in-progress-tickets.component';
import { TicketDetailsDialogComponent } from './components/ticket-details-dialog/ticket-details-dialog.component';
import { NewTicketDialogComponent } from './components/new-ticket-dialog/new-ticket-dialog.component';
import { FinishedTicketsComponent } from './containers/finished-tickets/finished-tickets.component';
import { ClosedTicketsComponent } from './containers/closed-tickets/closed-tickets.component';

const routes: Routes = [
  {
    path: '',
    redirectTo: 'u-toku',
    pathMatch: 'full',
  },
  {
    path: '',
    component: TicketsComponent,
    children: [
      {
        path: 'u-toku',
        component: InProgressTicketsComponent,
      },
      {
        path: 'zavrseni',
        component: FinishedTicketsComponent,
      },
      {
        path: 'zatvoreni',
        component: ClosedTicketsComponent,
      },
    ],
  },
];

@NgModule({
  declarations: [
    TicketsComponent,
    TicketsTableComponent,
    TicketsMenuComponent,
    InProgressTicketsComponent,
    TicketDetailsDialogComponent,
    NewTicketDialogComponent,
    FinishedTicketsComponent,
    ClosedTicketsComponent,
  ],
  imports: [CommonModule, RouterModule.forChild(routes), SharedModule],
})
export class TicketsModule {}
