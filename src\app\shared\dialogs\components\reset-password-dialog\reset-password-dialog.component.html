<ng-container *transloco="let t">
    <h2 mat-dialog-title>{{t('login_page.pass_reset')}}</h2>
    <mat-dialog-content class="d-flex align-items-center">
        <div class="row w-100">
            <div class="col-md-12">
                <app-fiscomm-input class="w-100" [control]="emailControl" type="email" placeholder="{{t('login_page.input.email')}}"></app-fiscomm-input>
            </div>
            <div class="col-md-12 error" *ngIf="error">
                <p>{{t('login_page.user_not_found')}}</p>
            </div>
            <div class="col-md-12 success" *ngIf="success">
                <p>{{t('login_page.email_sent')}}</p>
            </div>
        </div>
    </mat-dialog-content>
    <mat-dialog-actions class="buttons">
        <app-danger-btn [mat-dialog-close]="false" text="{{t('cancel')}}"></app-danger-btn>
        <app-success-btn (click)="resetPassword()" [text]="buttonText" class="me-4" [disabled]="disabled"></app-success-btn>
    </mat-dialog-actions>

</ng-container>