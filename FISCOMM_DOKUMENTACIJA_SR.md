# FisComm Klijent v2 - Kompletna Dokumentacija

## <PERSON><PERSON><PERSON><PERSON>

1. [Informacije o novoj verziji](#informacije-o-novoj-verziji)
2. [<PERSON><PERSON><PERSON><PERSON><PERSON> vodič](#korisnički-vodič)
3. [<PERSON><PERSON><PERSON><PERSON> za uvoz proizvoda](#vodič-za-uvoz-proizvoda)
4. [<PERSON><PERSON><PERSON><PERSON> za prilagođena polja](#vodič-za-prilagođena-polja)
5. [Najbolje prakse i preporuke](#najbolje-prakse)
6. [Često postavljana pitanja (FAQ)](#često-postavljana-pitanja)

---

Dobrodošli u FisComm Klijent v2! Ova dokumentacija sadrži sve što je potrebno da biste u potpunosti iskoristili sve mogućnosti aplikacije. Dizajnirana je kao sveobuhvatni resurs za sve tipove korisnika, od početnika do naprednih.

**Šta je novo u verziji 2.0?**

Nova verzija donosi značajna poboljšanja performansi, upotrebljivosti i funkcionalnosti, uključujući:
- Uvoz proizvoda putem TSV datoteka
- Upravljanje više proizvoda odjednom
- Prilagođena polja za račune
- Podrazumevanog kasira za brže kreiranje računa
- Poboljšane mogućnosti pretrage
- I mnogo toga drugog!

Nastavite čitanje da biste saznali sve o novim funkcijama i kako ih koristiti na najbolji način.

---

<a id="informacije-o-novoj-verziji"></a>
# Informacije o novoj verziji

## Verzija 2.0.0 (Maj 2025)

Sa zadovoljstvom vam predstavljamo FisComm Klijent v2, značajno unapređenje vašeg sistema za upravljanje računima! Ova verzija uključuje znatna poboljšanja performansi, upotrebljivosti i funkcionalnosti.

### Nove funkcije

#### Upravljanje proizvodima

- **Sistem za masovni uvoz**: Uvoz više proizvoda odjednom korišćenjem TSV formata
  - Detekcija i rešavanje duplikata
  - Validacija obaveznih polja
  - Interaktivna uputstva za format uvoza
  - Inteligentni algoritmi za deduplikaciju
  - Procesiranje u grupama za velike uvozne datoteke

- **Grupne akcije**: Upravljanje sa više proizvoda istovremeno
  - Izbor pojedinačnih ili svih proizvoda
  - Brisanje više proizvoda u jednoj operaciji
  - Jasan vizuelni prikaz izabranih stavki

- **Poboljšanja pri kreiranju proizvoda**:
  - Dodata validacija za popust (maksimalno 99%)
  - Bolje rukovanje greškama i povratne informacije
  - Dodato polje za datum kreiranja radi boljeg praćenja

#### Prilagođena polja

- **Upravljanje prilagođenim poljima**:
  - Kreiranje i upravljanje prilagođenim poljima za račune
  - Tip polja: Izbor (padajući meni)
  - Postavljanje polja kao obaveznih ili opcionalnih
  - Dodavanje opisa za korisničko uputstvo
  - Prilagođavanje redosleda opcija pomoću prevuci-i-pusti

- **Praćenje korišćenja polja**:
  - Sprečavanje brisanja polja koja se koriste
  - Vizuelni indikatori korišćenja polja
  - Zastava "isReceiptCreated" za zaštitu korišćenih polja

- **Vrednosti za pretragu**:
  - Automatsko čuvanje prethodno korišćenih vrednosti
  - Brzi izbor uobičajenih vrednosti
  - Predlaganje vrednosti tokom kucanja
  - Prelazak sa nizova na potškolekcije za bolje performanse

#### Upravljanje računima

- **Validacija formulara**:
  - Poboljšana vidljivost grešaka
  - Indikatori obaveznih polja
  - Poruke validacije u liniji
  - Detaljna validacija za prilagođena polja

- **Poboljšanja performansi**:
  - Upiti na zahtev sa paginacijom
  - Optimizovana struktura podataka
  - Glatke animacije za proširivanje/skupljanje

#### Upravljanje kasirima

- **Podrazumevani kasir**:
  - Postavljanje preferiranog kasira za brže operacije
  - Integracija sa podešavanjima fiskalne kase
  - Vizuelna indikacija zlatnom zvezdicom

- **Poboljšani interfejs**:
  - Obojene ikone za bolju vidljivost (plava za uređivanje, zlatna za omiljeno, crvena za brisanje)
  - Konzistentan stil

### Poboljšanja

#### Korisničko iskustvo

- **Animacije**: Glatki prelazi za proširivanje/skupljanje detalja računa
- **Stanja učitavanja**: Bolji vizuelni feedback tokom operacija sa podacima
- **Rukovanje greškama**: Detaljnije poruke o greškama
- **Validacija polja**: Trenutne povratne informacije o unosima u formularima
- **Lepljiva traka za grupne akcije**: Poboljšana vidljivost pri upravljanju više proizvoda

#### Performanse

- **Struktura podataka**: Promena sa nizova na potškolekcije za bolju skalabilnost
- **Optimizacija upita**: Učitavanje podataka na zahtev za brže inicijalno učitavanje
- **Keširanje**: Poboljšano keširanje često pristupanih podataka
- **Paginacija**: Funkcionalnost "Učitaj još" za velike skupove rezultata

#### Bezbednost

- **Firestore pravila**: Poboljšana sigurnosna pravila za potškolekciju metaFields
- **Validacija**: Validacija na strani servera za sve operacije
- **Integritet podataka**: Sprečavanje problema sa vremenskim oznakama u operacijama niza

### Lokalizacija

- Dodati potpuni prevodi za sve nove funkcije
- Podrška za engleski i srpski jezik
- Konzistentna terminologija kroz celu aplikaciju
- Prevedeni tipovi polja i predlošci za uvoz

---

<a id="korisnički-vodič"></a>
# Korisnički vodič

## Šta je novo u verziji 2.0

Radujemo se što vam donosimo najnovije ažuriranje FisComm-a sa nekoliko novih funkcija dizajniranih da učine vaš rad bržim i efikasnijim!

### Ključne nove funkcije

- **Sistem za uvoz proizvoda**: Lako uvezite više proizvoda odjednom koristeći jednostavan format tabele
- **Grupno upravljanje proizvodima**: Izaberite i upravljajte sa više proizvoda istovremeno
- **Prilagođena polja**: Kreirajte i upravljajte prilagođenim poljima za vaše račune
- **Podešavanje podrazumevanog kasira**: Postavite podrazumevanog kasira za brže kreiranje računa
- **Poboljšanja pretrage računa**: Poboljšane mogućnosti pretrage za pronalaženje računa
- **Optimizacije performansi**: Brže vreme učitavanja i glatkije animacije

## Pregled uvoza proizvoda

### Kako uvesti proizvode

1. **Idite** na stranicu Proizvodi
2. **Kliknite** na dugme "Uvoz proizvoda" (ikona ↑) u gornjem desnom uglu
3. **Pregledajte** format uvoza klikom na dugme "Format za uvoz proizvoda"
4. **Pripremite** TSV datoteku prema predlošku
5. **Kliknite** "Uvoz proizvoda" i izaberite vašu datoteku
6. **Pregledajte** sve duplikate koji su pronađeni i izaberite koje želite da zadržite
7. **Potvrdite** vaš izbor da završite uvoz

### Kreiranje datoteke za uvoz

Možete koristiti bilo koji program za tabelarne proračune (Excel, Google Sheets, LibreOffice Calc) za kreiranje datoteke za uvoz:

1. **Kreirajte** novu tabelu sa sledećim kolonama:
   - `name` (obavezno): Naziv vašeg proizvoda
   - `price` (obavezno): Cena u RSD
   - `gtin` (opciono): Broj barkoda
   - `productCode` (opciono): Vaša interna šifra proizvoda

2. **Unesite** podatke o vašim proizvodima u odgovarajuće kolone

3. **Sačuvajte** kao "TSV (Tab-Separated Values)" format
   - U Excel-u: Datoteka → Sačuvaj kao → Izaberite "Tekst (Tab delimited) (*.txt)"
   - U Google Sheets: Datoteka → Preuzmi → Tab-separated values (.tsv)

4. Vaša datoteka je sada spremna za uvoz!

### Rukovanje duplikatima

Tokom uvoza, sistem proverava duplikate na dva načina:

1. **Unutar datoteke za uvoz**: Ako više proizvoda ima isti GTIN ili šifru proizvoda, samo prvi će biti uvezen.

2. **Nasuprot postojećim proizvodima**: Ako uvezeni proizvodi odgovaraju postojećim, videćete dijalog gde možete izabrati koje zadržati:
   - Izaberite "Zadrži postojeći" da zadržite vaše trenutne podatke o proizvodu
   - Izaberite "Koristi novi" da ažurirate sa uvezenim podacima

## Grupno upravljanje proizvodima

### Kako koristiti višestruki izbor

1. **Idite** na stranicu Proizvodi
2. **Označite** polja pored proizvoda kojima želite da upravljate
3. **Koristite** polje "Izaberi sve" u zaglavlju da izaberete sve proizvode
4. **Kliknite** na dugme "Obriši izabrane" da uklonite više proizvoda odjednom
5. Pojaviće se dijalog za potvrdu pre brisanja

## Pregled prilagođenih polja

### Kreiranje prilagođenih polja

1. **Idite** na stranicu Podešavanja
2. **Izaberite** karticu "Prilagođena polja"
3. **Kliknite** dugme "+" da dodate novo polje
4. **Unesite** detalje o polju:
   - Naziv: Kako će se polje prikazivati korisnicima
   - Opis: Opcioni tekst opisa polja
   - Opciono, možete dodati predefinisane opcije za polje:
     - Kliknite "Dodaj opciju" 
     - Unesite svaku vrednost opcije
     - Koristite ručke za prevlačenje da promenite redosled opcija ako je potrebno
   - Obavezno: Da li polje mora biti popunjeno pri kreiranju računa

### Korišćenje prilagođenih polja u računima

1. Pri kreiranju novog računa, prilagođena polja će se pojaviti u formularu
2. Popunite sva obavezna prilagođena polja (označena sa "*")
3. Vrednosti prilagođenih polja će biti sačuvane sa računom

### Upravljanje vrednostima pretrage

1. Sistem će predlagati prethodno korišćene vrednosti pri popunjavanju prilagođenih polja
2. Možete videti sve korišćene vrednosti klikom na ikonu pretrage pored polja
3. Najčešće vrednosti će se prikazati na vrhu predloga

## Podrazumevani kasir

### Kako postaviti podrazumevanog kasira

1. **Idite** na "Kasiri" u glavnom meniju
2. Pregledajte listu svih registrovanih kasira u vašem sistemu
3. Pronađite kasira kojeg želite postaviti kao podrazumevanog
4. Kliknite na ikonu zvezdice (☆) pored njegovog imena - postaće zlatna (★) kada je aktivna
5. Sistem će prikazati poruku potvrde: "Podrazumevani kasir je uspešno postavljen"

### Prednosti korišćenja podrazumevanog kasira

- Brže kreiranje računa - podrazumevani kasir se automatski bira
- Smanjena mogućnost ljudske greške pri dodeljivanju računa kasirima
- Pojednostavljen tok rada tokom smena sa jednim aktivnim kasirom
- Jasna vizuelna indikacija koji je kasir trenutno podrazumevani

## Stranica za upravljanje kasirima

### Pristup stranici kasira

1. **Idite** na "Kasiri" u glavnom meniju
2. Stranica prikazuje kompletan pregled svih kasira u sistemu

### Funkcionalnosti stranice kasira

1. **Pregled svih kasira**:
   - Lista svih kasira sa njihovim detaljima (ime, kod, status, napomene)
   - Vizuelna indikacija za podrazumevanog kasira

2. **Dodavanje novog kasira**:
   - Kliknite na dugme "Dodaj kasira" na vrhu stranice
   - Popunite formular sa osnovnim informacijama:
     - Ime kasira
     - Kod kasira
     - Napomene (opciono)
     - Status aktivnosti

3. **Uređivanje postojećeg kasira**:
   - Kliknite na ikonu za uređivanje pored željenog kasira
   - Ažurirajte informacije u formularu
   - Sačuvajte promene

4. **Brisanje kasira**:
   - Kliknite na ikonu za brisanje pored željenog kasira
   - Potvrdite brisanje u dijalogu

5. **Postavljanje podrazumevanog kasira**:
   - Kliknite na ikonu zvezdice pored željenog kasira
   - Zvezdica će postati zlatna, označavajući podrazumevanog kasira

---

<a id="vodič-za-prilagođena-polja"></a>
# Vodič za prilagođena polja

## Uvod u prilagođena polja

Prilagođena polja vam omogućavaju da dodate dodatne informacije vašim računima pored standardnih polja. Ova funkcija je savršena za preduzeća koja trebaju pratiti specifične podatke za izveštavanje, usklađenost ili organizacione svrhe.

## Podešavanje prilagođenih polja

### Kreiranje vašeg prvog prilagođenog polja

1. Prijavite se na vaš FisComm nalog
2. Idite na "Podešavanja" iz glavnog menija
3. Izaberite karticu "Prilagođena polja"
4. Kliknite dugme "+" da dodate novo prilagođeno polje
5. Popunite sledeće informacije:
   - **Naziv**: Oznaka koja će se pojaviti na formularima i izveštajima
   - **Obavezno**: Uključite ako ovo polje mora biti popunjeno za sve račune
   - **Opis**: Opcioni tekst pomoći za objašnjenje svrhe polja

6. Dodajte opcije za izbor:
   - Kliknite "Dodaj opciju" 
   - Unesite svaku vrednost opcije
   - Koristite ručke za prevlačenje da promenite redosled opcija ako je potrebno

7. Kliknite "Sačuvaj" da kreirate vaše prilagođeno polje

### Upravljanje postojećim prilagođenim poljima

Sa kartice Prilagođena polja, možete:

- **Urediti polje**: Kliknite ikonu olovke (✏️) pored bilo kojeg polja
- **Obrisati polje**: Kliknite ikonu kante (🗑️) (dostupno samo za polja koja nisu korišćena u računima)
- **Pregled korišćenja**: Vidite koliko računa koristi svako prilagođeno polje

## Korišćenje prilagođenih polja u računima

### Pri kreiranju računa

1. Idite na "Računi" i kliknite "Kreiraj račun"
2. Popunite standardne informacije o računu
3. Pomerite se dole da pronađete sekciju sa prilagođenim poljima
4. Popunite sva obavezna prilagođena polja (označena sa *)
5. Opciona polja mogu ostati prazna ako nisu primenjiva

### Predlozi vrednosti

Dok koristite prilagođena polja za izbor, sistem će vam olakšati odabir:

1. Kliknite na padajući meni u prilagođenom polju
2. Pojaviće se lista sa definisanim opcijama
3. Izaberite željenu opciju sa liste
4. Vaš izbor će biti sačuvan sa računom

## Upravljanje opcijama izbora

### Pregled svih opcija

1. U dijalogu za kreiranje računa, kliknite na padajući meni bilo kojeg prilagođenog polja
2. Pojaviće se lista sa svim dostupnim opcijama
3. Opcije su prikazane prema redosledu koji ste definisali prilikom kreiranja polja
4. Kliknite na željenu opciju da je izaberete za vaš trenutni račun

### Primeri upotrebe prilagođenih polja

Prilagođena polja sa izborom opcija su korisna za mnoge poslovne slučajeve:

#### Primer 1: Odeljenja
- **Naziv**: "Odeljenje"
- **Opis**: "Izaberite kojem odeljenju pripada ovaj račun"
- **Obavezno**: Da
- **Opcije**: 
  - "Prodaja"
  - "Marketing"
  - "Operacije"
  - "Administracija"
  - "Drugo"

#### Primer 2: Zemlja kupca
- **Naziv**: "Zemlja kupca"
- **Opis**: "Izaberite zemlju porekla kupca"
- **Obavezno**: Ne
- **Opcije**: 
  - "Srbija"
  - "Hrvatska"
  - "Bosna i Hercegovina"
  - "Crna Gora"
  - "Drugo"

#### Primer 3: Ime kupca 
- **Naziv**: "Ime kupca"
- **Opis**: "Izaberite ili unesite ime kupca"
- **Obavezno**: Ne
- **Opcije**: 
  - [lista najčešćih kupaca će se automatski popunjavati]

## Najbolje prakse

- **Koristite jasne nazive**: Izaberite nazive polja koji jasno ukazuju na to koje informacije su potrebne
- **Dodajte opise**: Koristite polje za opis da pružite kontekst i primere
- **Ograničite obavezna polja**: Označite polja kao obavezna samo ako su zaista neophodna
- **Ograničite broj opcija**: Držite listu opcija razumno kratkom (idealno manje od 10)
- **Logički organizujte opcije**: Razmislite o redosledu opcija - najčešće korišćene stavite na vrh
- **Razmotrite izveštavanje**: Razmislite o tome kako ćete želeti da filtrirate ili grupišete po ovom polju u izveštajima

## Ograničenja

- Možete kreirati do 20 prilagođenih polja
- Prilagođena polja u upotrebi (koja se pojavljuju na računima) ne mogu se izbrisati
- Svako polje može imati do 50 opcija za izbor
- Nazivi opcija su ograničeni na 100 karaktera

---

<a id="najbolje-prakse"></a>
# Najbolje prakse i preporuke

Ovaj odeljak pruža preporučene pristupe za efikasno implementiranje i korišćenje ključnih funkcija FisComm-a u različitim poslovnim scenarijima.

## Podešavanje kataloga proizvoda

### Efikasna organizacija kataloga proizvoda

Dobro organizovan katalog proizvoda čini svakodnevne operacije efikasnijim. Razmotrite ove pristupe:

1. **Dosledne konvencije imenovanja**
   - Koristite standardni format za nazive proizvoda (npr. "[Brend] [Proizvod] [Veličina/Varijanta]")
   - Primer: "Coca Cola Zero 0.5L" umesto "0.5L Koka Kola Zero"
   - Ovo osigurava da se proizvodi logično pojavljuju na abecednim listama

2. **Upravljanje GTIN-om**
   - Uvek koristite puni GTIN/barkod sa pakovanja proizvoda
   - Proverite tačnost GTIN-a skenerom barkoda pre dodavanja u sistem
   - Za proizvode bez barkoda, razmislite o kreiranju i štampanju sopstvenih

3. **Šifre proizvoda**
   - Razvijte sistematski pristup šiframa proizvoda
   - Primer sistema: [Kod kategorije][Redni broj]
   - Za pića: BEV001, BEV002, itd.
   - Za prehrambene artikle: FOD001, FOD002, itd.

4. **Strategija grupne obrade**
   - Grupišite slične proizvode prilikom uvoza
   - Kreirajte odvojene datoteke za uvoz za različite kategorije proizvoda
   - Planirajte redovna ažuriranja da održite katalog aktuelnim

### Održavanje kataloga proizvoda

Redovno održavanje pomaže da vaš katalog proizvoda ostane tačan i koristan:

1. **Planirajte redovne preglede** (mesečno ili kvartalno)
2. **Arhivirajte prekinute proizvode** umesto da ih brišete
3. **Ažurirajte cene u grupama** koristeći funkciju uvoza
4. **Proveravajte podatke o proizvodima** sa fizičkim inventarom periodično
5. **Očistite duplikate proizvoda** koristeći alate za grupni izbor

## Implementacija prilagođenih polja

### Strategija prilagođenih polja prema tipu poslovanja

Različiti tipovi poslovanja imaju koristi od različitih prilagođenih polja:

#### Maloprodajno poslovanje
- **Referenca narudžbe**: Polje za izbor sa opcijama poput "Online narudžba", "Telefonska narudžba", "U prodavnici"
- **Promotivna kampanja**: Polje za izbor sa trenutnim marketinškim akcijama
- **Politika povraćaja**: Polje za izbor sa opcijama poput "Standardno", "Produženo" ili "Konačna prodaja"
- **Zemlja kupca**: Polje za izbor sa zemljama iz kojih dolaze kupci
- **Ime kupca**: Polje za izbor sa često posećenim kupcima

### Principi dizajna polja

Dizajnirajte vaša prilagođena polja imajući na umu ove principe:

1. **Počnite samo sa osnovnim poljima** - možete dodati više kasnije
2. **Koristite jasne, koncizne nazive** koje će svi razumeti
3. **Dodajte korisne opise** za polja koja bi mogla biti zbunjujuća
4. **Razmislite o potrebama izveštavanja** prilikom dizajniranja polja
5. **Testirajte tok rada** sa stvarnim korisnicima pre finaliziranja

## Tokovi rada za upravljanje računima

### Maloprodaja velikog obima

Za preduzeća koja obrađuju mnogo transakcija brzo:

1. **Postavite podrazumevanog kasira** na početku svake smene
2. **Minimizujte obavezna prilagođena polja** da ubrzate unos
3. **Koristite skenere barkoda** za brzo dodavanje proizvoda
4. **Kreirajte pakete proizvoda** za često prodavane kombinacije
5. **Implementirajte prečice na tastaturi** za bržu navigaciju

### Profesionalne usluge

Za preduzeća koja prate detaljne informacije po transakciji:

1. **Koristite prilagođena polja za hvatanje detalja o klijentu**
2. **Postavite obavezna polja specifična za uslugu**
3. **Implementirajte referentne brojeve projekta/slučaja**
4. **Konfigurišite detaljne predloške računa sa uslovima**
5. **Postavite tokove odobravanja kada je potrebno**

## Sigurnosne najbolje prakse

### Zaštita vaših poslovnih podataka

1. **Kontrole pristupa korisnika**
   - Kreirajte korisničke naloge za svakog člana osoblja
   - Dodelite odgovarajuće nivoe dozvola
   - Nikada ne delite pristupne podatke
   - Deaktivirajte naloge kada osoblje ode

2. **Sigurnost sistema**
   - Omogućite dvofaktorsku autentifikaciju
   - Koristite jake, jedinstvene lozinke
   - Redovno pregledajte logove pristupa
   - Držite softver ažuriranim

3. **Strategija rezervnih kopija podataka**
   - Omogućite automatske rezervne kopije u oblaku
   - Planirajte periodične lokalne rezervne kopije
   - Periodično testirajte restauraciju podataka
   - Dokumentujte procedure rezervnih kopija

---

<a id="često-postavljana-pitanja"></a>
# Često postavljana pitanja (FAQ)

Ovaj odeljak pokriva često postavljana pitanja i uobičajene scenarije rešavanja problema za sve funkcije FisComm Client v2.

### Opšta pitanja

**P: Kako ažuriram na najnoviju verziju FisComm Klijenta?**
O: Web aplikacija se ažurira automatski kada joj pristupite.

**P: Da li se moji podaci automatski čuvaju?**
O: Da, svi podaci se čuvaju u oblaku i redovno se prave rezervne kopije.

**P: Kako pristupam FisComm-u sa više uređaja?**
O: Jednostavno se prijavite sa vašim pristupnim podacima naloga na bilo kojem podržanom uređaju. Vaši podaci će biti sinhronizovani na svim vašim uređajima.

**P: Koji su pretraživači podržani?**
O: FisComm Klijent v2 zvanično podržava Chrome, Firefox, Safari i Edge (baziran na Chromium-u). Internet Explorer nije podržan.

### Upravljanje proizvodima

**P: Koliko proizvoda mogu uvesti odjednom?**
O: Iako ne postoji strogo tehničko ograničenje, preporučujemo uvoz ne više od 200 proizvoda odjednom za optimalne performanse.

**P: Mogu li ažurirati postojeće proizvode putem funkcije uvoza?**
O: Da, sistem će otkriti duplikate na osnovu GTIN-a ili šifre proizvoda i ponuditi opcije da zadržite postojeće podatke ili ažurirate sa uvezenim podacima.

**P: Da li postoji način za izvoz mog kataloga proizvoda?**
O: Ne, trenutno nema opcije za izvoz kataloga proizvoda.

**P: Mogu li koristiti CSV datoteku umesto TSV za uvoz?**
O: Ne, trenutno nema opcije za uvoz CSV datoteka.

**P: Šta se dešava ako izaberem više proizvoda i slučajno ih obrišem?**
O: Videćete dijalog za potvrdu pre nego što dođe do brisanja.

### Prilagođena polja

**P: Mogu li obrisati prilagođeno polje?**
O: Da, ali samo ako se ne koristi ni u jednom postojećem računu. Polja u upotrebi će prikazati upozorenje i ne mogu se obrisati. U slučaju da želite da sakrijete polje, možete ga označiti kao "Neaktivno".

**P: Koliko prilagođenih polja mogu kreirati?**
O: Neograničeno.

**P: Mogu li duplicirati postojeće prilagođeno polje?**
O: Ne, trenutno nema opcije za dupliciranje prilagođenih polja.

**P: Mogu li kreirati prilagođeno polje koje dozvoljava višestruki izbor?**
O: Ne, trenutno je moguće izabrati samo jednu opciju iz padajućeg menija. Za prikupljanje više informacija, razmotrite kreiranje više različitih polja.

**P: Postoji li način da se polje učini obaveznim samo u određenim situacijama?**
O: Polja su ili uvek obavezna ili uvek opciona. Za uslovne zahteve, kreirajte detaljne opise koji objašnjavaju kada vrednosti treba da budu pružene.

### Upravljanje kasirima

**P: Kako napraviti novog kasira?**
O: Idite na Kasiri, kliknite na dugme "Dodaj kasira" i popunite formular sa osnovnim informacijama.

**P: Kako postaviti podrazumevanog kasira?**
O: Idite na Kasiri, pronađite kasira kojeg želite postaviti kao podrazumevanog i kliknite ikonu zvezdice da ga označite kao podrazumevanog. Takođe, možete postaviti podrazumevanog kasira na stranici Podešavanja kase.(Podešavanja -> Podešavanje kase)

### Upravljanje računima

**P: Kako mogu da vidim proizvoljno polje u tabeli računa?**
O: Kliknite na zupčanik u tabeli računa i izaberite kolonu koju želite da dodate ili uklonite.

**P: Kako pretražujem račune po vrednostima prilagođenih polja?**
O: U pretrazi računa, kliknite "Napredna pretraga" i videćete opcije za pretragu po bilo kojem prilagođenom polju.

### Performanse i tehnički problemi

**P: Aplikacija se čini sporom. Kako mogu poboljšati performanse?**
O: Pokušajte očistiti keš vašeg pretraživača, zatvoriti nekorišćene kartice i osigurati da imate stabilnu internet vezu. Za veće skupove podataka, koristite filtere da suzite rezultate.

**P: Dobijam grešku pri uvozu proizvoda. Šta treba da proverim?**
O: Proverite da li je vaša datoteka sačuvana kao TSV format, proverite da li su sva obavezna polja popunjena i osigurajte da nema problema s formatiranjem sa specijalnim znakovima ili cenama.

**P: Aplikacija se uopšte ne učitava. Šta da radim?**
O: Pokušajte sledeće:
1. Očistite keš i kolačiće vašeg pretraživača
2. Pokušajte s drugim pretraživačem
3. Proverite vašu internet vezu
4. Onemogućite ekstenzije pretraživača koje bi mogle uticati
5. Kontaktirajte podršku ako problem i dalje postoji

**P: Kako promenim jezik aplikacije?**
O: Kliknite ikonu vašeg profila u gornjem desnom uglu, izaberite "Podešavanja" i izaberite vaš preferirani jezik iz padajućeg menija. FisComm trenutno podržava engleski i srpski.


*Dokument poslednji put ažuriran: Maj 2025* 
