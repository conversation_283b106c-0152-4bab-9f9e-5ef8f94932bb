import { BreakpointObserver } from '@angular/cdk/layout';
import { Component, OnInit } from '@angular/core';
import { DialogService } from 'src/app/shared/dialogs/dialog.service';
import { CashierService } from 'src/app/shared/services/firestore/cashier.service';
import { Cashier } from 'src/app/models/cashier.model';
import { ClientService } from '../../../../shared/services/backend/clients/client.service';
import { take } from 'rxjs';
import { AuthService } from 'src/app/core/services/auth.service';

@Component({
  selector: 'app-cashiers',
  templateUrl: './cashiers.component.html',
  styleUrls: ['./cashiers.component.scss']
})
export class CashiersComponent implements OnInit {
  columns = ['cashierCode', 'name', 'status', 'notes', 'createdAt', 'actions'];
  cashiers: Cashier[] = [];
  defaultCashierName: string | null = null;
  clientData: any;
  uid: string = '';
  constructor(
    private cashierService: CashierService,
    private dialogService: DialogService,
    private breakpointObserver: BreakpointObserver,
    private clientService: ClientService,
    private authService: AuthService
  ) {}

  ngOnInit(): void {
    this.loadCashiers();
    this.loadDefaultCashierFromSettings();
    this.authService
      .getUserId()
      .pipe(take(1))
      .subscribe((uid: any) => {
        this.uid = uid;
      });
  }

  loadCashiers(): void {
    this.cashierService.getCashiers().subscribe(
      (cashiers) => this.cashiers = cashiers
    );
  }

  loadDefaultCashierFromSettings(): void {
    this.clientService.getClient().subscribe(client => {
      this.clientData = client;
      this.defaultCashierName = client.defaultSettings?.defaultCashier;
    });
  }

  handleActions(event: any): void {
    switch (event.action.toLowerCase()) {
      case 'delete':
        this.deleteCashier(event.data);
        break;
      case 'add':
        this.openAddDialog();
        break;
      case 'edit':
        this.openEditDialog(event.data);
        break;
      case 'toggle-default':
        this.toggleDefaultCashier(event.data);
        break;
    }
  }

  private toggleDefaultCashier(cashier: Cashier): void {
    const isDefault = this.defaultCashierName === cashier.name;

    this.clientData.defaultSettings.defaultCashier = isDefault ? null : cashier.name;
    this.clientService.updateClient(this.uid, this.clientData).subscribe(
      () => {
        this.ngOnInit();
      }
    );
  }

  private deleteCashier(cashier: Cashier): void {
    if (cashier.id && confirm('Are you sure you want to delete this cashier?')) {
      this.cashierService.deleteCashier(cashier.id).subscribe(
        () => this.loadCashiers()
      );
    }
  }

  private openEditDialog(cashier: Cashier): void {
    let dialogConfig = {
      width: '50%',
      height: 'fit-content',
      data: cashier
    };

    this.breakpointObserver.observe(['(max-width: 576px)']).subscribe((result) => {
      if (result.matches) {
        dialogConfig.width = '90%';
      }
    });

    const dialogRef = this.dialogService.openEditCashierDialog(dialogConfig);
    this.afterClose(dialogRef);
  }

  private openAddDialog(): void {
    let dialogConfig = {
      width: '50%',
      height: 'fit-content'
    };

    this.breakpointObserver.observe(['(max-width: 576px)']).subscribe((result) => {
      if (result.matches) {
        dialogConfig.width = '90%';
      }
    });

    const dialogRef = this.dialogService.openAddCashierDialog(dialogConfig);
    this.afterClose(dialogRef);
  }

  private afterClose(dialogRef: any): void {
    dialogRef.afterClosed().subscribe((result: any) => {
      if (result) this.loadCashiers();
    });
  }
}
