import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { AngularFireAuth } from '@angular/fire/compat/auth';
import { AngularFirestore } from '@angular/fire/compat/firestore';
import { Observable, map, of } from 'rxjs';
import { environment } from 'src/environments/environment';
import { Order, OrderField, OrdersDto } from './types/orders-response.dto';
import { OrdersQueryParams } from './types/orders-request.dto';

@Injectable({
  providedIn: 'root',
})
export class MerchantProService {
  baseUrl = environment.mainMerchantPro.baseUrl;
  otherUrl = 'https://us-central1-fiscal-38558.cloudfunctions.net/api';
  headers = new HttpHeaders();

  orderFields: OrderField[] = [
    { key: 'billing_company_name', friendlyName: 'Naziv firme' },
    { key: 'billing_company_number', friendlyName: '<PERSON><PERSON><PERSON> broj' },
    { key: 'billing_company_vat', friendlyName: 'PIB' },
    { key: 'billing_country_name', friendlyName: 'Država' },
    { key: 'billing_full_address', friendlyName: 'Adresa' },
    { key: 'billing_name', friendlyName: 'Ime' },
    { key: 'billing_postal_code', friendlyName: 'Poštanski broj' },
    { key: 'billing_state', friendlyName: 'Država/Region' },
    { key: 'currency', friendlyName: 'Valuta' },
    { key: 'customer_email', friendlyName: 'Email' },
    { key: 'payment_method_name', friendlyName: 'Način plaćanja' },
    { key: 'payment_status', friendlyName: 'Status plaćanja' },
    { key: 'payment_status_text', friendlyName: 'Opis statusa plaćanja' },
    { key: 'payment_url', friendlyName: 'URL za plaćanje' },
  ];

  constructor(
    private auth: AngularFireAuth,
    private firestore: AngularFirestore,
    private http: HttpClient
  ) {
    auth.onAuthStateChanged(async (user) => {
      this.headers.append('Authorization', `Bearer ${user?.getIdToken()}`);
    });
  }

  getOrders(queryParams: OrdersQueryParams): Observable<OrdersDto> {
    return this.http.get<OrdersDto>(`${this.otherUrl}/merchant-pro/orders`, {
      params: { ...queryParams },
      headers: this.headers,
    });
  }

  getFilterOrderFields(order: Order) {
    return this.orderFields.filter((field) => order[field.key]);
  }

  getOrdersTesting(queryParams: OrdersQueryParams): Observable<any> {
    const ordersData: any = {
      orders: [
        {
          sys_id: *********,
          id: '********',
          payment_status: 'paid',
          payment_status_text: 'Plaćanje je uspešno',
          payment_method_code: 'wire',
          payment_method_name: 'Plaćanje preko računa',
          payment_details: {
            bank_accounts: [
              {
                bank_iban: '265-*************-29',
                bank_name: 'Raiffeisen Banka A.D.',
                bank_address: 'Bulevar Zorana Đinđića 64A, Novi Beograd',
              },
            ],
            company_name: 'SVE ZA POD d.o.o.',
            company_number: '*********',
          },
          shipping_status: 'in_process',
          shipping_status_text: 'Procesiranje porudžbine',
          shipping_method_id: 2,
          shipping_method_name: 'Direktna dostava kurirskim službama',
          fulfillment_info: null,
          parcels: null,
          shipping_amount: 1300,
          shipping_tax_amount: 216.67,
          shipping_cod_amount: 0,
          subtotal_including_tax: 12000,
          subtotal_excluding_tax: 10000,
          tax_amount: 2216.67,
          subtotal_amount: 13300,
          total_amount: 13300,
          paid_amount: 13300,
          currency: 'RSD',
          customer_id: 2986,
          customer_email: '<EMAIL>',
          customer_ip_address: null,
          customer_ip_country: null,
          customer_device: null,
          customer_lang: 'sr_RS',
          customer_note: null,
          customer_notification: null,
          date_created: '2024-05-30T12:44:58+03:00',
          date_modified: '2024-05-30T18:45:01+03:00',
          payment_date: '2024-05-30 18:45:01',
          created_by: 'user',
          traffic_source: null,
          source_referrer: null,
          source_type: 'user',
          meta_fields: null,
          tags: null,
          billing_type: 'company',
          billing_name: 'MAISON ROYAL KOSANČIĆ DOO',
          billing_company_name: 'MAISON ROYAL KOSANČIĆ DOO',
          billing_company_number: '21907413',
          billing_company_vat: '*********',
          billing_country_code: 'RS',
          billing_country_name: 'Србија',
          billing_state: null,
          billing_city: 'Beograd',
          billing_address: 'Braće Nedića 22',
          billing_postal_code: '11000',
          billing_full_address: 'Braće Nedića 22, 11000, Beograd, RS',
          billing_phone: '0601328344',
          shipping_as_billing: false,
          shipping_name: 'Dragan Obradović',
          shipping_country_code: 'RS',
          shipping_country_name: 'Србија',
          shipping_state: null,
          shipping_city: 'Beograd',
          shipping_address: 'Vajara Đoke Jovanovića 15A',
          shipping_postal_code: '11000',
          shipping_phone: '*********',
          shipping_full_address:
            'Vajara Đoke Jovanovića 15A, 11000, Beograd, RS',
          shipping_address_id: ********,
          bank_name: null,
          bank_account: null,
          billing_company_bank_name: null,
          billing_company_bank_account: null,
          billing_address_id: ********,
          line_items: [
            {
              item_type: 'product',
              product_id: 67,
              product_sku: 'PK00063',
              product_ean: null,
              product_ext_ref: 'Ugaoni odbojnik / Kosi (6001)',
              product_erp_id: null,
              product_name: 'Ugaoni odbojnik',
              product_url: 'https://www.svezapod.rs/kupi?id=67',
              product_image_url:
                'https://c.cdnmp.net/*********/p/t/9/ugaoni-odbojnik~149.jpg',
              product_tax_name: 'PDV',
              product_tax_percent: 20,
              category_id: 19,
              category_name: 'SAOBRAĆAJNI PROGRAM',
              manufacturer_id: 1000028,
              manufacturer_name: 'MFK PLASTIK',
              product_availability_id: 4,
              quantity: 8,
              unit_price_net: 1250,
              unit_tax_amount: 250,
              unit_price_gross: 1500,
              line_subtotal_net: 10000,
              line_tax_amount: 2000,
              line_subtotal_gross: 12000,
              unit_id: 5,
              unit_type: 'kom',
              weight: 1.8,
              name: 'Ugaoni odbojnik',
              unitPrice: 1500,
              unitPriceGrossWithDiscount: 1500,
              totalAmountWithDiscount: 12000,
              totalAmount: 12000,
            },
          ],
          info_url:
            'https://www.svezapod.rs/naruci/info/1e5c8aefd748f965a60b6f2ede2b36a3/********',
          proforma_url:
            'https://www.svezapod.rs/naruci/proforma/1e5c8aefd748f965a60b6f2ede2b36a3/********',
          payment_url: null,
        },
        {
          sys_id: *********,
          id: '********',
          payment_status: 'awaiting',
          payment_status_text: 'Plaćanje na čekanju',
          payment_method_code: 'cash_delivery',
          payment_method_name: 'Plaćanje pouzećem',
          payment_details: [],
          shipping_status: 'in_process',
          shipping_status_text: 'Procesiranje porudžbine',
          shipping_method_id: 2,
          shipping_method_name: 'Direktna dostava kurirskim službama',
          fulfillment_info: null,
          parcels: null,
          shipping_amount: 1500,
          shipping_tax_amount: 250,
          shipping_cod_amount: 22280,
          subtotal_including_tax: 20780,
          subtotal_excluding_tax: 17316.67,
          tax_amount: 3713.33,
          subtotal_amount: 22280,
          total_amount: 22280,
          paid_amount: null,
          currency: 'RSD',
          customer_id: 3364,
          customer_email: '<EMAIL>',
          customer_ip_address: null,
          customer_ip_country: null,
          customer_device: null,
          customer_lang: 'sr_RS',
          customer_note: null,
          customer_notification: null,
          date_created: '2024-05-30T11:29:22+03:00',
          date_modified: '2024-05-30T11:29:22+03:00',
          payment_date: null,
          created_by: 'user',
          traffic_source: null,
          source_referrer: null,
          source_type: 'user',
          meta_fields: null,
          tags: null,
          billing_type: 'individual',
          billing_name: 'Dragoslav Žikić',
          billing_company_name: null,
          billing_company_number: null,
          billing_company_vat: null,
          billing_country_code: 'RS',
          billing_country_name: 'Србија',
          billing_state: null,
          billing_city: 'Zaječar',
          billing_address: 'Dimitrija Popovića Mitketa 3',
          billing_postal_code: '19000',
          billing_full_address:
            'Dimitrija Popovića Mitketa 3, 19000, Zaječar, RS',
          billing_phone: '**********',
          shipping_as_billing: true,
          shipping_name: 'Dragoslav Žikić',
          shipping_country_code: 'RS',
          shipping_country_name: 'Србија',
          shipping_state: null,
          shipping_city: 'Zaječar',
          shipping_address: 'Dimitrija Popovića Mitketa 3',
          shipping_postal_code: '19000',
          shipping_phone: '**********',
          shipping_full_address:
            'Dimitrija Popovića Mitketa 3, 19000, Zaječar, RS',
          shipping_address_id: ********,
          bank_name: null,
          bank_account: null,
          billing_company_bank_name: null,
          billing_company_bank_account: null,
          billing_address_id: ********,
          line_items: [
            {
              item_type: 'product',
              product_id: 192,
              product_sku: 'UN00191',
              product_ean: null,
              product_ext_ref: null,
              product_erp_id: null,
              product_name: 'Parking stop barijera-zaobljena',
              product_url: 'https://www.svezapod.rs/kupi?id=192',
              product_image_url:
                'https://c.cdnmp.net/*********/p/t/2/parking-stop-barijera-zaobljena~472.jpg',
              product_tax_name: 'PDV',
              product_tax_percent: 20,
              category_id: 19,
              category_name: 'SAOBRAĆAJNI PROGRAM',
              manufacturer_id: 1000026,
              manufacturer_name: 'UNZ',
              product_availability_id: 4,
              quantity: 2,
              unit_price_net: 3700,
              unit_tax_amount: 740,
              unit_price_gross: 4440,
              line_subtotal_net: 7400,
              line_tax_amount: 1480,
              line_subtotal_gross: 8880,
              weight: 4.9,
              name: 'Parking stop barijera-zaobljena',
              unitPrice: 4440,
              unitPriceGrossWithDiscount: 4440,
              totalAmountWithDiscount: 8880,
              totalAmount: 8880,
            },
            {
              item_type: 'product',
              product_id: 331,
              product_sku: 'MON00329',
              product_ean: null,
              product_ext_ref: null,
              product_erp_id: null,
              product_name:
                'Parking barijera zaobljena SA DALJINSKIM UPRAVLJANJEM',
              product_url: 'https://www.svezapod.rs/kupi?id=331',
              product_image_url:
                'https://c.cdnmp.net/*********/p/t/8/parking-barijera-zaobljena-sa-daljinskim-upravljanjem~1058.jpg',
              product_tax_name: 'PDV',
              product_tax_percent: 20,
              category_id: 19,
              category_name: 'SAOBRAĆAJNI PROGRAM',
              manufacturer_id: 0,
              product_availability_id: 4,
              quantity: 1,
              unit_price_net: 9916.67,
              unit_tax_amount: 1983.33,
              unit_price_gross: 11900,
              line_subtotal_net: 9916.67,
              line_tax_amount: 1983.33,
              line_subtotal_gross: 11900,
              unit_id: 5,
              unit_type: 'kom',
              weight: 7,
              name: 'Parking barijera zaobljena SA DALJINSKIM UPRAVLJANJEM',
              unitPrice: 11900,
              unitPriceGrossWithDiscount: 11900,
              totalAmountWithDiscount: 11900,
              totalAmount: 11900,
            },
          ],
          info_url:
            'https://www.svezapod.rs/naruci/info/6912ce7f34815224864c83feaf3cb9d4/********',
          proforma_url:
            'https://www.svezapod.rs/naruci/proforma/6912ce7f34815224864c83feaf3cb9d4/********',
          payment_url: null,
        },
        {
          sys_id: *********,
          id: '********',
          payment_status: 'paid',
          payment_status_text: 'Plaćanje je uspešno',
          payment_substatus_id: 2,
          payment_substatus_text: 'Fiskalizovan',
          payment_method_code: 'wire',
          payment_method_name: 'Plaćanje preko računa',
          payment_details: {
            bank_accounts: [
              {
                bank_iban: '265-*************-29',
                bank_name: 'Raiffeisen Banka A.D.',
                bank_address: 'Bulevar Zorana Đinđića 64A, Novi Beograd',
              },
            ],
            company_name: 'SVE ZA POD d.o.o.',
            company_number: '*********',
          },
          shipping_status: 'in_process',
          shipping_status_text: 'Procesiranje porudžbine',
          shipping_method_id: 2,
          shipping_method_name: 'Direktna dostava kurirskim službama',
          fulfillment_info: null,
          parcels: null,
          shipping_amount: 980,
          shipping_tax_amount: 163.33,
          shipping_cod_amount: 0,
          subtotal_including_tax: 7200,
          subtotal_excluding_tax: 6000,
          tax_amount: 1363.33,
          subtotal_amount: 8180,
          total_amount: 8180,
          paid_amount: 8180,
          currency: 'RSD',
          customer_id: 3363,
          customer_email: '<EMAIL>',
          customer_ip_address: '***************',
          customer_ip_country: 'rs',
          customer_device: 'desktop',
          customer_lang: 'sr_RS',
          customer_note: null,
          customer_notification: null,
          date_created: '2024-05-30T10:59:57+03:00',
          date_modified: '2024-05-30T13:43:16+03:00',
          payment_date: '2024-05-30 12:52:22',
          created_by: 'shop',
          traffic_source: null,
          source_referrer: 'google.com',
          source_type: 'Search engine (google.com)',
          meta_fields: null,
          tags: null,
          billing_type: 'company',
          billing_name: 'Facility point d.o.o.',
          billing_company_name: 'Facility point d.o.o.',
          billing_company_number: '21525588',
          billing_company_vat: '*********',
          billing_country_code: 'RS',
          billing_country_name: 'Србија',
          billing_state: null,
          billing_city: 'Beograd (Čukarica), Grad Beograd',
          billing_address: 'Vladimira Rolovića 168',
          billing_postal_code: '11030',
          billing_full_address:
            'Vladimira Rolovića 168, 11030, Beograd (Čukarica), Grad Beograd, RS',
          billing_phone: '**********',
          shipping_as_billing: false,
          shipping_name: 'MAJA ZAGVAREVIĆ',
          shipping_country_code: 'RS',
          shipping_country_name: 'Србија',
          shipping_state: null,
          shipping_city: 'Beograd (Savski Venac), Grad Beograd',
          shipping_address: 'Hercegovačka 19, zgrada Vista, Beograd na vodi',
          shipping_postal_code: '11000',
          shipping_phone: '**********',
          shipping_full_address:
            'Hercegovačka 19, zgrada Vista, Beograd na vodi, 11000, Beograd (Savski Venac), Grad Beograd, RS',
          shipping_address_id: ********,
          bank_name: null,
          bank_account: null,
          billing_company_bank_name: null,
          billing_company_bank_account: null,
          billing_address_id: ********,
          line_items: [
            {
              item_type: 'product',
              product_id: 234,
              product_sku: 'UN00234',
              product_ean: null,
              product_ext_ref: '20 16776',
              product_erp_id: null,
              product_name: 'Jednodelni putarski čunj PVC 52 cm',
              product_url: 'https://www.svezapod.rs/kupi?id=234',
              product_image_url:
                'https://c.cdnmp.net/*********/p/t/4/jednodelni-putarski-cunj-pvc-52-cm~654.jpg',
              product_tax_name: 'PDV',
              product_tax_percent: 20,
              category_id: 19,
              category_name: 'SAOBRAĆAJNI PROGRAM',
              manufacturer_id: 1000028,
              manufacturer_name: 'MFK PLASTIK',
              product_availability_id: 4,
              quantity: 6,
              unit_price_net: 1000,
              unit_tax_amount: 200,
              unit_price_gross: 1200,
              line_subtotal_net: 6000,
              line_tax_amount: 1200,
              line_subtotal_gross: 7200,
              unit_id: 5,
              unit_type: 'kom',
              weight: 1.25,
              name: 'Jednodelni putarski čunj PVC 52 cm',
              unitPrice: 1200,
              unitPriceGrossWithDiscount: 1200,
              totalAmountWithDiscount: 7200,
              totalAmount: 7200,
            },
          ],
          info_url:
            'https://www.svezapod.rs/naruci/info/edadcf3b041d8f1f4c7518c758e3dda7/********',
          proforma_url:
            'https://www.svezapod.rs/naruci/proforma/edadcf3b041d8f1f4c7518c758e3dda7/********',
          payment_url: null,
          verificationURL:
            'https://suf.purs.gov.rs/v/?vl=A0VXQjJWWVJYUjdXUzVNTzAMAgAA9AEAAEAr4AQAAAAAAAABj8kYugAAAAwxMDoxMTE3MDQ2NDQztrlAemDsvYm8T3Ui%2FrxgYE8%2FRe2xlbaudx7rWWzJxwGexUoU3sWvztrFTjefrClEbM8FU9ucI6lcsCurCCRyLBQJuB8ZFnq3ORzYLBxIVTt3nufhWreWzCATkGG75GqqnFJPhEinAapY0X55rUn%2BwRnNy45sgoIl8EpaH%2BOhK0v%2FFZOlb9Pn78z00igsW%2FKEPDCI1U058mcAZQkDgTDZTZRnwJ5EccVVa%2Bq%2F%2BlIMv4k0ilTaxEU6GMKWzD3ZTDgNw7atDaApYL68ORcWcB0RSaSKgNPMa%2FizKV9vPgeZsGNvaN4zDzHTRP4t9MKD6Tu0HMFA1yXrB8lMprwSumkhljnZWlHqfewQTkls5%2F9y%2BIoYJomyn%2FsRdKWE99r%2FJbURJAgSsv%2Bb90YStRaIiryHejYsiUIM76vIplzEDRE5Q5V6jrw0UxRNGpeh5xG4LKD1pgUIQZvCiUfM3zZ3Rgk52zE%2BdPWiaI7o4nr76nQqyCnS8dIu5YgjVTpaNt2xVkqQshVG%2Bhz6S3VoMEP%2F2FaJh0jTROXF7M%2FgagjF1W24z4cKT6TOSNYPCjfkluVrcKBleFwNUtXx9KxdFBNrXbRPFdHn5pHv87zg39iLVGAcXUj7L0ODYMW9wEiriiMgoXew%2F2PKrvNiotSm0A1gv7UXD0cJNXzZXcOYhN5fWVIQstzQPY1nErai77vOdATHRv8%3D',
          createdAt: '2024-05-30T12:43:16.000000Z',
          invoicePdfUrl:
            'https://storage.fiscomm.rs/storage/NhEarGVgTlRKp7zTMfOdLvbte5J3/invoices/2024/05/30/EWB2VYRX-R7WS5MO0-52431jrbmd0h.pdf',
        },
        {
          sys_id: *********,
          id: '37685577',
          payment_status: 'awaiting',
          payment_status_text: 'Plaćanje na čekanju',
          payment_method_code: 'cash_delivery',
          payment_method_name: 'Plaćanje pouzećem',
          payment_details: [],
          shipping_status: 'shipped',
          shipping_status_text: 'Porudžbina se isporučuje',
          shipping_method_id: 2,
          shipping_method_name: 'Direktna dostava kurirskim službama',
          fulfillment_info: null,
          parcels: null,
          shipping_amount: 580,
          shipping_tax_amount: 96.67,
          shipping_cod_amount: 1680,
          subtotal_including_tax: 1100,
          subtotal_excluding_tax: 916.67,
          tax_amount: 280,
          subtotal_amount: 1680,
          total_amount: 1680,
          paid_amount: null,
          currency: 'RSD',
          customer_id: 3361,
          customer_email: '<EMAIL>',
          customer_ip_address: '*************',
          customer_ip_country: 'rs',
          customer_device: 'mobile',
          customer_lang: 'sr_RS',
          customer_note: null,
          customer_notification:
            'Pošiljka br.KO0067838633-Dexpress\nLink za praćenje pošiljke: https://bexexpress.rs/pracenje-posiljke',
          date_created: '2024-05-29T21:25:36+03:00',
          date_modified: '2024-05-30T13:13:42+03:00',
          payment_date: null,
          date_shipped: '2024-05-30T13:13:42+03:00',
          created_by: 'shop',
          traffic_source: null,
          source_referrer: 'google.com',
          source_type: 'Search engine (google.com)',
          meta_fields: null,
          tags: null,
          billing_type: 'individual',
          billing_name: 'Mihajlo Životić',
          billing_company_name: null,
          billing_company_number: null,
          billing_company_vat: null,
          billing_country_code: 'RS',
          billing_country_name: 'Србија',
          billing_state: 'Braničevski',
          billing_city: 'Pozarevac',
          billing_address: 'Vlastimira Carevca, 15',
          billing_postal_code: '12000',
          billing_full_address:
            'Vlastimira Carevca, 15, 12000, Pozarevac, Braničevski, RS',
          billing_phone: '**********',
          shipping_as_billing: true,
          shipping_name: 'Mihajlo Životić',
          shipping_country_code: 'RS',
          shipping_country_name: 'Србија',
          shipping_state: 'Braničevski',
          shipping_city: 'Pozarevac',
          shipping_address: 'Vlastimira Carevca, 15',
          shipping_postal_code: '12000',
          shipping_phone: '**********',
          shipping_full_address:
            'Vlastimira Carevca, 15, 12000, Pozarevac, Braničevski, RS',
          shipping_address_id: ********,
          bank_name: null,
          bank_account: null,
          billing_company_bank_name: null,
          billing_company_bank_account: null,
          billing_address_id: ********,
          line_items: [
            {
              item_type: 'product',
              product_id: 334,
              product_sku: 'EL00332',
              product_ean: null,
              product_ext_ref: 'Dobavljač: ELAN Šifra: 140540',
              product_erp_id: null,
              product_name: 'Lepak za gumu ELAPREN 1150-limenka 1L',
              product_url: 'https://www.svezapod.rs/kupi?id=334',
              product_image_url:
                'https://c.cdnmp.net/*********/p/t/8/lepak-za-gumu-elapren-1150-limenka-1l~1068.jpg',
              product_tax_name: 'PDV',
              product_tax_percent: 20,
              category_id: 6,
              category_name: 'LEPLJENJE PODNIH OBLOGA',
              manufacturer_id: ********,
              manufacturer_name: 'ELAN',
              product_availability_id: 4,
              quantity: 1,
              unit_price_net: 916.67,
              unit_tax_amount: 183.*********00004,
              unit_price_gross: 1100,
              line_subtotal_net: 916.67,
              line_tax_amount: 183.33,
              line_subtotal_gross: 1100,
              unit_id: 3,
              unit_type: 'pakovanje',
              weight: 1,
              name: 'Lepak za gumu ELAPREN 1150-limenka 1L',
              unitPrice: 1100,
              unitPriceGrossWithDiscount: 1100,
              totalAmountWithDiscount: 1100,
              totalAmount: 1100,
            },
          ],
          info_url:
            'https://www.svezapod.rs/naruci/info/964cc9ce572cad144e630f3f7cc849ad/37685577',
          proforma_url:
            'https://www.svezapod.rs/naruci/proforma/964cc9ce572cad144e630f3f7cc849ad/37685577',
          payment_url: null,
        },
        {
          sys_id: *********,
          id: '26195725',
          payment_status: 'awaiting',
          payment_status_text: 'Plaćanje na čekanju',
          payment_method_code: 'cash_delivery',
          payment_method_name: 'Plaćanje pouzećem',
          payment_details: [],
          shipping_status: 'in_process',
          shipping_status_text: 'Procesiranje porudžbine',
          shipping_method_id: 2,
          shipping_method_name: 'Direktna dostava kurirskim službama',
          fulfillment_info: null,
          parcels: null,
          shipping_amount: 780,
          shipping_tax_amount: 130,
          shipping_cod_amount: 4680,
          subtotal_including_tax: 3900,
          subtotal_excluding_tax: 3250,
          tax_amount: 780,
          subtotal_amount: 4680,
          total_amount: 4680,
          paid_amount: null,
          currency: 'RSD',
          customer_id: 3360,
          customer_email: '<EMAIL>',
          customer_ip_address: '************',
          customer_ip_country: 'rs',
          customer_device: 'mobile',
          customer_lang: 'sr_RS',
          customer_note: null,
          customer_notification: null,
          date_created: '2024-05-29T20:33:26+03:00',
          date_modified: '2024-05-30T11:49:05+03:00',
          payment_date: null,
          created_by: 'shop',
          traffic_source: null,
          source_referrer: null,
          source_type: 'Direct',
          meta_fields: null,
          tags: null,
          billing_type: 'individual',
          billing_name: 'Vladimir Purkovic',
          billing_company_name: null,
          billing_company_number: null,
          billing_company_vat: null,
          billing_country_code: 'RS',
          billing_country_name: 'Србија',
          billing_state: 'Grad Beograd',
          billing_city: 'Surcin',
          billing_address: '23 Majora Tepica',
          billing_postal_code: '11271',
          billing_full_address:
            '23 Majora Tepica, 11271, Surcin, Grad Beograd, RS',
          billing_phone: '**********',
          shipping_as_billing: true,
          shipping_name: 'Vladimir Purkovic',
          shipping_country_code: 'RS',
          shipping_country_name: 'Србија',
          shipping_state: 'Grad Beograd',
          shipping_city: 'Surcin',
          shipping_address: '23 Majora Tepica',
          shipping_postal_code: '11271',
          shipping_phone: '**********',
          shipping_full_address:
            '23 Majora Tepica, 11271, Surcin, Grad Beograd, RS',
          shipping_address_id: ********,
          bank_name: null,
          bank_account: null,
          billing_company_bank_name: null,
          billing_company_bank_account: null,
          billing_address_id: ********,
          line_items: [
            {
              item_type: 'product',
              product_id: 219,
              product_sku: 'UN00219',
              product_ean: null,
              product_ext_ref: '20 18864',
              product_erp_id: null,
              product_name: 'Znak upozorenja - Pažnja! Klizav pod',
              product_url: 'https://www.svezapod.rs/kupi?id=219',
              product_image_url:
                'https://c.cdnmp.net/*********/p/t/2/znak-upozorenja-paznja-klizav-pod~922.jpg',
              product_tax_name: 'PDV',
              product_tax_percent: 20,
              category_id: 29,
              category_name: 'Podna obeležja',
              manufacturer_id: 1000028,
              manufacturer_name: 'MFK PLASTIK',
              product_availability_id: 4,
              quantity: 3,
              unit_price_net: 1083.3333,
              unit_tax_amount: 216.6667,
              unit_price_gross: 1300,
              line_subtotal_net: 3250,
              line_tax_amount: 650,
              line_subtotal_gross: 3900,
              unit_id: 5,
              unit_type: 'kom',
              weight: 0.8,
              name: 'Znak upozorenja - Pažnja! Klizav pod',
              unitPrice: 1300,
              unitPriceGrossWithDiscount: 1300,
              totalAmountWithDiscount: 3900,
              totalAmount: 3900,
            },
          ],
          info_url:
            'https://www.svezapod.rs/naruci/info/8d7aea2c1a468f90ab3661211a85b27d/26195725',
          proforma_url:
            'https://www.svezapod.rs/naruci/proforma/8d7aea2c1a468f90ab3661211a85b27d/26195725',
          payment_url: null,
        },
        {
          sys_id: *********,
          id: '********',
          payment_status: 'cancelled',
          payment_status_text: 'Plaćanje je otkazano',
          payment_method_code: 'cash_delivery',
          payment_method_name: 'Plaćanje pouzećem',
          payment_details: [],
          shipping_status: 'cancelled',
          shipping_status_text: 'Porudžbina otkazana',
          shipping_method_id: 2,
          shipping_method_name: 'Direktna dostava kurirskim službama',
          fulfillment_info: null,
          parcels: null,
          shipping_amount: 450,
          shipping_tax_amount: 75,
          shipping_cod_amount: 1750,
          subtotal_including_tax: 1300,
          subtotal_excluding_tax: 1083.33,
          tax_amount: 291.66999999999996,
          subtotal_amount: 1750,
          total_amount: 1750,
          currency: 'RSD',
          customer_id: 3360,
          customer_email: '<EMAIL>',
          customer_ip_address: '************',
          customer_ip_country: 'rs',
          customer_device: 'mobile',
          customer_lang: 'sr_RS',
          customer_note: null,
          customer_notification: '',
          date_created: '2024-05-29T20:32:23+03:00',
          date_modified: '2024-05-30T11:48:57+03:00',
          payment_date: null,
          created_by: 'shop',
          traffic_source: null,
          source_referrer: null,
          source_type: 'Direct',
          meta_fields: null,
          tags: null,
          billing_type: 'individual',
          billing_name: 'Vladimir Purkovic',
          billing_company_name: null,
          billing_company_number: null,
          billing_company_vat: null,
          billing_country_code: 'RS',
          billing_country_name: 'Србија',
          billing_state: 'Grad Beograd',
          billing_city: 'Surcin',
          billing_address: '23 Majora Tepica',
          billing_postal_code: '11271',
          billing_full_address:
            '23 Majora Tepica, 11271, Surcin, Grad Beograd, RS',
          billing_phone: '**********',
          shipping_as_billing: true,
          shipping_name: 'Vladimir Purkovic',
          shipping_country_code: 'RS',
          shipping_country_name: 'Србија',
          shipping_state: 'Grad Beograd',
          shipping_city: 'Surcin',
          shipping_address: '23 Majora Tepica',
          shipping_postal_code: '11271',
          shipping_phone: '**********',
          shipping_full_address:
            '23 Majora Tepica, 11271, Surcin, Grad Beograd, RS',
          shipping_address_id: ********,
          bank_name: null,
          bank_account: null,
          billing_company_bank_name: null,
          billing_company_bank_account: null,
          billing_address_id: ********,
          line_items: [
            {
              item_type: 'product',
              product_id: 219,
              product_sku: 'UN00219',
              product_ean: null,
              product_ext_ref: '20 18864',
              product_erp_id: null,
              product_name: 'Znak upozorenja - Pažnja! Klizav pod',
              product_url: 'https://www.svezapod.rs/kupi?id=219',
              product_image_url:
                'https://c.cdnmp.net/*********/p/t/2/znak-upozorenja-paznja-klizav-pod~922.jpg',
              product_tax_name: 'PDV',
              product_tax_percent: 20,
              category_id: 29,
              category_name: 'Podna obeležja',
              manufacturer_id: 1000028,
              manufacturer_name: 'MFK PLASTIK',
              product_availability_id: 4,
              quantity: 1,
              unit_price_net: 1083.33,
              unit_tax_amount: 216.**************,
              unit_price_gross: 1300,
              line_subtotal_net: 1083.33,
              line_tax_amount: 216.67,
              line_subtotal_gross: 1300,
              unit_id: 5,
              unit_type: 'kom',
              weight: 0.8,
              name: 'Znak upozorenja - Pažnja! Klizav pod',
              unitPrice: 1300,
              unitPriceGrossWithDiscount: 1300,
              totalAmountWithDiscount: 1300,
              totalAmount: 1300,
            },
          ],
          info_url:
            'https://www.svezapod.rs/naruci/info/9f37fbad7d41b87521df21ca400140fd/********',
          proforma_url:
            'https://www.svezapod.rs/naruci/proforma/9f37fbad7d41b87521df21ca400140fd/********',
          payment_url: null,
        },
        {
          sys_id: *********,
          id: '********',
          payment_status: 'awaiting',
          payment_status_text: 'Plaćanje na čekanju',
          payment_method_code: 'wire',
          payment_method_name: 'Plaćanje preko računa',
          payment_details: {
            bank_accounts: [
              {
                bank_iban: '265-*************-29',
                bank_name: 'Raiffeisen Banka A.D.',
                bank_address: 'Bulevar Zorana Đinđića 64A, Novi Beograd',
              },
            ],
            company_name: 'SVE ZA POD d.o.o.',
            company_number: '*********',
          },
          shipping_status: 'awaiting',
          shipping_status_text: 'Porudžbina na čekanju',
          shipping_method_id: 2,
          shipping_method_name: 'Direktna dostava kurirskim službama',
          fulfillment_info: null,
          parcels: null,
          shipping_amount: 580,
          shipping_tax_amount: 96.67,
          shipping_cod_amount: 0,
          subtotal_including_tax: 5600,
          subtotal_excluding_tax: 4666.67,
          tax_amount: 1030,
          subtotal_amount: 6180,
          total_amount: 6180,
          paid_amount: null,
          currency: 'RSD',
          customer_id: 3359,
          customer_email: '<EMAIL>',
          customer_ip_address: null,
          customer_ip_country: null,
          customer_device: null,
          customer_lang: 'sr_RS',
          customer_note: null,
          customer_notification: null,
          date_created: '2024-05-29T18:12:40+03:00',
          date_modified: '2024-05-29T18:12:40+03:00',
          payment_date: null,
          created_by: 'user',
          traffic_source: null,
          source_referrer: null,
          source_type: 'user',
          meta_fields: null,
          tags: null,
          billing_type: 'company',
          billing_name: 'UNIVERZITET U BEOGRADU - BIOLOŠKI FAKULTET',
          billing_company_name: 'UNIVERZITET U BEOGRADU - BIOLOŠKI FAKULTET',
          billing_company_number: '07048599',
          billing_company_vat: '*********',
          billing_country_code: 'RS',
          billing_country_name: 'Србија',
          billing_state: null,
          billing_city: 'Beograd',
          billing_address: 'Studentski trg 16',
          billing_postal_code: '11158',
          billing_full_address: 'Studentski trg 16, 11158, Beograd, RS',
          billing_phone: '**********',
          shipping_as_billing: true,
          shipping_name: 'Miloš Ćućko',
          shipping_country_code: 'RS',
          shipping_country_name: 'Србија',
          shipping_state: null,
          shipping_city: 'Beograd',
          shipping_address: 'Studentski trg 16',
          shipping_postal_code: '11158',
          shipping_phone: '**********',
          shipping_full_address: 'Studentski trg 16, 11158, Beograd, RS',
          shipping_address_id: ********,
          bank_name: null,
          bank_account: null,
          billing_company_bank_name: null,
          billing_company_bank_account: null,
          billing_address_id: ********,
          line_items: [
            {
              item_type: 'product',
              product_id: 45,
              variant_name: 'Boja: Plava',
              variant_options: [
                {
                  id: 1,
                  name: 'Boja',
                  value: 'Plava',
                },
              ],
              product_sku: 'PK00037',
              product_ean: null,
              product_ext_ref: null,
              product_erp_id: null,
              product_name: 'Antibakterijski višeslojni otirač 45cm x 90cm',
              product_details: 'Boja: Plava',
              product_url: 'https://www.svezapod.rs/kupi?id=45',
              product_image_url:
                'https://c.cdnmp.net/*********/p/t/3/antibakterijski-viseslojni-otirac-45cm-x-90cm~923.jpg',
              product_tax_name: 'PDV',
              product_tax_percent: 20,
              category_id: 37,
              category_name: 'OTIRAČI',
              manufacturer_id: 1000009,
              manufacturer_name: 'Notrax',
              product_availability_id: 4,
              quantity: 2,
              unit_price_net: 2333.335,
              unit_tax_amount: 466.66499999999996,
              unit_price_gross: 2800,
              line_subtotal_net: 4666.67,
              line_tax_amount: 933.33,
              line_subtotal_gross: 5600,
              unit_id: 5,
              unit_type: 'kom',
              weight: 0.5,
              name: 'Antibakterijski višeslojni otirač 45cm x 90cm',
              unitPrice: 2800,
              unitPriceGrossWithDiscount: 2800,
              totalAmountWithDiscount: 5600,
              totalAmount: 5600,
            },
          ],
          info_url:
            'https://www.svezapod.rs/naruci/info/8c1cc47b91ec881a8fbc337ea9bf2b6c/********',
          proforma_url:
            'https://www.svezapod.rs/naruci/proforma/8c1cc47b91ec881a8fbc337ea9bf2b6c/********',
          payment_url: null,
        },
        {
          sys_id: *********,
          id: '********',
          payment_status: 'awaiting',
          payment_status_text: 'Plaćanje na čekanju',
          payment_method_code: 'cash_delivery',
          payment_method_name: 'Plaćanje pouzećem',
          payment_details: [],
          shipping_status: 'awaiting',
          shipping_status_text: 'Porudžbina na čekanju',
          shipping_method_id: 2,
          shipping_method_name: 'Direktna dostava kurirskim službama',
          fulfillment_info: null,
          parcels: null,
          shipping_amount: 580,
          shipping_tax_amount: 96.67,
          shipping_cod_amount: 1840,
          subtotal_including_tax: 1260,
          subtotal_excluding_tax: 1050,
          tax_amount: 306.67,
          subtotal_amount: 1840,
          total_amount: 1840,
          paid_amount: null,
          currency: 'RSD',
          customer_id: 3358,
          customer_email: '<EMAIL>',
          customer_ip_address: '***************',
          customer_ip_country: 'rs',
          customer_device: 'mobile',
          customer_lang: 'sr_RS',
          customer_note: null,
          customer_notification: null,
          date_created: '2024-05-29T15:44:51+03:00',
          date_modified: '2024-05-29T15:44:51+03:00',
          payment_date: null,
          created_by: 'shop',
          traffic_source: null,
          source_referrer: null,
          source_type: 'Direct',
          meta_fields: null,
          tags: null,
          billing_type: 'individual',
          billing_name: 'Jelena Pribanovic',
          billing_company_name: null,
          billing_company_number: null,
          billing_company_vat: null,
          billing_country_code: 'RS',
          billing_country_name: 'Србија',
          billing_state: null,
          billing_city: 'Aleksandrovac',
          billing_address: '37230',
          billing_postal_code: '37230',
          billing_full_address: '37230, 37230, Aleksandrovac, RS',
          billing_phone: '**********',
          shipping_as_billing: true,
          shipping_name: 'Jelena Pribanovic',
          shipping_country_code: 'RS',
          shipping_country_name: 'Србија',
          shipping_state: null,
          shipping_city: 'Aleksandrovac',
          shipping_address: '37230',
          shipping_postal_code: '37230',
          shipping_phone: '**********',
          shipping_full_address: '37230, 37230, Aleksandrovac, RS',
          shipping_address_id: ********,
          bank_name: null,
          bank_account: null,
          billing_company_bank_name: null,
          billing_company_bank_account: null,
          billing_address_id: ********,
          line_items: [
            {
              item_type: 'product',
              product_id: 394,
              product_sku: 'WEP00372',
              product_ean: null,
              product_ext_ref: '*********',
              product_erp_id: null,
              product_name:
                'WEPOS Sredstvo za čišćenje granitnih pločica osnovni - bez kiseline 1L',
              product_url: 'https://www.svezapod.rs/kupi?id=394',
              product_image_url:
                'https://c.cdnmp.net/*********/p/t/0/wepos-sredstvo-za-ciscenje-granitnih-plocica-osnovni-bez-kiseline-1l~1200.jpg',
              product_tax_name: 'PDV',
              product_tax_percent: 20,
              category_id: 26,
              category_name: 'ČIŠĆENJE PODOVA',
              manufacturer_id: ********,
              manufacturer_name: 'WEPOS',
              product_availability_id: 4,
              quantity: 1,
              unit_price_net: 1050,
              unit_tax_amount: 210,
              unit_price_gross: 1260,
              line_subtotal_net: 1050,
              line_tax_amount: 210,
              line_subtotal_gross: 1260,
              unit_id: 5,
              unit_type: 'kom',
              weight: 1,
              name: 'WEPOS Sredstvo za čišćenje granitnih pločica osnovni - bez kiseline 1L',
              unitPrice: 1260,
              unitPriceGrossWithDiscount: 1260,
              totalAmountWithDiscount: 1260,
              totalAmount: 1260,
            },
          ],
          info_url:
            'https://www.svezapod.rs/naruci/info/423343ad316d481fa1976153057feddf/********',
          proforma_url:
            'https://www.svezapod.rs/naruci/proforma/423343ad316d481fa1976153057feddf/********',
          payment_url: null,
        },
        {
          sys_id: *********,
          id: '********',
          payment_status: 'paid',
          payment_status_text: 'Plaćanje je uspešno',
          payment_substatus_id: 2,
          payment_substatus_text: 'Fiskalizovan',
          payment_method_code: 'wire',
          payment_method_name: 'Plaćanje preko računa',
          payment_details: {
            bank_accounts: [
              {
                bank_iban: '265-*************-29',
                bank_name: 'Raiffeisen Banka A.D.',
                bank_address: 'Bulevar Zorana Đinđića 64A, Novi Beograd',
              },
            ],
            company_name: 'SVE ZA POD d.o.o.',
            company_number: '*********',
          },
          shipping_status: 'shipped',
          shipping_status_text: 'Porudžbina se isporučuje',
          shipping_method_id: 2,
          shipping_method_name: 'Direktna dostava kurirskim službama',
          fulfillment_info: null,
          parcels: null,
          shipping_amount: 980,
          shipping_tax_amount: 163.33,
          shipping_cod_amount: 0,
          subtotal_including_tax: 3100,
          subtotal_excluding_tax: 2583.33,
          tax_amount: 680,
          subtotal_amount: 4080,
          total_amount: 4080,
          paid_amount: 4080,
          currency: 'RSD',
          customer_id: 3357,
          customer_email: '<EMAIL>',
          customer_ip_address: null,
          customer_ip_country: null,
          customer_device: null,
          customer_lang: 'sr_RS',
          customer_note: null,
          customer_notification:
            'Pošiljka br.*********-Bex\nLink za praćenje pošiljke:https://bexexpress.rs/pracenje-posiljke',
          date_created: '2024-05-29T11:49:20+03:00',
          date_modified: '2024-05-30T11:43:41+03:00',
          payment_date: '2024-05-29 13:12:10',
          date_shipped: '2024-05-30T11:43:41+03:00',
          created_by: 'user',
          traffic_source: null,
          source_referrer: null,
          source_type: 'user',
          meta_fields: null,
          tags: null,
          billing_type: 'company',
          billing_name: 'FRUITICA d.o.o. Čantavir',
          billing_company_name: 'FRUITICA d.o.o. Čantavir',
          billing_company_number: '08633266',
          billing_company_vat: '*********',
          billing_country_code: 'RS',
          billing_country_name: 'Србија',
          billing_state: null,
          billing_city: 'Čantavir',
          billing_address: 'Trg republike 7',
          billing_postal_code: '24220',
          billing_full_address: 'Trg republike 7, 24220, Čantavir, RS',
          billing_phone: '*********',
          shipping_as_billing: false,
          shipping_name: 'Roland Feher',
          shipping_country_code: 'RS',
          shipping_country_name: 'Србија',
          shipping_state: null,
          shipping_city: 'Čantavir',
          shipping_address: 'Bajmočka 20',
          shipping_postal_code: '24220',
          shipping_phone: '*********',
          shipping_full_address: 'Bajmočka 20, 24220, Čantavir, RS',
          shipping_address_id: ********,
          bank_name: null,
          bank_account: null,
          billing_company_bank_name: null,
          billing_company_bank_account: null,
          billing_address_id: ********,
          line_items: [
            {
              item_type: 'product',
              product_id: 126,
              product_sku: 'ECO00124',
              product_ean: null,
              product_ext_ref: null,
              product_erp_id: null,
              product_name: 'Dezinfekciono sredstvo Sutter Cuat 88 Food',
              product_url: 'https://www.svezapod.rs/kupi?id=126',
              product_image_url:
                'https://c.cdnmp.net/*********/p/t/4/dezinfekciono-sredstvo-sutter-cuat-88-food~264.jpg',
              product_tax_name: 'PDV',
              product_tax_percent: 20,
              category_id: 26,
              category_name: 'ČIŠĆENJE PODOVA',
              manufacturer_id: 1000016,
              manufacturer_name: 'Sutter',
              product_availability_id: 4,
              quantity: 1,
              unit_price_net: 2583.33,
              unit_tax_amount: 516.*************,
              unit_price_gross: 3100,
              line_subtotal_net: 2583.33,
              line_tax_amount: 516.67,
              line_subtotal_gross: 3100,
              unit_id: 3,
              unit_type: 'pakovanje',
              weight: 5,
              name: 'Dezinfekciono sredstvo Sutter Cuat 88 Food',
              unitPrice: 3100,
              unitPriceGrossWithDiscount: 3100,
              totalAmountWithDiscount: 3100,
              totalAmount: 3100,
            },
          ],
          info_url:
            'https://www.svezapod.rs/naruci/info/286e6f6e13009370f4ca3658dcc6dee4/********',
          proforma_url:
            'https://www.svezapod.rs/naruci/proforma/286e6f6e13009370f4ca3658dcc6dee4/********',
          payment_url: null,
          verificationURL:
            'https://suf.purs.gov.rs/v/?vl=A0VXQjJWWVJYREJWOEdQTzDrAQAAwAEAAACPbgIAAAAAAAABj8g9CwYAAAwxMDoxMDE3NDk0MTAbBCYME1vQNYEl8CDdrWU7Q96PgX2s%2BIVfWyw4Lfk3C3%2BQ4TTaIiqktnFDSeB4819HQ7ESS3bWojWJLKlao9P16lAMU2Pbnw8H4TFZCHMQSZ%2FPkKFGGwqHTzIU7EnZsv2k1oNHZR63cOX8Nb7mwgA%2FnW8Fjj3J7IKm7sWegp2AXjZPLrKhVhK8g7l6ZZSFUvZn3h%2Ffbq1sWisVV2uA0Lg5adlfzJANSg5kW%2FCe8wvYSpvhm1nh77uahoS6ivmKAwUQPJhol0zxv8XxR4Q9%2BwzdhFoxQCzNLq0NllcdbXJkR1rQLcj9UUf8dJwePk9LB%2BudRuR3dHR7dBnNTMs4eSH6zmh9THiO5duVmKQUn%2FFOsqf9z3dP3MO2fCVEniE9KIItMzHocrUJhad0dAu%2BlG9zefeIrb2KmihruvSxRLXx%2BTwrHifoBRnVVpoXIcPvIAX48matOQQ1dH13kEyB4jBPuzffuQeuIrJQ%2BWIUjP21x6cGJ7E9Jg9N1fdr7u8rvljFy0IA5c5h1o%2BIGpg%2FkeHRyYJhEurbQ%2BEgq4lmQjmH6EcQgpiiN8uEW%2FP8o6mvK0SKI%2Bww56zW%2FnF90NSMC2rcI9YjgqfCHXdLHduz475NkEH5hcrRXJUrFsD9kxKifZycLBUY4f0C113gredQCkuFsZnBX5u7Dx%2BPfPA00czkcy1oDLDY87ipRlhyYpWihT4%3D',
          createdAt: '2024-05-30T08:43:19.000000Z',
          invoicePdfUrl:
            'https://storage.fiscomm.rs/storage/NhEarGVgTlRKp7zTMfOdLvbte5J3/invoices/2024/05/30/EWB2VYRX-DBV8GPO0-491l7xi2e861.pdf',
        },
        {
          sys_id: 104291265,
          id: '58594196',
          payment_status: 'awaiting',
          payment_status_text: 'Plaćanje na čekanju',
          payment_method_code: 'cash_delivery',
          payment_method_name: 'Plaćanje pouzećem',
          payment_details: [],
          shipping_status: 'shipped',
          shipping_status_text: 'Porudžbina se isporučuje',
          shipping_method_id: 2,
          shipping_method_name: 'Direktna dostava kurirskim službama',
          fulfillment_info: null,
          parcels: null,
          shipping_amount: 450,
          shipping_tax_amount: 75,
          shipping_cod_amount: 800,
          subtotal_including_tax: 350,
          subtotal_excluding_tax: 291.67,
          tax_amount: 133.32999999999998,
          subtotal_amount: 800,
          total_amount: 800,
          paid_amount: null,
          currency: 'RSD',
          customer_id: 3355,
          customer_email: '<EMAIL>',
          customer_ip_address: null,
          customer_ip_country: null,
          customer_device: null,
          customer_lang: 'sr_RS',
          customer_note: null,
          customer_notification:
            'Pošiljka br.KO0067838632-Dexpress\nLink za praćenje pošiljke:https://bexexpress.rs/pracenje-posiljke',
          date_created: '2024-05-28T16:40:20+03:00',
          date_modified: '2024-05-30T13:21:16+03:00',
          payment_date: null,
          date_shipped: '2024-05-30T13:21:16+03:00',
          created_by: 'user',
          traffic_source: null,
          source_referrer: null,
          source_type: 'user',
          meta_fields: null,
          tags: null,
          billing_type: 'individual',
          billing_name: 'Ivica Mirković',
          billing_company_name: null,
          billing_company_number: null,
          billing_company_vat: null,
          billing_country_code: 'RS',
          billing_country_name: 'Србија',
          billing_state: null,
          billing_city: 'Beograd-Zvezdara',
          billing_address: 'Ulofa Palmea 9/9',
          billing_postal_code: '11050',
          billing_full_address: 'Ulofa Palmea 9/9, 11050, Beograd-Zvezdara, RS',
          billing_phone: '*********',
          shipping_as_billing: true,
          shipping_name: 'Ivica Mirković',
          shipping_country_code: 'RS',
          shipping_country_name: 'Србија',
          shipping_state: null,
          shipping_city: 'Beograd-Zvezdara',
          shipping_address: 'Ulofa Palmea 9/9',
          shipping_postal_code: '11050',
          shipping_phone: '*********',
          shipping_full_address:
            'Ulofa Palmea 9/9, 11050, Beograd-Zvezdara, RS',
          shipping_address_id: ********,
          bank_name: null,
          bank_account: null,
          billing_company_bank_name: null,
          billing_company_bank_account: null,
          billing_address_id: ********,
          line_items: [
            {
              item_type: 'product',
              product_id: 332,
              product_sku: 'EL00330',
              product_ean: null,
              product_ext_ref: 'Dobavljač: ELAN Šifra: 142592',
              product_erp_id: null,
              product_name: 'Lepak za gumu ELAPREN 1150 u tubi - 60 ml',
              product_url: 'https://www.svezapod.rs/kupi?id=332',
              product_image_url:
                'https://c.cdnmp.net/*********/p/t/6/lepak-za-gumu-elapren-1150-u-tubi-60-ml~1066.jpg',
              product_tax_name: 'PDV',
              product_tax_percent: 20,
              category_id: 6,
              category_name: 'LEPLJENJE PODNIH OBLOGA',
              manufacturer_id: ********,
              manufacturer_name: 'ELAN',
              product_availability_id: 4,
              quantity: 1,
              unit_price_net: 291.67,
              unit_tax_amount: 58.329999999999984,
              unit_price_gross: 350,
              line_subtotal_net: 291.67,
              line_tax_amount: 58.33,
              line_subtotal_gross: 350,
              unit_id: 3,
              unit_type: 'pakovanje',
              weight: 0.1,
              name: 'Lepak za gumu ELAPREN 1150 u tubi - 60 ml',
              unitPrice: 350,
              unitPriceGrossWithDiscount: 350,
              totalAmountWithDiscount: 350,
              totalAmount: 350,
            },
          ],
          info_url:
            'https://www.svezapod.rs/naruci/info/d32cad1eae46484f75e3ad468ef33dde/58594196',
          proforma_url:
            'https://www.svezapod.rs/naruci/proforma/d32cad1eae46484f75e3ad468ef33dde/58594196',
          payment_url: null,
        },
      ],
      total: 3603,
      current: 10,
    };

    return of(ordersData);
  }

  fiscalise(id: string, trainingMode: boolean = false) {
    const endpoint = trainingMode ? 'training' : 'normal';
    return this.http.post(
      `${this.otherUrl}/merchant-pro/fiscalise-invoice/${endpoint}`,
      { id: id },
      { headers: this.headers }
    );
  }

  updateColumnSettings(columns: string[]) {
    return this.http.post(
      `${environment.mainSelfcare.baseUrl}/settings/merchant-pro/columns`,
      columns,
      { headers: this.headers }
    );
  }
  getColumnSettings(): Observable<string[]> {
    return this.http.get<string[]>(
      `${environment.mainSelfcare.baseUrl}/settings/merchant-pro/columns`,
      { headers: this.headers }
    );
  }

  async getSettings({ uid }: { uid: string }): Promise<any> {
    return (
      (await this.firestore.doc(`merchant-pro-settings/${uid}`).get()) as any
    ).pipe(
      map((settingsSnap: any) => {
        const settings = settingsSnap.data();
        return {
          ...settings,
          emailTemplate: {
            html: settings?.emailTemplate?.html,
            json: settings?.emailTemplate?.json
              ? JSON.parse(settings?.emailTemplate?.json)
              : null,
          },
        };
      })
    );
  }

  async updateSettings({ uid, settings }: { uid: string; settings: any }) {
    return (await this.firestore
      .doc(`merchant-pro-settings/${uid}`)
      .set(settings, { merge: true })) as any;
  }
}
