textarea{
    width: 100%;
}

.container-fluid{
    background: white;
    padding: 40px 30px;
}

.mat-slider {
    width: 100%;
}

button{
    background-color:#008BFF;
    border: none;
    cursor: pointer;
    color: white;
    font-size: 0.9rem;
    align-items: center;
    border-radius: 0.25rem;
    padding: 10px;
    width: 100%;
}

iframe{
    width: 100%;
    min-height:550px;
}


::ng-deep{
    .mat-slider-thumb-label{
        display: flex !important;
    }
  
    .mat-slider-horizontal .mat-slider-thumb-label-text{
        transform:  rotate(-45deg) !important;
    }
}

.row{
    padding-bottom: 20px;
}

p{
    display: flex;
    margin: 0;
    margin-bottom: 10px;
    align-items: center;
}