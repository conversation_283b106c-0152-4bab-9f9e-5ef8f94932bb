import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MpOrdersComponent } from './containers/mp-orders/mp-orders.component';
import { MpSettingsComponent } from './containers/mp-settings/mp-settings.component';
import { RouterModule, Routes } from '@angular/router';
import { OrdersTableComponent } from './components/orders-table/orders-table.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { RowExpandDetailsComponent } from './components/row-expand-details/row-expand-details.component';
import { OrdersHeaderComponent } from './components/orders-header/orders-header.component';
import { MpNavbarComponent } from './components/mp-navbar/mp-navbar.component';
import { BulkFiscalizeDialogComponent } from './components/bulk-fiscalize-dialog/bulk-fiscalize-dialog.component';
import { RefundOrderDialogComponent } from './components/refund-order-dialog/refund-order-dialog.component';


const routes: Routes = [
  {
    path:'',
    component: MpOrdersComponent
  },
  {
    path:'podesavanja',
    component: MpSettingsComponent
  },
];


@NgModule({
  declarations: [
    MpOrdersComponent,
    MpSettingsComponent,
    OrdersTableComponent,
    RowExpandDetailsComponent,
    OrdersHeaderComponent,
    MpNavbarComponent,
    BulkFiscalizeDialogComponent,
    RefundOrderDialogComponent
  ],
  imports: [
    CommonModule,
    SharedModule,
    RouterModule.forChild(routes),
  ]
})
export class MerchantProModule { }
