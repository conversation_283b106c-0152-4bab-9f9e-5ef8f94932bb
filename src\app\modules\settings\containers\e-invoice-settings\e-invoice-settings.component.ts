import { Component, OnInit } from '@angular/core';
import { AngularFireAuth } from '@angular/fire/compat/auth';
import { take } from 'rxjs';
import { AuthService } from 'src/app/core/services/auth.service';
import { ClientService } from 'src/app/shared/services/backend/clients/client.service';
import { CommonFirestoreService } from 'src/app/shared/services/firestore/common-firestore.service';

@Component({
  selector: 'app-e-invoice-settings',
  templateUrl: './e-invoice-settings.component.html',
  styleUrls: ['./e-invoice-settings.component.scss'],
})
export class EInvoiceSettingsComponent implements OnInit {
  userInfo: any;
  uid: any;

  eInvoicesInfo: {
    apiKey: string;
    demoApiKey: string;
  } = {
    demoApiKey: '',
    apiKey: '',
  };

  constructor(
    private clientService: ClientService,
    private commonFirestoreService: CommonFirestoreService,
    private authService: AuthService
  ) {}

  ngOnInit(): void {
    this.authService
      .getUserId()
      .pipe(take(1))
      .subscribe((uid: any) => {
        this.uid = uid;
        this.loadSettings();
      });
  }

  private loadSettings() {
    this.commonFirestoreService
      .listenToSettingsChange$(this.uid)
      .pipe(take(1))
      .subscribe(() => this.getUserData());
  }

  private getUserData() {
    this.clientService.getClient(this.uid).subscribe((client) => {
      this.initalizeEInvoiceSettings(client);
      this.userInfo = client;
    });
  }

  private initalizeEInvoiceSettings(data: any) {
    this.eInvoicesInfo = {
      apiKey: data?.eInvoice?.apiKey || '',
      demoApiKey: data?.eInvoice?.apiKeyDemo || '',
    };
  }

  demoApiKeyChanged(e: any) {
    this.eInvoicesInfo.demoApiKey = e.target.value;
  }
  apiKeyChanged(e: any) {
    this.eInvoicesInfo.apiKey = e.target.value;
  }

  saveEInvoicesSettings() {
    this.userInfo.eInvoice = {
      ...this.userInfo.eInvoice,
      apiKeyDemo: this.eInvoicesInfo.demoApiKey,
      apiKey: this.eInvoicesInfo.apiKey,
    };
    this.clientService
      .updateClient(
        this.uid,
        this.userInfo,
        'Vaša podešavanja su uspešno sačuvana.'
      )
      .subscribe();
  }
}
