<div class="container-fluid" id="dialog" *transloco="let t">
  <div class="row">
    <div class="col-lg-10">
      <h1 class="modal-title border-bottom" mat-dialog-title>
        {{ t("receipts.new_receipt") }}
      </h1>
    </div>
    <div class="col-lg-1 col-sm-1 d-flex justify-content-end">
      <mat-icon class="x-button" (click)="minimize()" mat-dialog-close>minimize</mat-icon>
    </div>
    <div class="col-lg-1 col-sm-1">
      <mat-icon class="x-button" (click)="closeDialog()">close</mat-icon>
    </div>
  </div>

  <!-- Error summary section -->
  <div *ngIf="showErrors && !formService.isValid()" class="error-summary-container">
    <div class="error-summary">
      <h4>{{ t("error.please_fix_errors") || 'Please fix the following errors:' }}</h4>
      <ul>
        <li *ngIf="receiptsForm.hasError('validateAmountExceptAdvance')">
          {{ t("error.invalid_payment_amount") || 'Invalid payment amount' }}
        </li>
        <li *ngIf="receiptsForm.hasError('sumComparisonValidator')">
          {{ t("error.payment_items_mismatch") || 'Total payment amount must match total item amount' }}
        </li>
        <li *ngIf="receiptsForm.hasError('referentDocumentNumberRequired')">
          {{ t("error.referent_document_required") || 'Referent document number is required' }}
        </li>
        <li *ngIf="receiptsForm.hasError('refDocDtRequired')">
          {{ t("error.referent_date_required") || 'Referent document date is required' }}
        </li>
        <li *ngIf="receiptsForm.hasError('customerIndetificationRequired')">
          {{ t("error.customer_id_required") || 'Customer identification is required' }}
        </li>
        <li *ngIf="receiptsForm.hasError('buyerIdRequired')">
          {{ t("error.buyer_id_required") || 'Buyer ID is required' }}
        </li>
        <li *ngIf="receiptsForm.hasError('buyerCostIdRequired')">
          {{ t("error.buyer_cost_id_required") || 'Buyer cost center ID is required' }}
        </li>
        <li *ngIf="receiptsForm.getError('requiredMetaFields') as metaFieldErrors">
          <span>{{ t("error.required_meta_fields") || 'Required meta fields are missing' }}:</span>
          <ul class="nested-errors">
            <li *ngFor="let key of getErrorKeys(metaFieldErrors)">
              {{ getMetaFieldName(key) }}
            </li>
          </ul>
        </li>
        <li *ngIf="getItemsWithErrors().length > 0">
          {{ t("error.item_errors") || 'Items have errors' }}:
          <ul class="nested-errors">
            <li *ngFor="let error of getItemsWithErrors()">{{ error }}</li>
          </ul>
        </li>
        <li *ngIf="getPaymentsWithErrors().length > 0">
          {{ t("error.payment_errors") || 'Payments have errors' }}:
          <ul class="nested-errors">
            <li *ngFor="let error of getPaymentsWithErrors()">{{ error }}</li>
          </ul>
        </li>
      </ul>
    </div>
  </div>

  <div class="row" id="container">
    <p>{{ t("required_info") }}</p>
    <div class="col-lg-4" id="leftPart">
      <form [formGroup]="formService.getReceiptForm()">

        <!-- add here currncy info -->
        <div class="col-lg-12" class="fromGroupContainer">
          <h4>{{ t("receipts.currency_info") }}</h4>

          <app-fiscomm-select [control]="formService.getCurrencyControl()" [placeholder]="t('receipts.input.currency')"
            [options]="selectDataService.getCurrencies()">
          </app-fiscomm-select>

          <!-- if currency is different from RSD, need to show input for currcy date -->
          <app-fiscomm-input *ngIf="formService.getCurrencyControl().value !== 'RSD'"
            [control]="formService.getCurrencyDateControl()" placeholder="Datum valute" type="date" step="1"
            max="{{ today | date : 'yyyy-MM-dd' }}">
          </app-fiscomm-input>


          <app-fiscomm-input *ngIf="formService.getCurrencyControl().value !== 'RSD'"
            [control]="formService.getExchangeRateDisplayControl()" [readOnly]="true" suffix="RSD">

          </app-fiscomm-input>

        </div>

        <!-- Opsti podaci form group -->
        <div class="col-lg-12" class="fromGroupContainer">
          <h4>{{ t("receipts.base_data") }}</h4>

          <app-fiscomm-select [control]="formService.getInvoiceType()"
            placeholder="{{ t('receipts.input.receipt_type') }}" [options]="selectDataService.getInvoiceTypes()">
          </app-fiscomm-select>

          <app-fiscomm-select [control]="formService.getTransactionType()"
            placeholder="{{ t('receipts.input.transaction_type') }}" [options]="
              selectDataService.getTransactionTypes(
                formService.getInvoiceType().value
              )
            ">
          </app-fiscomm-select>

          <app-fiscomm-input [control]="formService.getInvoiceNumberPos()"
            placeholder="{{ t('receipts.input.receipt_number') }}">
          </app-fiscomm-input>

          <app-fiscomm-input [control]="formService.getReferentDocumentNumber()"
            placeholder="{{ t('receipts.input.receipt_referent') }}" [loading]="loadingReferentDocNumber">
          </app-fiscomm-input>

          <ng-container *ngIf="
              (formService.getReferentDocumentNumber().hasError('badRequest') ||
                outsideCheckbox.value) &&
              !formService.getReferentDocumentNumber().hasError('invalidFormat')
            ">
            <p class="warning">
              *Referentni broj računa se ne poklapa ni sa jednim računom u
              Fiscomm sistemu,
            </p>
            <div class="d-flex">
              <input type="checkbox" [formControl]="outsideCheckbox" />
              <p class="warning">
                Račun je kreiran van Fiscomm sistema, dozvoli referentni broj.
              </p>
            </div>
          </ng-container>

          <ng-container *ngIf="!data?.disabled?.referentDocumentNumber">
            <p class="warning mb-3" *ngIf="
                formService.hasError('referentDocumentNumberRequired') ||
                formService.getInvoiceType().value == 'Advance'
              ">
              {{ t("receipts.referent_required") }}
            </p>
          </ng-container>

          <p class="warning mb-3" *ngIf="
              formService.getReferentDocumentNumber().hasError('invalidFormat')
            ">
            Referentni broj racuna mora biti u formatu
            <a target="_blank"
              href="https://tap.suf.purs.gov.rs/Help/view/894863027/%D0%9F%D0%A4%D0%A0-%D0%B1%D1%80%D0%BE%D1%98/sr-Cyrl-RS">PFR
              broja racuna</a>
          </p>

          <app-fiscomm-input *ngIf="formService.getTransactionType().value == 'Finalize'"
            [control]="formService.getReferentDocumentDT()" placeholder="Datum referentnog računa" type="datetime-local"
            step="1" max="{{ today | date : 'yyyy-MM-dd' }}">
          </app-fiscomm-input>

          <app-fiscomm-input *ngIf="formService.dateNeeded()" [control]="formService.getDateControl()"
            placeholder="Datum placanja avansa" type="datetime-local" step="1" max="{{ today | date : 'yyyy-MM-dd' }}">
          </app-fiscomm-input>

          <app-cashier-autocomplete [control]="formService.getControl('cashier')" [placeholder]="t('receipts.cashier')">
          </app-cashier-autocomplete>
        </div>
        <!-- Identifikacija kupca form group -->
        <div class="col-lg-12 fromGroupContainer">
          <h4>{{ t("receipts.customer_iden") }}</h4>

          <app-fiscomm-select [control]="formService.getBuyerIDPrefix()" [enableNone]="true"
            placeholder="{{ t('receipts.input.document_type') }}" [options]="buyerDocTypes">
          </app-fiscomm-select>

          <app-fiscomm-input [control]="formService.getBuyerId()"
            placeholder="{{ t('receipts.input.document_number') }}">
          </app-fiscomm-input>
          <p class="warning" *ngIf="formService.hasError('customerIndetificationRequired')">
            {{ t("receipts.customer_iden_required") }}
          </p>
        </div>
        <!-- Opciono polje kupca form group -->
        <div class="col-lg-12 fromGroupContainer">
          <h4>{{ t("receipts.optional_customer") }}</h4>

          <app-fiscomm-select [enableNone]="true" [control]="formService.getPrefixBuyerCostCenterId()"
            placeholder="{{ t('receipts.input.document_type') }}" [options]="buyerCostTypes">
          </app-fiscomm-select>

          <app-fiscomm-input [control]="formService.getSufixBuyerCostCenterId()"
            placeholder="{{ t('receipts.input.document_number') }}">
          </app-fiscomm-input>

          <app-fiscomm-input *ngIf="formService.isClientEmailActive" [control]="formService.getControl('toEmail')"
            placeholder="Email kupca">
          </app-fiscomm-input>
        </div>
        <!-- Podaci o putovanju form group -->
        <ng-container *ngIf="!data?.disabled?.metaFields">
          <ng-container *ngIf="metaFields$ | async as metaFields">
            <div class="col-lg-12 fromGroupContainer" *ngIf="metaFields.length > 0">
              <h4>{{ t("receipts.custom_fields") }}</h4>
              <app-meta-field-input *ngFor="let field of metaFields; let i = index" appearance="outline"
                [placeholder]="(field.isRequired ? '* ' : '') + (field.name || field.key)" [data]="field.values"
                [control]="formService.getMetaFieldControl(field.key)">
              </app-meta-field-input>
            </div>
          </ng-container>
        </ng-container>
      </form>
    </div>
    <div class="col-lg-8 rightPart">
      <div class="row">
        <div class="col-lg-12">
          <app-create-receipt-items></app-create-receipt-items>
        </div>
        <div class="col-lg-12" id="paymentContainer">
          <div class="container">
            <div class="row" id="paymentTitle">
              <div class="col-sm-6">
                <h5>{{ t("receipts.payment") }}:</h5>
              </div>
            </div>
            <div class="row" id="paymentContent">
              <form [formGroup]="formService.getReceiptForm()" class="row">
                <ng-container *ngFor="
                    let itemRow of formService.getPaymentFormArray().controls;
                    let i = index
                  ">
                  <div class="col-lg-5 col-md-12">
                    <app-fiscomm-select formGroupName="payment" [control]="
                        formService.getPaymentControl(i, 'paymentType')
                      " placeholder="{{ t('receipts.input.payment_type') }}"
                      [options]="selectDataService.getPaymentTypes()">
                    </app-fiscomm-select>
                  </div>

                  <div class="col-lg-2" *ngIf="!formService.advanceColumnsNeeded()"></div>

                  <div class="col-lg-3 col-md-12">
                    <app-fiscomm-input class="numberInput" type="number" [decimals]="2"
                      [control]="formService.getPaymentControl(i, 'amount')" placeholder="{{ t('amount') }}"
                      suffix="{{ formService.getCurrencyControl().value }}">
                    </app-fiscomm-input>
                    <div *ngIf="formService.getCurrencyControl().value !== 'RSD'" class="rsd-conversion">
                      ({{ getPaymentAmountInRSD(i) | number:'1.2-2' }} RSD)
                    </div>
                  </div>



                  <div class="col-lg-3" *ngIf="formService.advanceColumnsNeeded()">
                    <app-fiscomm-input class="numberInput" type="number" [control]="
                        formService.getPaymentControl(i, 'paidByAdvance')
                      " placeholder="{{ t('advance_amount') }}" suffix="{{ formService.getCurrencyControl().value }}">
                    </app-fiscomm-input>
                    <div *ngIf="formService.getCurrencyControl().value !== 'RSD'" class="rsd-conversion">
                      ({{ getAdvanceAmountInRSD(i) | number:'1.2-2' }} RSD)
                    </div>
                  </div>
                  <div class="col-lg-1 d-flex align-items-center" *ngIf="i > 0"
                    (click)="formService.removePaymentRow(i)">
                    <mat-icon>delete</mat-icon>
                  </div>
                </ng-container>

                <!-- MOZDA CE TREBATI JOS JEDAN INPUT AKO JE ADVANCE-->
              </form>
              <div class="d-flex justify-content-between" id="paymentButtons">
                <span class="d-flex align-items-center" (click)="addPayment()"><mat-icon>add</mat-icon>{{
                  t("receipts.add_payment_type") }}</span>
                <span class="d-flex align-items-center">
                  <form [formGroup]="formService.getReceiptForm()">
                    <input type="checkbox" [formControl]="formService.getEnableDifferentSums()" /><label>{{
                      t("receipts.different_sum") }}</label>
                  </form>
                </span>
              </div>
            </div>
            <div class="row d" id="paymentFooter">
              <ng-container *ngIf="formService.advanceColumnsNeeded()">
                <div class="col-sm-7 d-flex">
                  <h5>{{ t("paid") }}:</h5>
                </div>
                <div class="col-sm-5 d-flex justify-content-end align-items-center">
                  <p>
                    {{ formService.getPaymentSum() }} {{ formService.getCurrencyControl().value }}
                    <span *ngIf="formService.getCurrencyControl().value !== 'RSD'" class="rsd-conversion">
                      ({{ getTotalPaymentInRSD() | number:'1.2-2' }} RSD)
                    </span>
                  </p>
                </div>
                <div class="col-sm-7 d-flex">
                  <h5>{{ t("paid_advance") }}:</h5>
                </div>
                <div class="col-sm-5 d-flex justify-content-end align-items-center">
                  <p>
                    {{ formService.getPayByAdvanceSum() }} {{ formService.getCurrencyControl().value }}
                    <span *ngIf="formService.getCurrencyControl().value !== 'RSD'" class="rsd-conversion">
                      ({{ getTotalAdvanceInRSD() | number:'1.2-2' }} RSD)
                    </span>
                  </p>
                </div>
              </ng-container>

              <div class="col-sm-7 d-flex">
                <h5>{{ t("total_paid") }}:</h5>
              </div>
              <div class="col-sm-5 d-flex justify-content-end align-items-center">
                <p>
                  {{ formService.getTotalSum() }} {{ formService.getCurrencyControl().value }}

                  <span [ngClass]="
                      formService.hasError('sumComparisonValidator')
                        ? 'red'
                        : ''
                    ">({{ t("left") }}:
                    {{
                    formService.getItemsAmountSum() -
                    formService.getTotalSum() | number : "1.2-2"
                    }}
                    {{ formService.getCurrencyControl().value }})</span>
                    <span *ngIf="formService.getCurrencyControl().value !== 'RSD'" class="rsd-conversion">
                      ({{ formService.getTotalSumInRSDDisplay() }} RSD)
                    </span>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-6">
          <app-fiscomm-input [control]="formService.getControl('textHeader')" placeholder="Header text">
          </app-fiscomm-input>
        </div>
        <div class="col-6">
          <app-fiscomm-input [control]="formService.getControl('textFooter')" placeholder="Footer text">
          </app-fiscomm-input>
        </div>
      </div>
    </div>
  </div>


  <!-- Currency information callout -->
  <div class="currency-info-callout" *ngIf="formService.getCurrencyControl().value !== 'RSD'">
    <div class="callout-content">
      <div class="callout-icon">
        <mat-icon>currency_exchange</mat-icon>
      </div>
      <div class="callout-text">
        <h5>{{ t("receipts.currency_info_notice_title") }}</h5>
        <p>
          {{ t("receipts.currency_info_notice", { currency:
          formService.getCurrencyControl().value,
          currencyDate: formService.getCurrencyDateControl().value,
          totalAmount: formService.getTotalSumInRSDDisplay() }) }}
        </p>
        <!-- <p class="highlight-text">{{All values will be automatically converted to RSD during submission.}}</p> -->
        <p class="highlight-text">{{ t("receipts.currency_info_notice_highlight") }}</p>
      </div>
    </div>
  </div>
  <div class="col-12 d-flex justify-content-center">
    <input type="checkbox" [formControl]="formService.openInNew" checked />
    <label>Otvori račun nakon kreiranja</label>
  </div>
  <!-- Dialog actions -->
  <div class="col-lg-12  ">
    <mat-dialog-actions class="buttons gap-3">
      <button class="noButton" mat-button (click)="closeDialog()" [mat-dialog-close]="false">
        {{ t("give_up") }}
      </button>
      <div class="create-button-container">
        <button class="yesButton" disabled="{{ isDisabled() || loadingReferentDocNumber }}" (click)="createReceipt()"
          mat-button>
          {{ t("create") }}
        </button>
        <mat-icon *ngIf="isDisabled() || loadingReferentDocNumber" class="info-icon" [matTooltip]="errorTooltipContent"
          matTooltipClass="error-tooltip" (click)="openErrorModal()">info</mat-icon>
      </div>
    </mat-dialog-actions>
  </div>
</div>
