# Product Import Functionality

## Overview
This update adds a comprehensive product import system with duplicate handling, validation, and user-friendly interface. The system supports importing products via TSV (Tab-Separated Values) files with automatic duplicate detection and resolution.

## New Features

### 1. Import Template Dialog
- **Component**: `ImportTemplateDialogComponent`
- **Location**: `src/app/modules/products/components/import-template-dialog/`
- **Purpose**: Provides users with clear instructions and examples for product import
- **Features**:
  - Detailed column descriptions
  - Example data format
  - Bilingual support (English/Serbian)
  - Responsive design

### 2. Duplicate Handling
- **Location**: `src/app/modules/products/containers/products/products.component.ts`
- **Features**:
  - Automatic detection of duplicates based on GTIN or product code
  - Deduplication within imported file
  - Conflict resolution with existing products
  - User-friendly duplicate selection dialog

### 3. Import Process
1. **File Validation**:
   - Checks for required fields (name, price)
   - Validates data format
   - Removes duplicates within the file

2. **Duplicate Resolution**:
   - Identifies conflicts with existing products
   - Shows dialog for user selection
   - Updates existing products if selected
   - Adds new products if no conflicts

3. **Progress Tracking**:
   - Success/error count tracking
   - User feedback via snackbar messages
   - Automatic refresh after import

## File Format

### TSV Template
Required columns:
- `name` (required): Product name
- `price` (required): Product price in RSD
- `gtin` (optional): Global Trade Item Number
- `productCode` (optional): Internal product code

Example:
```tsv
name    price   gtin           productCode
"Product 1" 100.00  "1234567890123"  "P001"
"Product 2" 250.50  "2345678901234"  "P002"
```

## Translations

### English
```json
{
  "items": {
    "import_template_title": "Product Import Format",
    "import_template_description": "To import products, prepare a TSV (Tab-Separated Values) file with the following columns:",
    "import_template_columns": {
      "name": "name - Product name (required)",
      "price": "price - Product price in RSD (required)",
      "gtin": "gtin - Global Trade Item Number (optional)",
      "productCode": "productCode - Your internal product code (optional)"
    },
    "import_template_note": "Note: Products with the same GTIN or product code will be considered duplicates. Only one product with the same identifier will be kept."
  }
}
```

### Serbian
```json
{
  "items": {
    "import_template_title": "Format za uvoz proizvoda",
    "import_template_description": "Za uvoz proizvoda, pripremite TSV (Tab-Separated Values) fajl sa sledećim kolonama:",
    "import_template_columns": {
      "name": "name - Naziv proizvoda (obavezno)",
      "price": "price - Cena proizvoda u RSD (obavezno)",
      "gtin": "gtin - Globalni broj trgovinske jedinice (opciono)",
      "productCode": "productCode - Vaša interni šifra proizvoda (opciono)"
    },
    "import_template_note": "Napomena: Proizvodi sa istim GTIN ili šifrom proizvoda će biti smatrani duplikatima. Samo jedan proizvod sa istim identifikatorom će biti zadržan."
  }
}
```

## Technical Implementation

### Components
1. **ImportTemplateDialogComponent**
   - Displays import instructions
   - Shows example data
   - Provides column descriptions

2. **ProductsComponent**
   - Handles import process
   - Manages duplicate detection
   - Coordinates with services

### Services
1. **DialogService**
   - Manages dialog interactions
   - Handles dialog configurations

2. **ProductsService**
   - Processes product creation/updates
   - Manages data persistence

## Usage

1. Click the "Product Import Format" button in the products page
2. Review the import format instructions
3. Prepare your TSV file following the template
4. Use the import button to upload your file
5. Review and resolve any duplicates
6. Confirm the import

## Error Handling

- Invalid file format
- Missing required fields
- Duplicate products
- Import failures
- Network errors

## UI/UX Improvements

- Clear instructions
- Visual feedback
- Progress tracking
- Error messages
- Responsive design
- Bilingual support

## Dependencies

- Angular Material
- Transloco (for translations)
- MatDialog
- MatTable
- MatSnackBar
