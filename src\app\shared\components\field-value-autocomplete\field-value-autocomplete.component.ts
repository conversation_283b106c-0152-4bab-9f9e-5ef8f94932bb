import { Component, Input, OnInit, Optional, Self, Output, EventEmitter, OnD<PERSON>roy } from '@angular/core';
import { ControlValueAccessor, FormControl, NgControl } from '@angular/forms';
import { Observable, Subject, of } from 'rxjs';
import { debounceTime, distinctUntilChanged, switchMap, map, takeUntil } from 'rxjs/operators';
import { FieldSearchValuesService } from '../../services/firestore/field-search-values.service';

@Component({
  selector: 'app-field-value-autocomplete',
  templateUrl: './field-value-autocomplete.component.html',
  styleUrls: ['./field-value-autocomplete.component.scss']
})
export class FieldValueAutocompleteComponent implements OnInit, ControlValueAccessor, OnDestroy {
  @Input() fieldId: string = '';
  @Input() placeholder: string = 'Type to search';
  @Input() limit: number = 10;
  @Output() selected = new EventEmitter<string>();

  inputControl = new FormControl();
  filteredOptions: Observable<string[]>;
  private destroy$ = new Subject<void>();

  // For ControlValueAccessor
  private onChange: any = () => {};
  private onTouched: any = () => {};
  disabled = false;

  constructor(
    private fieldSearchValuesService: FieldSearchValuesService,
    @Optional() @Self() public ngControl: NgControl
  ) {
    // Register this component as a ControlValueAccessor
    if (this.ngControl) {
      this.ngControl.valueAccessor = this;
    }
    this.filteredOptions = of([]);
  }

  ngOnInit(): void {
    // Setup autocomplete search
    this.filteredOptions = this.inputControl.valueChanges.pipe(
      takeUntil(this.destroy$),
      debounceTime(300),
      distinctUntilChanged(),
      switchMap(searchText => {
        if (!searchText || typeof searchText !== 'string') {
          return of([]);
        }
        return this.fieldSearchValuesService.getAutocompleteSuggestions(
          this.fieldId,
          searchText,
          this.limit
        );
      })
    );

    // For ControlValueAccessor, sync with parent form
    this.inputControl.valueChanges.pipe(
      takeUntil(this.destroy$)
    ).subscribe(value => {
      this.onChange(value);
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // Handle the option selection from autocomplete
  optionSelected(value: string): void {
    this.selected.emit(value);
  }

  // ControlValueAccessor implementation
  writeValue(value: any): void {
    this.inputControl.setValue(value, { emitEvent: false });
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
    if (isDisabled) {
      this.inputControl.disable();
    } else {
      this.inputControl.enable();
    }
  }
}
