import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
import { BulkActionsService } from '../../services/bulk-actions.service';;
import { ReceiptsService } from 'src/app/shared/services/backend/receipts/receipts.service';
import { Receipts } from 'src/app/shared/services/backend/receipts/types/receipts-response.dto';
import { CreateReceipt } from 'src/app/shared/services/backend/receipts/types/receipts-request.dto';
import { SelectTypesDataService } from 'src/app/shared/services/select-types-data.service';
import { BulkRefundFormService } from '../../../modules/receipts/services/bulk-refund-form.service';

@Component({
  selector: 'app-bulk-action-dialog',
  templateUrl: './bulk-action-dialog.component.html',
  styleUrls: ['./bulk-action-dialog.component.scss']
})
export class BulkActionDialogComponent implements OnInit {


  createDisabled = false;
  isLoading = false;

  receipts: any[] = [];
  receiptStatus: any[] = [];
  currentIndex = 0;

  allDone: string = 'waiting';

  constructor(private bulkActionService: BulkActionsService,
    @Inject(MAT_DIALOG_DATA) private data: any,
    private receiptsService: ReceiptsService,
    public selectDataService: SelectTypesDataService,
    public refundFormService:BulkRefundFormService ) {
    this.receipts = this.bulkActionService.getSelectedElements();
  }

  ngOnInit() {
    this.handleValidation();
    this.initalizeRefundDataForOrders();
    for (let receipts of this.receipts) {
      this.receiptStatus.push('waiting'); 
      if(this.data='refund')
        this.refundFormService.addBuyerRow(receipts)
    }
  }

  private initalizeRefundDataForOrders() {
    if (this.data == 'order-refund') {
      this.data = 'refund';
      let orders = this.receipts;
      this.receipts = [];
      orders.forEach(order => {
        this.receipts.push(...order.receipts); 
      })
    }
  }

  handleValidation() {
    switch (this.data) {
      case "advance":
        break;
      case "finalize":
        this.filterFinalized();
        break;
      case "refund":
        this.validateRefundAction();
        break; 
    }
  }

  handleCreate() {
    switch (this.data) {
      case "advance":
        this.createAdvanceBulk();
        break;
      case "finalize":
        this.createBulkFinalize();
        break;
      case "refund":
        this.createRefundBulk();
        break; 
    }
  }

  private validateRefundAction() {
    let form = this.refundFormService.getRefundForm();
    this.refundFormService.getRefundForm().valueChanges.subscribe(x => {
      this.createDisabled = !form.valid;
    });
  }

  createBulkFinalize() {
    
  }

  private filterFinalized() {
    this.receipts=this.receipts.filter((receipt: any) => !receipt.finalized);
  }

  createAdvanceBulk() {
    setInterval(() => {
      if (this.currentIndex < this.receipts.length) {
        this.allDone = 'progress';
        this.receiptStatus[this.currentIndex] = 'progress';
        const createDto = this.convertToCreateDto(this.receipts[this.currentIndex], 'Advance');
        this.receiptsService.createReceipt(createDto)
          .subscribe((result: any) => {
            this.receiptStatus[statusIndex++] = 'done';
            if (this.receiptStatus.every(status => status == 'done'))
              this.allDone = 'done';
          });
        this.currentIndex++;
      }
    }, 100);
    let statusIndex = 0;
    this.isLoading = false;
    this.bulkActionService.emptyAllElements();
  }

  createRefundBulk() {
    setInterval(() => {
      if (this.currentIndex < this.receipts.length) {
        this.receiptStatus[this.currentIndex] = 'progress';
        this.allDone = 'progress';
        const refundDto = this.convertToRefundDto(this.receipts[this.currentIndex],this.makeId(this.currentIndex));
        this.receiptsService.createReceipt(refundDto)
          .subscribe((result: any) => {
            this.receiptStatus[statusIndex++] = 'done';
            if (this.receiptStatus.every(status => status == 'done'))
              this.allDone = 'done';
          });
        this.currentIndex++;
      }
    }, 100);
    let statusIndex = 0;
    this.isLoading = false;
    this.bulkActionService.emptyAllElements();
  }

  private convertToCreateDto(receipt:Receipts,transactionType:string) {
    return {
      invoiceType: receipt.invoice_type.invoice_type,
      items: receipt.items,
      metaFields: receipt.meta_fields,
      payment: receipt.payments as any,
      transactionType: transactionType,
      sdcDateTime: receipt.sdc_date_time,
      buyerId: receipt.buyer_id,
      cashier: receipt.cashier,
      buyerCostCenterId: receipt.buyer_cost_center_id,
      invoiceNumber: receipt.invoice_number,
      invoiceNumberPos: receipt.invoice_number_pos,
      advanceChain: receipt.advance_chain
    }
  }

  
  private convertToRefundDto(receipt: Receipts,buyerId:string) {
    let tmp: CreateReceipt;
    return tmp ={
      referentDocumentNumber:receipt.invoice_number,
      invoiceType: receipt.invoice_type.invoice_type,
      items: receipt.items,
      metaFields: receipt.meta_fields,
      payment: receipt.payments as any,
      transactionType: 'Refund',
      sdcDateTime: receipt.sdc_date_time,
      buyerId: buyerId,
      cashier: receipt.cashier,
      buyerCostCenterId: receipt.buyer_cost_center_id,
      invoiceNumberPos: receipt.invoice_number_pos,
      advanceChain: receipt.advance_chain,
    }
  }

  private makeId(index:number) {
    return `${this.refundFormService.getBuyerIDPrefix(index).value}:${this.refundFormService.getBuyerIDSufix(index).value}`;
  }

}
