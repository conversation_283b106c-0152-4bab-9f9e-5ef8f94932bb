import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Cashier } from 'src/app/models/cashier.model';

@Component({
  selector: 'app-cashier-list',
  templateUrl: './cashier-list.component.html',
  styleUrls: ['./cashier-list.component.scss']
})
export class CashierListComponent implements OnInit {
  @Input() cashiers: Cashier[] = [];
  @Input() displayColumns: string[] = [];
  @Input() defaultCashierName: string | null = null;
  @Output() actionEmitter = new EventEmitter<any>();

  constructor() { }

  ngOnInit(): void {
  }

  onEdit(cashier: Cashier): void {
    this.actionEmitter.emit({ action: 'edit', data: cashier });
  }

  onDelete(cashier: Cashier): void {
    if (confirm('Are you sure you want to delete this cashier?')) {
      this.actionEmitter.emit({ action: 'delete', data: cashier });
    }
  }

  onToggleDefault(cashier: Cashier): void {
    this.actionEmitter.emit({ action: 'toggle-default', data: cashier });
  }
}
