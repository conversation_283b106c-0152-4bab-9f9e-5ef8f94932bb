import { ChangeDetectorRef, Component, Inject, LOCALE_ID, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { ReportsService } from 'src/app/shared/services/backend/reports/reports.service';
import { formatDate } from '@angular/common';
import { TranslocoService, translate } from '@ngneat/transloco';
import { CommonFirestoreService } from '../../../../shared/services/firestore/common-firestore.service';
import { AngularFireAuth } from '@angular/fire/compat/auth';

@Component({
  selector: 'app-reports',
  templateUrl: './reports.component.html',
  styleUrls: ['./reports.component.scss']
})
export class ReportsComponent implements OnInit {

  reports: any[] = [];

  reportTypes = this.initReportTypes();

  reportForm: FormGroup = new FormGroup({});
  reportUrl: string | undefined = undefined;
  reportName: string = '';
  reportDates: any = {};
  currentDate: Date = new Date();

  maxDate: Date = new Date();
  cooldownTimer: number = 0;

  constructor(private reportsService: ReportsService, private translocoService:TranslocoService
    , private fb: FormBuilder, @Inject(LOCALE_ID) public locale: string,
    private cdr: ChangeDetectorRef
  ) { }

  ngOnInit(): void {
    this.currentDate = new Date();
    this.initForm();
    this.getReports();

    this.getFromLocalStorage();
    this.translocoService.langChanges$.subscribe(() => {
      this.reportTypes = this.initReportTypes();
    })

    this.reportForm.get('dateFrom')?.valueChanges.subscribe((value) => {
      if (value) {
        let maxDate = new Date(value);
        maxDate.setMonth(maxDate.getMonth() + 1);
        this.maxDate = maxDate > this.currentDate ? this.currentDate : maxDate;
      }
    })

  }

  private initReportTypes() {

    return [
      { label:  translate('reports.report_orders'), value: 'orders' },
      { label:  translate('reports.report_products'), value: 'products' },
      { label:  translate('reports.report_payments'), value: 'billing', disabled: true },
    ];
  }

  private initForm() {
    this.reportForm = this.fb.group({
      type: ['', Validators.required],
      name: '',
      dateFrom: ['', Validators.required],
      dateTo: ['', Validators.required],
    });
  }

  getControl(name: string) {
    return this.reportForm.get(name) as FormControl;
  }

  createReport() {
    let formValue = this.reportForm.value;
    this.formatDates(formValue);
    this.reportsService.generateReport(formValue).subscribe(response => {
      this.setToLocalStorage(response.url, formValue);

      this.getReports();
    });
  }

  getReports() {
    this.reportsService.getReports().subscribe((response: any) => {
      console.log(response);
      this.reports = response
        .map((report: any) => {
          return {
            ...report,
            transloco: report.type === 'orders' ? 'reports.report_orders' :
              report.type === 'products' ? 'reports.report_products' :
                'reports.report_payments'
          };
        })

      if (this.reports.length > 0) {
        const lastReport = this.reports[0];
        const lastReportDate = new Date(lastReport.createdAt);
        const currentTime = new Date();
        const cooldownTime = 10 * 60 * 1000;
        const diff = currentTime.getTime() - lastReportDate.getTime();

        this.cooldownTimer = diff > cooldownTime ? 0 : cooldownTime - diff;

        const interval = setInterval(() => {
          this.cooldownTimer = this.cooldownTimer - 1000;

          if (this.cooldownTimer < 0) {
            clearInterval(interval);
            this.cooldownTimer = 0;
          }

        }, 1000);
      }

    });
  }

  private setToLocalStorage(url:string,formValue:any) {
    localStorage.setItem('reportUrl', url);
    localStorage.setItem('reportDates', JSON.stringify({ from: formValue.dateFrom, to: formValue.dateTo }));
    if (formValue.name != '') {
      localStorage.setItem('reportName', formValue.name);
      this.reportName = formValue.name;
    }
    this.reportUrl = url;
  }

  private getFromLocalStorage() {
    if (localStorage.getItem('reportName'))
      this.reportName = localStorage.getItem('reportName') as string;
    if (localStorage.getItem('reportUrl'))
      this.reportUrl = localStorage.getItem('reportUrl') as string;
    if (localStorage.getItem('reportDates'))
      this.reportDates = JSON.parse(localStorage.getItem('reportDates') as string);
  }

  private removeFromLocalStorage() {
    localStorage.removeItem('reportName');
    localStorage.removeItem('reportDates');
    localStorage.removeItem('reportUrl');
  }

  private formatDates(object:any) {
    object.dateFrom = formatDate(object.dateFrom, 'yyyy-MM-dd', this.locale);
    object.dateTo = formatDate(object.dateTo, 'yyyy-MM-dd',this.locale);
  }

  closeReportUrl() {
    this.reportUrl = undefined;
    this.reportName = '';
    this.removeFromLocalStorage();
  }

}
