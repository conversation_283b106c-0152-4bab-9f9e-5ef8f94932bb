<div class="container-fluid">
  <div class="row">
    <div class="col-12 col-sm-6 col-md-6 d-flex align-items-center">
      <p>Po stranici:</p>
      <select class="ms-1" (change)="pageSizeChange($event)">
        <option *ngFor="let size of pageSizes" value="{{ size }}">
          {{ size }}
        </option>
      </select>
    </div>
    <div class="col-12 col-sm-6 col-md-6 d-flex rightContainer">
      <div class="d-flex align-items-center right" style="width: 100px">
        <p>Stranica: {{ page }}</p>
      </div>
      <div class="buttons d-flex justify-content-end">
        <div
          class="page_button d-flex align-items-center"
          [ngClass]="isFirstPage() ? 'disabled' : ''"
          (click)="isFirstPage() ? '' : first()"
        >
          <mat-icon>first_page</mat-icon>
        </div>
        <div
          class="page_button d-flex align-items-center"
          [ngClass]="isFirstPage() ? 'disabled' : ''"
          (click)="isFirstPage() ? '' : prev()"
        >
          <mat-icon>navigate_before</mat-icon>
        </div>
        <div
          class="page_button d-flex align-items-center"
          [ngClass]="isLastPage() ? 'disabled' : ''"
          (click)="isLastPage() ? '' : next()"
        >
          <mat-icon>navigate_next</mat-icon>
        </div>
      </div>
    </div>
  </div>
</div>
