::ng-deep {
  .create-invoice .mat-dialog-container {
    padding: 50px;
    background-color: white;
    position: relative;
  }
}

.buttons {
  display: flex;
  justify-content: end;
  padding-top: 15px;
}

.contentRow {
  border-bottom: 1px solid #e1e5f2;
}

.leftCol {
  border-right: 1px solid #e1e5f2;
  padding-right: 40px;
  margin-top: 15px;
  margin-bottom: 15px;
  p {
    margin: 0;
  }
  .customer {
    border-top: 1px solid #e1e5f2;
    padding-top: 15px;
  }
  .customerContainer {
    min-height: 180px;
    border-bottom: 1px solid #e1e5f2;
    margin-bottom: 15px;
  }
  .action-icons {
    color: #008bff;
    div {
      display: flex;
      justify-content: center;
      padding: 6px 20px;
      border: 1px solid #008bff;
    }
  }
}

.rightCol {
  margin-top: 15px;
  margin-bottom: 15px;
  padding-left: 20px;
  p {
    margin-bottom: 4px;
  }
}

.firstRow {
  border-bottom: 1px solid #e1e5f2;
  padding-bottom: 10px;
}

.x-button {
  position: absolute;
  top: 10px;
  right: 8px;
  cursor: pointer;
  background-color: transparent;
  border: none;
  .mat-icon {
    width: 2rem;
    height: 2rem;
    font-size: 1.8rem;
  }
}

.table-container {
  width: 100%;
  max-height: 300px;
  overflow-y: auto;
  overflow-x: auto;
}

table {
  border-radius: 8px;
  margin: 0px !important;
  thead {
    background-color: #dddfea;
  }
  th {
    padding-top: 10px;
    padding-bottom: 10px;
    font-family: "Inter" !important;
    font-weight: 600;
    padding-left: 20px;
    font-size: 15px;
    color: #022b3a;
    width: 250px !important;
    text-wrap: nowrap !important;
  }
  td {
    padding-top: 10px;
    padding-bottom: 10px;
    padding-left: 20px;
    font-size: 14px;
    color: #022b3a;
    width: 250px !important;
    text-wrap: nowrap !important;
  }
  .icons {
    display: flex;
    height: 100%;
    cursor: pointer;
  }
  tr:nth-child(even) {
    background-color: #f0f2fc;
  }
}

.details {
  .col-2 {
    text-align: right;
  }
}
