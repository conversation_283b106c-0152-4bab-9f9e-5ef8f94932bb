import { Component, Inject, OnInit, Ng<PERSON>one } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { Observable, Subject, of, BehaviorSubject } from 'rxjs';
import { map, startWith, switchMap, debounceTime, distinctUntilChanged, tap, catchError } from 'rxjs/operators';
import { CustomField } from 'src/app/models/custom-field.model';
import { FieldSearchValuesService, SearchOptions, SearchValue } from 'src/app/shared/services/firestore/field-search-values.service';
import { animate, state, style, transition, trigger } from '@angular/animations';

// Extended SearchValue type with UI properties
interface ExtendedSearchValue extends SearchValue {
  showReceipts?: boolean;
  expanding?: boolean; // To track expansion state for smooth animations
}

@Component({
  selector: 'app-field-values-dialog',
  templateUrl: './field-values-dialog.component.html',
  styleUrls: ['./field-values-dialog.component.scss'],
  animations: [
    trigger('expandReceipts', [
      state('collapsed', style({
        height: '0',
        minHeight: '0',
        opacity: 0,
        overflow: 'hidden',
        visibility: 'hidden' // Hide completely when collapsed
      })),
      state('expanding', style({
        height: '*',
        opacity: 0.5,
        overflow: 'hidden',
        visibility: 'visible'
      })),
      state('expanded', style({
        height: '*',
        opacity: 1,
        overflow: 'visible',
        visibility: 'visible'
      })),
      transition('collapsed => expanding', [
        style({ height: '0', opacity: 0, visibility: 'visible' }),
        animate('150ms cubic-bezier(0.4, 0.0, 0.2, 1)')
      ]),
      transition('expanding => expanded', [
        animate('150ms cubic-bezier(0.4, 0.0, 0.2, 1)')
      ]),
      transition('expanded => collapsed', [
        style({ overflow: 'hidden' }),
        animate('200ms cubic-bezier(0.4, 0.0, 0.2, 1)')
      ])
    ]),
    trigger('rotateIcon', [
      state('collapsed', style({ transform: 'rotate(0deg)' })),
      state('expanding', style({ transform: 'rotate(180deg)' })),
      state('expanded', style({ transform: 'rotate(180deg)' })),
      transition('collapsed <=> *', [
        animate('200ms cubic-bezier(0.4, 0.0, 0.2, 1)')
      ])
    ])
  ]
})
export class FieldValuesDialogComponent implements OnInit {
  searchValues: ExtendedSearchValue[] = [];
  filteredValues: Observable<ExtendedSearchValue[]>;
  searchControl = new FormControl('');
  loading = true;
  private readonly LIMIT = 50;
  searchOptions: SearchOptions = { limit: this.LIMIT };

  // To handle pagination
  hasMoreResults = false;
  totalResults = 0;
  private valuesSubject = new BehaviorSubject<ExtendedSearchValue[]>([]);

  constructor(
    public dialogRef: MatDialogRef<FieldValuesDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { field: CustomField },
    private fieldSearchValuesService: FieldSearchValuesService,
    private zone: NgZone
  ) {
    this.filteredValues = this.valuesSubject.asObservable();
  }

  ngOnInit(): void {
    if (this.data.field.id) {
      // Initial load with empty search
      this.loadInitialValues();

      // Subscribe to search input changes
      this.searchControl.valueChanges.pipe(
        debounceTime(300), // Wait for 300ms pause in typing
        distinctUntilChanged(), // Only if the value has changed
        tap(() => this.loading = true)
      ).subscribe(searchText => {
        this.loadSearchValues(searchText || '');
      });
    }
  }

  /**
   * Load initial values when the dialog opens
   */
  private loadInitialValues(): void {
    this.loading = true;
    const limit = this.LIMIT;
    // Query for the initial values (no search term, just limit)
    this.fieldSearchValuesService.querySearchValues(
      this.data.field.id!,
      { limit } // Start with 20 results
    ).pipe(
      catchError(error => {
        console.error('Error loading values:', error);
        this.loading = false;
        return of([]);
      })
    ).subscribe(values => {
      // Process values
      const extendedValues = values.map(value => ({
        ...value,
        showReceipts: false,
        expanding: false
      }));

      // Delay showing the list items by 600ms
      setTimeout(() => {
        this.zone.run(() => {
          // Update the state
          this.searchValues = extendedValues;
          this.valuesSubject.next(extendedValues);
          this.hasMoreResults = values.length >= limit;
          this.totalResults = values.length;
          this.loading = false;
        });
      }, 600);
    });
  }

  /**
   * Load search values based on current search term
   */
  private loadSearchValues(searchTerm: string): void {
    if (!this.data.field.id) {
      this.loading = false;
      this.valuesSubject.next([]);
      return;
    }

    this.searchOptions.searchTerm = searchTerm;
    this.searchOptions.includeReceipts = true; // Request to include receipts data

    this.fieldSearchValuesService.querySearchValues(
      this.data.field.id,
      this.searchOptions
    ).pipe(
      catchError(error => {
        console.error('Error searching values:', error);
        this.loading = false;
        return of([]);
      })
    ).subscribe(values => {
      // Process values
      const extendedValues = values.map(value => ({
        ...value,
        showReceipts: false,
        expanding: false
      }));

      // Delay showing the list items by 600ms
      setTimeout(() => {
        this.zone.run(() => {
          // Update the state
          this.searchValues = extendedValues;
          this.valuesSubject.next(extendedValues);
          this.hasMoreResults = values.length >= this.searchOptions.limit!;
          this.totalResults = values.length;
          this.loading = false;
        });
      }, 600);
    });
  }

  /**
   * Load more results (pagination)
   */
  loadMore(): void {
    if (!this.hasMoreResults) return;

    this.loading = true;
    this.searchOptions.limit = (this.searchOptions.limit || this.LIMIT) + this.LIMIT;

    this.loadSearchValues(this.searchControl.value || '');
  }

  /**
   * Toggle display of receipts list for a value with a smooth animation
   */
  toggleReceiptsList(item: ExtendedSearchValue): void {
    // Prevent multiple clicks during animation
    if (item.expanding) return;

    if (!item.showReceipts) {
      // Starting expansion - set intermediate state
      item.expanding = true;
      item.showReceipts = true;

      // Complete expansion after a short delay
      setTimeout(() => {
        this.zone.run(() => {
          item.expanding = false;
        });
      }, 150);
    } else {
      // Collapse immediately
      item.expanding = false;
      item.showReceipts = false;
    }
  }

  /**
   * Get animation state for a value
   */
  getAnimationState(item: ExtendedSearchValue): string {
    if (!item.showReceipts) return 'collapsed';
    return item.expanding ? 'expanding' : 'expanded';
  }

  close(): void {
    this.dialogRef.close();
  }
}
