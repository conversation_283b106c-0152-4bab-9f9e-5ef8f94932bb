import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { map } from 'rxjs';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root',
})
export class InvoiceService {
  baseURL: string = environment.admin.baseUrl;

  constructor(private http: HttpClient) {}

  getClientInvoices(uid: string, searchParams?: any) {
    return this.http
      .get(this.baseURL + '/billing/invoices?uid=' + uid, {
        params: searchParams,
      })
      .pipe(
        map((response: any) => {
          return {
            ...response,
            data: response.data.map((invoice: any) => {
              return { ...invoice, id: this.createInvoiceNumber(invoice) };
            }),
          };
        })
      );
  }

  getClientInvoiceStats(uid: string) {
    return this.http.get(this.baseURL + '/billing/invoices/counts?uid=' + uid);
  }

  getInvoices(searchParams?: any) {
    return this.http
      .get(this.baseURL + '/billing/invoices', {
        params: searchParams,
      })
      .pipe(
        map((response: any) => {
          return {
            ...response,
            data: response.data.map((invoice: any) => {
              return { ...invoice, id: this.createInvoiceNumber(invoice) };
            }),
          };
        })
      );
  }

  private createInvoiceNumber(invoice: any) {
    if (
      !invoice.user.billing ||
      !invoice.year ||
      !invoice.user.billing.customerBillingId
    )
      return '\\';
    let number = invoice.user.billing.customerBillingId;
    number = number + '' + invoice.year.toString().slice(-2);
    let month = invoice.month - 1;
    month < 10 ? (number += 0 + '' + month) : (number += '' + month);
    return number;
  }

  getInvoiceStats() {
    return this.http.get(this.baseURL + '/billing/invoices/counts');
  }

  bulkDownloadInvoice(ids: string[]) {
    const downloadUrl =
      this.baseURL + '/billing/invoices/download/bulk?ids=' + ids.join(',');
    window.open(downloadUrl);
  }
}
