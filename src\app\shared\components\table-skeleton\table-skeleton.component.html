<div class="table-container">
    <table class="table mat-elevation-z8">
        <thead>
          <tr>
            <ng-container *ngFor="let col of columns">
                <ng-container *ngIf="col != 'checkbox' && col != 'expand'; else emptyCell">
                   <th>{{col}}</th>
                </ng-container>
                <ng-template #emptyCell><th></th></ng-template>
            </ng-container>
    
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let i of [].constructor(rowsNumber)"> 
            <td class="loading"  *ngFor="let col of columns">
              <div *ngIf="col != 'checkbox' && col != 'expand'" class="bar"></div>
            </td>
          </tr>
        </tbody>
    </table>
</div>
