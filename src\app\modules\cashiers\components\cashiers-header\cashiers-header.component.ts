import { Component, EventEmitter, OnInit, Output } from '@angular/core';

@Component({
  selector: 'app-cashiers-header',
  templateUrl: './cashiers-header.component.html',
  styleUrls: ['./cashiers-header.component.scss']
})
export class CashiersHeaderComponent implements OnInit {
  @Output() actionEmitter: EventEmitter<any> = new EventEmitter();

  constructor() { }

  ngOnInit(): void {
  }

  addCashier(): void {
    this.actionEmitter.emit({ action: 'add' });
  }
}
