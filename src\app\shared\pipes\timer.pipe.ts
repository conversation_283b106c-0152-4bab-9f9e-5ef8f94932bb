import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'timer',
  pure: false, // Set to false to update dynamically if the cooldownTimer changes
})
export class TimerPipe implements PipeTransform {
  transform(value: number | null): string {

    if (!value) {
      return '';
    }

    const remainingTime = value;

    const minutes = Math.floor((remainingTime / (1000 * 60)) % 60);
    const seconds = Math.floor((remainingTime / 1000) % 60);

    if (minutes === 0) {
      return `${seconds}s`;
    }

    return `${minutes}m ${seconds}s`;
  }
}
