import { Component, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';

@Component({
  selector: 'app-create-invoice-dialog',
  templateUrl: './create-invoice-dialog.component.html',
  styleUrls: ['./create-invoice-dialog.component.scss'],
})
export class CreateInvoiceDialogComponent implements OnInit {
  today: Date = new Date();
  invoiceForm: FormGroup;

  constructor() {
    this.invoiceForm = new FormGroup({
      invoiceNumber: new FormControl('', [Validators.required]),
      password: new FormControl('', [Validators.required]),
    });
  }

  ngOnInit(): void {}

  onTableScroll(event: Event) {
    const target = event.target as HTMLElement;
    const thead = target.querySelector('thead');
    if (thead) {
      const translationValue = `translate(0, ${target.scrollTop}px)`;
      thead.style.transform = translationValue;
    }
  }
}
