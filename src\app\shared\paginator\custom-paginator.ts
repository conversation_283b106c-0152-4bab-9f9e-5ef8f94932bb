import { Injectable } from "@angular/core";
import { MatPaginatorIntl } from "@angular/material/paginator";
import { TranslocoService, translate } from "@ngneat/transloco";
@Injectable()
export class CustomPaginatorIntl extends MatPaginatorIntl {
    constructor(translocoService:TranslocoService) {
        super();
        translocoService.langChanges$.subscribe(() => {
            this.getRangeLabel = (page: number, pageSize: number, length: number) =>{
                return translate('page_num') + ': ' + (page + 1);
            }
            this.itemsPerPageLabel = translate('per_page') + ':';

        })
    }
  
    
  }