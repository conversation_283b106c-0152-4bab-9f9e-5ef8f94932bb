import { Component, Inject, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { Cashier } from 'src/app/models/cashier.model';
import { CashierService } from 'src/app/shared/services/firestore/cashier.service';
@Component({
  selector: 'app-add-cashier-dialog',
  templateUrl: './add-cashier-dialog.component.html',
  styleUrls: ['./add-cashier-dialog.component.scss']
})
export class AddCashierDialogComponent implements OnInit {
  cashierForm: FormGroup;
  isEditMode = false;
  nameControl = new FormControl('', [Validators.required]);
  cashierCodeControl = new FormControl('');
  notesControl = new FormControl('');

  constructor(
    private fb: FormBuilder,
    private dialogRef: MatDialogRef<AddCashierDialogComponent>,
    private cashierService: CashierService,
    @Inject(MAT_DIALOG_DATA) public data: { cashier?: Cashier }
  ) {
    this.cashierForm = this.fb.group({
      id: [null],
      name: this.nameControl,
      cashierCode: this.cashierCodeControl,
      notes: this.notesControl,
      active: [true]
    });
  }

  ngOnInit(): void {
    if (this.data?.cashier) {
      this.isEditMode = true;
      this.cashierForm.patchValue(this.data.cashier);
    }
  }

  onSubmit(): void {
    if (this.cashierForm.valid) {
      const cashier: Cashier = this.cashierForm.value;

      if (this.isEditMode) {
        this.cashierService.updateCashier(cashier).subscribe(() => {
          this.dialogRef.close(cashier);
        });
      } else {
        this.cashierService.addCashier(cashier).subscribe(() => {
          this.dialogRef.close(cashier);
        });
      }
    }
  }

  onCancel(): void {
    this.dialogRef.close();
  }
}
