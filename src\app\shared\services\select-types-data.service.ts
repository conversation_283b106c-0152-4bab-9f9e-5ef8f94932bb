import { Injectable } from '@angular/core';
import { TranslocoService, translate } from '@ngneat/transloco';
import { filter } from 'rxjs';
import { AVAILABLE_CURRENCIES, CURRENCY_ORDER, CURRENCY_TRANSLATIONS, getUsedCurrencies } from '../constants/available_currencies.const';

@Injectable({
  providedIn: 'root',
})
export class SelectTypesDataService {
  private invoiceTypes = this.initInvoiceTypes();
  private transactionTypes = this.initTransactionTypes();
  private transactionAdvanceTypes = this.initTransactionAdvanceTypes();
  private buyerIdDocumentTypes = this.initBuyerIdTypes();
  private buyerCostCenterIdTypes = this.initBuyerCostIdTypes();
  private paymentTypes = this.initPaymentTypes();
  private currencies = this.initCurrencies();

  private initPaymentTypes() {
    return [
      { value: 'Other', label: translate('payment_types.other') },
      { value: 'Cash', label: translate('payment_types.cash') },
      { value: 'Card', label: translate('payment_types.card') },
      { value: 'Check', label: translate('payment_types.check') },
      { value: 'WireTransfer', label: translate('payment_types.wireTransfer') },
      { value: 'Voucher', label: translate('payment_types.voucher') },
      { value: 'MobileMoney', label: translate('payment_types.mobileMoney') },
    ];
  }

  private initInvoiceTypes() {
    return [
      {
        value: 'Normal',
        label: translate('invoice_types.normal'),
      },
      {
        value: 'Proforma',
        label: translate('invoice_types.proforma'),
      },
      {
        value: 'Training',
        label: translate('invoice_types.training'),
      },
      {
        value: 'Advance',
        label: translate('invoice_types.advance'),
      },
      {
        value: 'Copy',
        label: translate('invoice_types.copy'),
        disabled: true,
      },
    ];
  }

  private initTransactionTypes() {
    return [
      {
        value: 'Sale',
        label: translate('transaction_types.sale'),
      },
      {
        value: 'Refund',
        label: translate('transaction_types.refund'),
      },
    ];
  }

  private initTransactionAdvanceTypes() {
    return [
      {
        value: 'Sale',
        label: translate('transaction_types.sale'),
      },
      {
        value: 'Refund',
        label: translate('transaction_types.refund'),
      },
      {
        value: 'Finalize',
        label: translate('transaction_types.finalize'),
      },
    ];
  }

  private initBuyerIdTypes() {
    return [
      { value: '10', label: translate('buyer_id_types.PIB_home') },
      { value: '11', label: translate('buyer_id_types.JMBG_home') },
      { value: '12', label: translate('buyer_id_types.PIB_JBKJS') },
      { value: '13', label: translate('buyer_id_types.pensioner_card') },
      { value: '14', label: translate('buyer_id_types.PIB_company') },
      { value: '15', label: translate('buyer_id_types.JMBG_company') },
      { value: '16', label: translate('buyer_id_types.BPG') },
      { value: '20', label: translate('buyer_id_types.personal_ID') },
      { value: '21', label: translate('buyer_id_types.refugee_ID') },
      { value: '22', label: translate('buyer_id_types.EBS') },
      { value: '23', label: translate('buyer_id_types.passport') },
      { value: '30', label: translate('buyer_id_types.foreign_passport') },
      { value: '31', label: translate('buyer_id_types.LK') },
      { value: '32', label: translate('buyer_id_types.personal_ID_MKD') },
      { value: '33', label: translate('buyer_id_types.personal_ID_MNE') },
      { value: '34', label: translate('buyer_id_types.personal_ID_ALB') },
      { value: '35', label: translate('buyer_id_types.personal_ID_BIH') },
      { value: '36', label: translate('buyer_id_types.personal_ID_Decision') },
      { value: '40', label: translate('buyer_id_types.TIN') },
    ];
  }

  private initBuyerCostIdTypes() {
    return [
      { value: '20', label: translate('buyer_cost_id_types.SNPDV') },
      { value: '21', label: translate('buyer_cost_id_types.LNPDV') },
      { value: '30', label: translate('buyer_cost_id_types.PPO_PDV') },
      { value: '31', label: translate('buyer_cost_id_types.ZPPO_PDV') },
      { value: '32', label: translate('buyer_cost_id_types.MPPO_PDV') },
      { value: '33', label: translate('buyer_cost_id_types.IPPO_PDV') },
      { value: '50', label: translate('buyer_cost_id_types.corp_card_number') },
      { value: '60', label: translate('buyer_cost_id_types.time_period') },
    ];
  }

  getInvoiceTypes() {
    return this.invoiceTypes;
  }

  getTransactionTypes(isAdvance?: string) {
    if (isAdvance == 'Advance') return this.transactionAdvanceTypes;
    return this.transactionTypes;
  }

  getBuyerIdDocumentTypes() {
    return this.buyerIdDocumentTypes;
  }

  getBuyerCostCenterIdTypes() {
    return this.buyerCostCenterIdTypes;
  }

  getPaymentTypes() {
    return this.paymentTypes;
  }

  getCurrencies() {
    return this.currencies;
  }

  constructor(private translocoService: TranslocoService) {
    this.translocoService.langChanges$.subscribe(() => {
      this.translocoService.events$
        .pipe(
          filter(
            (event) =>
              event.type == 'translationLoadSuccess' ||
              event.type == 'langChanged'
          )
        )
        .subscribe(() => {
          this.invoiceTypes = this.initInvoiceTypes();
          this.paymentTypes = this.initPaymentTypes();
          this.transactionTypes = this.initTransactionTypes();
          this.transactionAdvanceTypes = this.initTransactionAdvanceTypes();
          this.buyerIdDocumentTypes = this.initBuyerIdTypes();
          this.buyerCostCenterIdTypes = this.initBuyerCostIdTypes();
          this.currencies = this.initCurrencies();
        });
    });
  }

  private initCurrencies(): { value: string; label: string }[] {
    return getUsedCurrencies().map((currency) => {
      return {
        value: currency.code,
        label: translate(currency.translation_key) + ' (' + currency.code + ')'
      };
    });
  }
}
