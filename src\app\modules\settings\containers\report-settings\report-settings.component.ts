import { Component, OnInit, Input } from '@angular/core';
import { AngularFireAuth } from '@angular/fire/compat/auth';
import { FormControl } from '@angular/forms';
import { take } from 'rxjs';
import { AuthService } from 'src/app/core/services/auth.service';
import { ClientService } from 'src/app/shared/services/backend/clients/client.service';
import { CommonFirestoreService } from 'src/app/shared/services/firestore/common-firestore.service';

@Component({
  selector: 'app-report-settings',
  templateUrl: './report-settings.component.html',
  styleUrls: ['./report-settings.component.scss'],
})
export class ReportSettingsComponent implements OnInit {
  userInfo: any;

  reportsInfo: {
    emails: string[];
    daily: boolean;
    weekly: boolean;
    monthly: boolean;
  } = {
    emails: [''],
    daily: false,
    weekly: false,
    monthly: false,
  };

  emailsArr: FormControl[] = [];

  uid: string = '';
  clientData?: any;

  constructor(
    private clientService: ClientService,
    private commonFirestoreService: CommonFirestoreService,
    private authService: AuthService
  ) {}

  ngOnInit(): void {
    this.authService
      .getUserId()
      .pipe(take(1))
      .subscribe((uid: any) => {
        this.uid = uid;
        this.loadSettings();
      });
  }

  private loadSettings() {
    this.commonFirestoreService
      .listenToSettingsChange$(this.uid)
      .pipe(take(1))
      .subscribe(() => this.getUserData());
  }

  private getUserData() {
    this.clientService.getClient(this.uid).subscribe((client) => {
      this.initalizeReportsSettings(client);
      this.clientData = client;
    });
  }

  private initalizeReportsSettings(data: any) {
    this.reportsInfo = {
      emails: data?.reports?.emails || [''],
      daily: data?.reports?.daily || false,
      weekly: data?.reports?.weekly || false,
      monthly: data?.reports?.monthly || false,
    };
    this.reportsInfo.emails.forEach((el) =>
      this.emailsArr.push(new FormControl(el))
    );
  }

  addEmail() {
    this.reportsInfo.emails.push('');
    this.emailsArr?.push(new FormControl(''));
  }

  removeEmail(index: number) {
    this.reportsInfo.emails.splice(index, 1);
    this.emailsArr.splice(index, 1);
  }

  getEmailControl(index: number) {
    return this.emailsArr[index];
  }

  toggleChange(type: 'daily' | 'weekly' | 'monthly') {
    this.reportsInfo[type] = !this.reportsInfo[type];
  }

  saveReportsSettings() {
    if (this.clientData) {
      this.reportsInfo.emails = [];
      this.emailsArr.forEach((control) =>
        this.reportsInfo.emails.push(control.value)
      );
      this.clientData.reports = {
        ...this.clientData.reports,
        ...this.reportsInfo,
      };
      this.clientService
        .updateClient(
          this.uid,
          this.clientData,
          'Vaša podešavanja su uspešno sačuvana.'
        )
        .subscribe();
    }
  }
}
