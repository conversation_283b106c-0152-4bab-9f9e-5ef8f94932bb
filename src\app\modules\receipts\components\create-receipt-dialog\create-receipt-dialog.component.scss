::ng-deep {
  #dialog {
    .mat-form-field-outline {
      background-color: white;
    }
    .mat-form-field-wrapper {
      padding-bottom: 8px;
    }

    .mat-form-field-appearance-outline .mat-form-field-wrapper {
      margin: 0px !important;
    }
  }
}

::ng-deep {
  .create-receipt .mat-dialog-container {
    padding: 0px !important;
  }
}

#dialog {
  color: #022b3a;
}

button {
  padding: 0.2rem 3.7rem;
  color: white;
}
.noButton {
  background-color: #eb8e88;
}

.yesButton {
  background-color: #3dccaa;
}

.buttons {
  width: 100%;
  display: flex;
  justify-content: center;
}

button:disabled {
  background-color: #e2e2e2 !important;
  color: #a4a4a4;
}

#leftPart {
  height: 80%;
}

.fromGroupContainer {
  display: flex;
  flex-direction: column;
}

.mat-form-field {
  font-size: 13px;
  font-style: italic;
  font-weight: 500;
}

.matLabel {
  color: #044962a1;
}

h5 {
  margin: 0px;
}

p {
  margin-bottom: 0px;
  font-style: italic;
  color: #2cafc9;
}

.mat-dialog-title {
  margin-bottom: 5px;
}

.x-button {
  background-color: transparent;
  border: none;
  color: #022b3a;
  width: 20px;
}

.container {
  border-radius: 0.5rem;
  background-color: white;
  margin-top: 20px;
}

#textRight {
  text-align: right;
}
#paymentTitle {
  border-bottom: 3px solid #e1e5f2;
  padding: 15px 20px;
}

#paymentContent {
  padding-top: 10px;
  padding-bottom: 10px;
  border-bottom: 43x solid #e1e5f2;
  margin-bottom: 10px;
}
#paymentFooter {
  border-top: solid 1px #e1e5f2;
  padding-block: 20px;
  p {
    color: #022b3a;
  }
  h5 {
    font-size: 1rem;
    color: #022b3a;
    font-weight: 400;
  }
}
.row {
  padding: 10px 20px;
  justify-content: left;
}
.numberInput {
  width: 50%;
}

.rightPart {
  margin-top: 30px;
}

span {
  cursor: pointer;
  font-size: 13px;
}

.mat-icon {
  cursor: pointer;
}

.red {
  color: red;
}

.error {
  color: red;
  font-size: 12px;
  font-weight: 400px;
  font-style: italic;
}

.warning {
  color: #2cafc9;
  font-size: 12px;
  font-weight: 400px;
  font-style: italic;
}

@media only screen and (max-width: 992px) {
  p {
    font-size: 13px;
  }
  h4 {
    font-size: 18px;
  }
  .rightPart {
    .row {
      padding: 0;
    }
    #paymentContent {
      padding-left: 20px;
    }
  }
  #paymentButtons {
    margin-top: 10px;
    flex-direction: column;
    justify-content: start !important;
    padding: 0;
  }
}

@media (max-width: 576px) {
  #dialog {
    .row {
      padding: 0 !important;
    }
  }
  h4 {
    font-size: 16px;
  }
  h5 {
    font-size: 16px !important;
  }
}

// Add error summary styling at the beginning of the file
.error-summary-container {
  margin: 16px 0;

  .error-summary {
    background-color: #ffefef;
    border-left: 4px solid #f44336;
    border-radius: 4px;
    padding: 16px;
    margin-bottom: 20px;

    h4 {
      color: #d32f2f;
      margin-top: 0;
      margin-bottom: 8px;
    }

    ul {
      margin: 0;
      padding-left: 24px;

      li {
        color: #616161;
        margin-bottom: 4px;
      }

      &.nested-errors {
        margin-top: 4px;

        li {
          font-size: 0.9em;
        }
      }
    }
  }
}

// Custom error text styles to be used across the form
.error-text {
  color: #f44336;
  font-size: 12px;
  margin-top: 4px;
  margin-bottom: 8px;
}

.field-error {
  color: #f44336;
  font-weight: 500;
}

// Add indicator for required fields
.required-field::after {
  content: "*";
  color: #f44336;
  margin-left: 4px;
}

// Warning text styling
.warning {
  color: #d32f2f;
}

.create-button-container {
  display: flex;
  align-items: center;
  position: relative;
}

.info-icon {
  color: #f44336;
  margin-left: 8px;
  cursor: pointer;
  font-size: 18px;
  transition: transform 0.2s ease-in-out;

  &:hover {
    transform: scale(1.2);
  }
}

::ng-deep .error-tooltip {
  font-size: 12px;
  max-width: 280px;
  white-space: pre-line;
  background-color: rgba(0, 0, 0, 0.8);
}

.rsd-conversion {
  font-size: 0.85em;
  color: #6c757d;
  margin-top: 2px;
}

// Currency information callout styles
.currency-info-callout {
  margin: 16px 24px;
  border-radius: 8px;
  border-left: 4px solid #2196F3;
  background-color: rgba(33, 150, 243, 0.08);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &:hover {
    background-color: rgba(33, 150, 243, 0.12);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }

  .callout-content {
    display: flex;
    padding: 16px;
    align-items: flex-start;
  }

  .callout-icon {
    margin-right: 16px;

    mat-icon {
      color: #2196F3;
      font-size: 28px;
      height: 28px;
      width: 28px;
    }
  }

  .callout-text {
    flex: 1;

    h5 {
      margin-top: 0;
      margin-bottom: 8px;
      color: #2196F3;
      font-weight: 500;
    }

    p {
      margin-bottom: 8px;
      color: #333;
      font-style: normal;
    }

    .highlight-text {
      font-weight: 500;
      color: #0D47A1;
    }
  }
}

// Add spacing for dialog actions container
.dialog-actions-container {
  margin-top: 24px;
}
