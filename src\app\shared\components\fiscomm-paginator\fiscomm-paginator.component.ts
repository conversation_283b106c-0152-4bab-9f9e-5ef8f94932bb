import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { PageEvent } from '@angular/material/paginator';

@Component({
  selector: 'app-fiscomm-paginator',
  templateUrl: './fiscomm-paginator.component.html',
  styleUrls: ['./fiscomm-paginator.component.scss'],
})
export class FiscommPaginatorComponent implements OnInit {
  @Input() chunkSize: number = 0;
  @Input() pageSizes: number[] = [];
  @Input() startPage: number = 1;
  @Input() startPerPage: number = 10;

  @Output() pageChange: EventEmitter<PaginatorEvent> = new EventEmitter();
  page: number = 1;
  perPage: number = 15;

  constructor() {}

  ngOnInit(): void {
    this.page = this.startPage;
    this.perPage = this.startPerPage;
  }

  isFirstPage() {
    return this.page == 1;
  }

  isLastPage() {
    return this.perPage > this.chunkSize;
  }

  next() {
    this.page++;
    this.emitEvent();
  }

  prev() {
    this.page--;
    this.emitEvent();
  }

  first() {
    this.page = 1;
    this.emitEvent();
  }

  pageSizeChange(event: any) {
    let total = this.page * this.perPage;
    this.page = Math.floor(total / event.target.value);
    if (this.page == 0) this.page++;
    this.perPage = event.target.value;
    this.emitEvent();
  }

  private emitEvent() {
    this.pageChange.emit({ page: this.page, perPage: this.perPage });
  }
}

export interface PaginatorEvent {
  page: number;
  perPage: number;
}
