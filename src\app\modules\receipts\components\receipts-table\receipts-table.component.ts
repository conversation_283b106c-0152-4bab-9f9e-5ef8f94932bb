import {
  animate,
  state,
  style,
  transition,
  trigger,
} from '@angular/animations';
import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { Sort } from '@angular/material/sort';
import { MatDialog } from '@angular/material/dialog';
import {
  MetaFieldsDto,
  Receipts,
} from 'src/app/shared/services/backend/receipts/types/receipts-response.dto';
import { Action } from '../../containers/receipts/receipts.component';
import {
  TableColumn,
  UserSettingsService,
} from 'src/app/shared/services/backend/user/user-settings.service';
import { BulkActionsService } from '../../../../shared/services/bulk-actions.service';
import { TranslocoService } from '@ngneat/transloco';
import { DialogService } from 'src/app/shared/dialogs/dialog.service';
import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';

@Component({
  selector: 'app-receipts-table',
  templateUrl: './receipts-table.component.html',
  styleUrls: ['./receipts-table.component.scss'],
  animations: [
    trigger('detailExpand', [
      state('collapsed', style({ height: '0px', minHeight: '0' })),
      state('expanded', style({ height: '*' })),
      transition(
        'expanded <=> collapsed',
        animate('225ms cubic-bezier(0.4, 0.0, 0.2, 1)')
      ),
    ]),
  ],
})
export class ReceiptsTableComponent implements OnInit {
  showMetaField(field: MetaFieldsDto, element: any) {
    if (!element.meta_fields) return '';
    if (element.meta_fields.length <= 0) return '';
    return element.meta_fields.find((f: any) => f.key === field.key)?.value;
  }
  @Input() data: Receipts[] = [];
  @Input() metaFields: MetaFieldsDto[] = [];
  @Output() sortEvent: EventEmitter<Sort> = new EventEmitter<Sort>();
  @Output() actionEmitter: EventEmitter<Action> = new EventEmitter<Action>();

  @Input() systemColumns: string[] = [];
  allColumnsData: TableColumn[] = [];

  displayColumns: string[] = [];

  sortedData: Receipts[] = [];
  historyColumns: string[] = [
    'Broj računa',
    'Tip plaćanja',
    'Tip računa',
    'Tip transakcije',
    'Žurnal',
    'Vreme fisk.',
    'Ukupno',
  ];
  expandedElement: Receipts | null = null;

  checkboxStates: Map<string, boolean> = new Map();
  isMasterSelected: boolean = true;
  activeLang = 'sr';

  constructor(
    private userSettingsService: UserSettingsService,
    private bulkActionService: BulkActionsService,
    private translocoService: TranslocoService,
    private dialog: MatDialog,
    private dialogService: DialogService
  ) {
    this.activeLang = this.translocoService.getActiveLang();
  }

  ngOnInit(): void {
    console.log(this.systemColumns);
    this.initalizeColumnsState(this.systemColumns);
    this.filterDisplayColumns(this.systemColumns);
    this.initalizeCheckboxes();
    this.translocoService.langChanges$.subscribe(
      (lang) => (this.activeLang = lang)
    );

    console.log(this.allColumnsData);
    console.log(this.displayColumns);
    console.log(this.systemColumns);

  }

  private initalizeColumnsState(systemColumns: string[]) {
    this.allColumnsData = this.userSettingsService.getAllColumnsData();
    // reorder all columnsData by systemColumns order

    this.appendMetaFields();
    // if metafields are empty, remove metafields columns from the display columns
    if (this.metaFields.length <= 0)
      this.allColumnsData = this.allColumnsData.filter((col) => !col.metaField);


    if (this.systemColumns.length <= 0) this.initalizeFirstTimeColumns();
    else this.setColumnsState(systemColumns);
  }

  private initalizeFirstTimeColumns() {
    this.allColumnsData.forEach((col) => {
      if (col.enabled) this.systemColumns.push(col.key);
    });
  }

  private setColumnsState(systemColumns: string[]) {
    this.allColumnsData.forEach((col) => {
      col.enabled = systemColumns.includes(col.key);
    });
  }

  private appendMetaFields() {
    this.metaFields.forEach((field) =>
      this.allColumnsData.push({
        key: field.key,
        displayName: field.name || field.key,
        enabled: false,
        metaField: true,

      })
    );
  }

  private filterDisplayColumns(systemColumns: string[]) {

    systemColumns = systemColumns.filter((column, index, self) =>
      index === self.findIndex((t) => t === column)
    );

    this.systemColumns = systemColumns;
    this.displayColumns = [];
    this.displayColumns.push('checkbox');
    let tmp: TableColumn[] = [];
    if (systemColumns.length > 0){
      tmp = this.allColumnsData.filter(
        (column) => column.enabled && systemColumns.includes(column.key)
      );

      // if there is a column in system but not in alldata,
      // remove it from systemColumns and update user settings
      systemColumns = systemColumns.filter((column) =>
        tmp.some((col: any) => col.key === column)
      );
      this.userSettingsService.updateColumnSettings(systemColumns).subscribe();

      // reorder tmp to match systemColumns order
      tmp = tmp.sort((a, b) =>
        systemColumns.indexOf(a.key) - systemColumns.indexOf(b.key)
      );

      // filter out non system columns
      tmp = tmp.filter((column) => systemColumns.includes(column.key));
    }
    else tmp = this.allColumnsData.filter((column) => column.enabled);
    console.log(tmp);


    this.displayColumns.push(...tmp.map((column) => {
      if (column.metaField) return column.key;
      return column.displayName;
    }));
    this.displayColumns.push('Akcije', 'Expand');




    this.bulkActionService.emptyAllElements();
  }

  toggleColumn(column: TableColumn) {
    column.enabled = !column.enabled;
    this.updateSystemColumns(column);
    this.filterDisplayColumns(this.systemColumns);
  }

  private updateSystemColumns(column: TableColumn) {
    column.enabled
      ? this.systemColumns.push(column.key)
      : (this.systemColumns = this.systemColumns.filter(
          (key) => key !== column.key
        ));
    this.userSettingsService
      .updateColumnSettings(this.systemColumns)
      .subscribe();
  }

  /**
   * Reorders a column to a new position in the table
   * @param columnToMove The column to move
   * @param newIndex The new index for the column
   */
  reorderColumn(columnToMove: TableColumn, newIndex: number): void {
    // First, get the current index of the column in the systemColumns array
    const currentIndex = this.systemColumns.indexOf(columnToMove.key);

    if (currentIndex === -1) {
      console.error('Column not found in system columns');
      return;
    }

    // Remove the column from its current position
    this.systemColumns.splice(currentIndex, 1);

    // Insert the column at the new position
    this.systemColumns.splice(newIndex, 0, columnToMove.key);

    // Update the columns in the UI
    this.filterDisplayColumns(this.systemColumns);

    // Save the new order to user settings
    this.userSettingsService.updateColumnSettings(this.systemColumns).subscribe();
  }

  /**
   * Moves a column one position to the left in the table
   * @param column The column to move left
   */
  moveColumnLeft(column: TableColumn): void {
    const currentIndex = this.systemColumns.indexOf(column.key);
    if (currentIndex > 0) {
      this.reorderColumn(column, currentIndex - 1);
    }
  }

  /**
   * Moves a column one position to the right in the table
   * @param column The column to move right
   */
  moveColumnRight(column: TableColumn): void {
    const currentIndex = this.systemColumns.indexOf(column.key);
    if (currentIndex < this.systemColumns.length - 1 && currentIndex !== -1) {
      this.reorderColumn(column, currentIndex + 1);
    }
  }

  //checkboxes for bulk actions
  private initalizeCheckboxes() {
    this.data.forEach((receipt) => {
      this.checkboxStates.set(receipt.invoice_number, false);
    });
  }

  getCheckbox(invoice_number: any) {
    return this.checkboxStates.get(invoice_number);
  }

  checkUncheckAll() {
    this.bulkActionService.emptyAllElements();
    this.isMasterSelected = !this.isMasterSelected;
    this.checkboxStates.forEach((value, key) =>
      this.checkboxStates.set(key, this.isMasterSelected)
    );
    const checkboxes = document.querySelectorAll('.checkbox');
    checkboxes.forEach((checkbox) => {
      checkbox.dispatchEvent(new Event('change'));
    });
  }

  toggleElement(element: any) {
    let currVal = this.getCheckbox(element.invoice_number);
    this.checkboxStates.set(element.invoice_number, !currVal);
    if (this.getCheckbox(element.invoice_number))
      this.bulkActionService.addSelectedElement(element);
    else this.bulkActionService.removeSelectedElement(element);
  }

  sortData(sort: Sort) {
    this.sortEvent.emit(sort);
  }

  emitAction(value: Action) {
    this.actionEmitter.emit(value);
  }

  openSendEmailDialog(receipt: Receipts) {
    this.dialogService.openSendReceiptEmailDialog(receipt.invoice_number);
  }

  /**
   * Handle drop event when columns are reordered using drag and drop
   * @param event The drop event from CDK drag drop
   */
  dropColumn(event: CdkDragDrop<string[]>): void {

    const columnPrevIndex = event.previousIndex;
    const columnCurrentIndex = event.currentIndex;

    const systemColumns = this.systemColumns;

    // so we need to move prevIndex column to currentIndex
    // the column that was on currentIndex need to be shifted as well as all the columns after it
    const columnToMove = systemColumns[columnPrevIndex];
    systemColumns.splice(columnPrevIndex, 1);
    systemColumns.splice(columnCurrentIndex, 0, columnToMove);



    this.filterDisplayColumns(this.systemColumns);

    console.log(this.displayColumns, this.systemColumns);

    // Persist the new column order to user settings
    this.userSettingsService.updateColumnSettings(this.systemColumns)
      .subscribe({
        next: () =>
          console.log('Column order updated successfully'),
        error: (err) => console.error('Failed to update column order:', err)
      });
  }
}
