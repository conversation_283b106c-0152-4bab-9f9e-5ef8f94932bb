import { Injectable } from '@angular/core';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
  FormArray,
} from '@angular/forms';
import { BehaviorSubject, Observable } from 'rxjs';
import { MetaFieldsDto } from '../../../shared/services/backend/receipts/types/receipts-response.dto';

@Injectable({
  providedIn: 'root',
})
export class FormFiltersService {
  private filtersForm: FormGroup = new FormGroup({});

  constructor(private fb: FormBuilder) {
    this.initForm();
  }

  private initForm() {
    this.filtersForm = this.fb.group({
      invoiceNumberPos: '',
      invoiceNumber: '',
      invoiceType: '',
      transactionType: '',
      paymentType: '',
      date_after: '',
      date_before: '',
      metaFields: this.fb.array([]),
    });
  }

  getMetaFieldFormArray() {
    return this.filtersForm.get('metaFields') as FormArray;
  }

  populateMetaFields(fields: { key: string, value: string }[]) {
    if (!Array.isArray(fields)) return;
    fields.forEach((field) => this.setMetaFieldControl(field.key, field.value));
  }

  setMetaFieldControl(key: string, value: string) {
    const field = this.getMetaFieldControl(key);
    field.setValue(value);
    console.log(field, 'field');
  }

  addMetaFieldControl(key: string) {
    const field = this.fb.group({
      key: key,
      value: '',
    });
    console.log(field, 'field added');
    this.getMetaFieldFormArray().push(field);
    console.log(this.getMetaFieldFormArray().length, 'formArray', key);
    return field;
  }

  getMetaFieldControl(indexOrKey: number | string) {
    const metaFields = this.getMetaFieldFormArray();

    // If it's a string, find the control by key
    if (typeof indexOrKey === 'string') {
      const fieldGroup = metaFields.controls.find((control: any) =>{
        return control.get('key')?.value === indexOrKey
      });



      const control =  fieldGroup ? fieldGroup.get('value') as FormControl : new FormControl('');

      return control;
    }

    // If it's a number, use as index (legacy support)
    return metaFields.controls[indexOrKey]?.get('value') as FormControl || new FormControl('');
  }

  getControl(key: string) {
    return this.filtersForm.get(key) as FormControl;
  }

  getFiltersForm(): FormGroup {
    return this.filtersForm;
  }
  getInvoiceNumberPos(): FormControl {
    return this.filtersForm.get('invoiceNumberPos') as FormControl;
  }

  getInvoiceNumber(): FormControl {
    return this.filtersForm.get('invoiceNumber') as FormControl;
  }

  getInvoiceType(): FormControl {
    return this.filtersForm.get('invoiceType') as FormControl;
  }

  getTransactionType(): FormControl {
    return this.filtersForm.get('transactionType') as FormControl;
  }

  getPaymentType(): FormControl {
    return this.filtersForm.get('paymentType') as FormControl;
  }

  getDateAfter(): FormControl {
    return this.filtersForm.get('date_after') as FormControl;
  }

  getDateBefore(): FormControl {
    return this.filtersForm.get('date_before') as FormControl;
  }
}
