<mat-dialog-content class="mat-typography">
  <div class="container">
    <div class="row">
      <div class="col-4">
        <span *ngIf="data.trainingMode">Trening mod (kreirani računi nisu fiskalni računi)</span>
      </div>
      <div class="col-4 text-center">
        <span>ID</span>
      </div>
      <div class="col-4 text-right">
        <span>Iznos</span>
      </div>
    </div>
  </div>
  <mat-list role="list">
    <mat-list-item *ngFor="let order of data.orders" role="listitem">
      <div class="container">
        <div class="row">
          <div class="col-4 d-flex justify-content-around align-items-center">
            <ng-container *ngIf="order.fiscalisationStatus == 'not_started'">
              <mat-icon>hourglass_empty</mat-icon>
            </ng-container>
            <ng-container *ngIf="order.fiscalisationStatus == 'in_progress'">
              <mat-spinner [diameter]="25"></mat-spinner>
              <span>Fiskalizovanje...</span>
            </ng-container>
            <ng-container *ngIf="order.fiscalisationStatus == 'error'">
            </ng-container>
            <ng-container *ngIf="order.fiscalisationStatus == 'done'">
              <mat-icon>check_circle_outline</mat-icon>
            </ng-container>
          </div>
          <div class="col-4 text-center">
            <span>{{ order.id }}</span>
          </div>
          <div class="col-4 text-right">
            <span>{{ order.total_amount }}</span>
          </div>
        </div>
      </div>
      <mat-divider></mat-divider>
    </mat-list-item>
  </mat-list>
</mat-dialog-content>
<mat-dialog-actions align="end">
  <button
    mat-button
    color="primary"
    [disabled]="fiscalisationStatus != 'not_started'"
    (click)="startFiscalisation()"
  >
    Fiskalizuj
  </button>
  <button
    mat-button
    mat-dialog-close
    [disabled]="fiscalisationStatus == 'in_progress'"
  >
    Zatvori
  </button>
</mat-dialog-actions>
