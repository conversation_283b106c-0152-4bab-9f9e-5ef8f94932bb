<div class="container-fluid p-lg-5 p-md-3">
  <app-page-title text="Merchant Pro"></app-page-title>
  <div class="row">
    <app-orders-header
      (actionEmitter)="handleActions($event)"
      (filtersChanged)="filter($event)"
      [refreshTime]="refreshTime"
    >
    </app-orders-header>
  </div>
  <div class="row">
    <div class="col-lg-12" *ngIf="orders$ | async as orders; else load">
      <app-orders-table
        *ngIf="columns$ | async as columns"
        [data]="orders"
        (actionEmitter)="handleActions($event)"
        [systemColumns]="columns"
      >
      </app-orders-table>
    </div>
    <ng-template #load>
      <div class="col-lg-12">
        <app-table-skeleton
          [columns]="[
            'Broj porudžbine',
            'Kupac',
            'Email',
            'Vreme kreiranja',
            'Iznos',
            'Fiskalizacija',
            'Akcije',
            'Expand'
          ]"
          [rowsNumber]="perPage"
        >
        </app-table-skeleton>
      </div>
    </ng-template>
    <div class="col-lg-12">
      <mat-paginator
        [length]="total"
        [pageIndex]="pageIndex"
        [pageSizeOptions]="pageSizeOptions"
        [pageSize]="perPage"
        (page)="handlePageEvent($event)"
        showFirstLastButtons="true"
      >
      </mat-paginator>
    </div>
  </div>
</div>
