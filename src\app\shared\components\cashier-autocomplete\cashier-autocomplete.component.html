<app-fiscomm-input
  [placeholder]="placeholder"
  [control]="control"
  [autoComplete]="auto"
  (click)="showAllSuggestions()">
</app-fiscomm-input>

<mat-autocomplete #auto="matAutocomplete" [displayWith]="displayFn" (optionSelected)="optionSelected($event)">
  <mat-option *ngIf="isLoading">
    <mat-spinner diameter="20"></mat-spinner>
  </mat-option>
  <ng-container *ngIf="!isLoading">
    <mat-option
      *ngFor="let option of filteredCashiers"
      [value]="option">
      {{ option.cashierCode ? option.cashierCode + ' - ' + option.name : option.name }}
    </mat-option>
  </ng-container>
</mat-autocomplete>
