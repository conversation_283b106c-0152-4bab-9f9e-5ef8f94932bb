{"name": "fiscomm-e-invoices-client", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "start:prod": "ng serve --configuration production", "build": "ng build", "build:prod": "ng build --configuration production", "deploy": "npm run build:prod && firebase deploy --only hosting", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^14.2.0", "@angular/cdk": "^14.2.7", "@angular/common": "^14.2.0", "@angular/compiler": "^14.2.0", "@angular/core": "^14.2.0", "@angular/fire": "^7.5.0", "@angular/forms": "^14.2.0", "@angular/localize": "^14.2.0", "@angular/material": "^14.2.7", "@angular/platform-browser": "^14.2.0", "@angular/platform-browser-dynamic": "^14.2.0", "@angular/router": "^14.2.0", "@ng-bootstrap/ng-bootstrap": "^13.0.0", "@ngneat/transloco": "^4.3.0", "@popperjs/core": "^2.10.2", "bootstrap": "^5.2.0", "firebase": "^9.23.0", "mdb-angular-ui-kit": "^3.0.1", "rxjs": "~7.5.0", "tslib": "^2.3.0", "uuid": "^9.0.0", "xlsx": "^0.18.5", "zone.js": "~0.11.4"}, "devDependencies": {"@angular-devkit/build-angular": "^14.2.3", "@angular/cli": "~14.2.3", "@angular/compiler-cli": "^14.2.0", "@types/jasmine": "~4.0.0", "@types/uuid": "^9.0.1", "jasmine-core": "~4.3.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.0.0", "typescript": "~4.7.2"}}