import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { debounceTime, tap } from 'rxjs';

@Component({
  selector: 'app-detail-filters',
  templateUrl: './detail-filters.component.html',
  styleUrls: ['./detail-filters.component.scss']
})
export class DetailFiltersComponent implements OnInit {

  constructor(private fb:FormBuilder) { }

  @Output() filterEmitter: EventEmitter<any> = new EventEmitter();

  searchForm:FormGroup = new FormGroup({});

  ngOnInit(): void {
    this.initalizeForm();
    this.emitFilters();
  }

  private emitFilters() {
    this.searchForm.valueChanges.pipe(
      debounceTime(300),
    ).subscribe(value => this.filterEmitter.emit(value));
  }

  private initalizeForm() {
    this.searchForm = this.fb.group({
      searchName: '',
      searchPrice: '',
      searchQuantity:''
    })
  }

  getControl(name: string) {
    return this.searchForm.get(name) as FormControl;
  }

}
