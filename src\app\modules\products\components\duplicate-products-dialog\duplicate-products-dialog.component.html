<div class="duplicate-products-dialog" *transloco="let t">
  <h2 mat-dialog-title>{{ t('items.duplicate_products_title') }}</h2>

  <mat-dialog-content>
    <p class="mb-4">{{ t('items.duplicate_products_description', { total: data.totalProducts }) }}</p>

    <div class="table-container">
      <table mat-table [dataSource]="duplicateProducts" class="mat-elevation-z2">
        <!-- Checkbox Column -->
        <ng-container matColumnDef="select">
          <th mat-header-cell *matHeaderCellDef>
            <mat-checkbox
              (change)="toggleSelectAll()"
              [checked]="selectAll"
              [indeterminate]="selectedProducts.size > 0 && selectedProducts.size < duplicateProducts.length">
            </mat-checkbox>
          </th>
          <td mat-cell *matCellDef="let product; let i = index">
            <mat-checkbox
              (change)="toggleProduct(i)"
              [checked]="selectedProducts.has(i)">
            </mat-checkbox>
          </td>
        </ng-container>

        <!-- New Product Column -->
        <ng-container matColumnDef="newProduct">
          <th mat-header-cell *matHeaderCellDef>{{ t('items.new_product') }}</th>
          <td mat-cell *matCellDef="let product">
            <div class="product-info">
              <div><strong>{{ t('items.name') }}:</strong> {{ product.name }}</div>
              <div><strong>{{ t('price') }} (RSD):</strong> {{ product.price | rsdCurrency:'RSD ':2:'.':',':3 }}</div>
              <div *ngIf="product.gtin"><strong>GTIN:</strong> {{ product.gtin }}</div>
              <div *ngIf="product.productCode"><strong>{{ t('items.product_code') }}:</strong> {{ product.productCode }}</div>

              <!-- Currency prices for new product -->
              <div *ngIf="hasCurrencyPrices(product)" class="currency-prices">
                <div class="currency-prices-title"><strong>{{ t('items.additional_prices') }}:</strong></div>
                <div class="currency-tags">
                  <ng-container *ngFor="let currency of supportedCurrencies">
                    <span *ngIf="product.prices && product.prices[currency]" class="currency-tag has-value">
                      {{ currency }}: {{ getCurrencyPrice(product, currency) }}
                    </span>
                  </ng-container>
                </div>
              </div>
            </div>
          </td>
        </ng-container>

        <!-- Existing Product Column -->
        <ng-container matColumnDef="existingProduct">
          <th mat-header-cell *matHeaderCellDef>{{ t('items.existing_product') }}</th>
          <td mat-cell *matCellDef="let product">
            <div class="product-info">
              <div><strong>{{ t('items.name') }}:</strong> {{ product.existingProduct.name }}</div>
              <div><strong>{{ t('price') }} (RSD):</strong> {{ product.existingProduct.price | rsdCurrency:'RSD ':2:'.':',':3 }}</div>
              <div *ngIf="product.existingProduct.gtin"><strong>GTIN:</strong> {{ product.existingProduct.gtin }}</div>
              <div *ngIf="product.existingProduct.productCode"><strong>{{ t('items.product_code') }}:</strong> {{ product.existingProduct.productCode }}</div>

              <!-- Currency prices for existing product -->
              <div *ngIf="hasCurrencyPrices(product.existingProduct)" class="currency-prices">
                <div class="currency-prices-title"><strong>{{ t('items.additional_prices') }}:</strong></div>
                <div class="currency-tags">
                  <ng-container *ngFor="let currency of supportedCurrencies">
                    <span *ngIf="product.existingProduct.prices && product.existingProduct.prices[currency]" class="currency-tag has-value">
                      {{ currency }}: {{ getCurrencyPrice(product.existingProduct, currency) }}
                    </span>
                  </ng-container>
                </div>
              </div>
            </div>
          </td>
        </ng-container>

        <!-- Conflict Type Column -->
        <ng-container matColumnDef="conflictType">
          <th mat-header-cell *matHeaderCellDef>{{ t('items.conflict_type') }}</th>
          <td mat-cell *matCellDef="let product">
            {{ getConflictTypeLabel(product.conflictType) }}
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="['select', 'newProduct', 'existingProduct', 'conflictType']"></tr>
        <tr mat-row *matRowDef="let row; columns: ['select', 'newProduct', 'existingProduct', 'conflictType'];"></tr>
      </table>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions align="end" class="mb-1">
    <button mat-button (click)="cancel()">{{ t('cancel') }}</button>
    <button mat-raised-button color="primary" (click)="confirm()" >
      {{ t('confirm') }}
    </button>
  </mat-dialog-actions>
</div>
