import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { COMMA, ENTER, SPACE } from '@angular/cdk/keycodes';
import { MatChipInputEvent } from '@angular/material/chips';
import { BulkActionsService } from 'src/app/shared/services/bulk-actions.service';

@Component({
  selector: 'app-orders-header',
  templateUrl: './orders-header.component.html',
  styleUrls: ['./orders-header.component.scss']
})
export class OrdersHeaderComponent implements OnInit {

  @Output() actionEmitter: EventEmitter<any> = new EventEmitter();
  @Input() refreshTime: Date = new Date();
  @Output() filtersChanged: EventEmitter<any> = new EventEmitter();

  previousLength: number = 0;

  invoiceNumberFilters: any[] = [];
  choosenFilters = {
    invoiceType: undefined,
    transactionType: undefined,
    paymentType: undefined,
    finalized:undefined
  }

  menuItems = [
    { 'label': 'Fiskalizuj', 'action':()=> this.actionEmitter.emit({ action:"bulk-fiscalize"}),'enabled':false },
    { 'label': 'Refundiraj', 'action':()=>this.actionEmitter.emit({ action:"bulk-refund"}),'enabled':false }
  ]


  readonly separatorKeysCodes = [ENTER, COMMA,SPACE] as const;

  constructor(public bulkActionService:BulkActionsService) { }

  ngOnInit(): void {
    this.bulkActionService.hasNotFiscalizedElement$().subscribe(hasNotFiscalizedElement => 
      this.menuItems[0].enabled = hasNotFiscalizedElement);
    this.bulkActionService.hasFiscalizedElement$().subscribe(hasFiscalizedElement =>
      this.menuItems[1].enabled = hasFiscalizedElement);
  }


  removeInvoiceNumberFilter(invoiceNumber: any): void {
    const index = this.invoiceNumberFilters.indexOf(invoiceNumber);
    if (index >= 0) 
      this.invoiceNumberFilters.splice(index, 1);
    if(this.isLenghtChanged())
      this.filtersChanged.emit(this.invoiceNumberFilters)
  }

  addInvoiceNumberFilter(event: MatChipInputEvent): void {
    const value = (event.value || '').trim();
    if (value) 
      this.invoiceNumberFilters.push(value);
    event.chipInput!.clear();
    if(this.isLenghtChanged())
      this.filtersChanged.emit(this.invoiceNumberFilters)
  }

  isLenghtChanged() {
    if (this.invoiceNumberFilters.length != this.previousLength) {
      this.previousLength = this.invoiceNumberFilters.length;
      return true;
    }
    return false;
  }

}
