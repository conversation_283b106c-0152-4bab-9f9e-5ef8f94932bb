import {
  AbstractControl,
  ValidatorFn,
  ValidationErrors,
  FormArray,
  FormGroup,
  Validators,
} from '@angular/forms';

export class CreateReceiptValidators {
  static atLeastOneRowRequired: ValidatorFn = (
    control: AbstractControl
  ): ValidationErrors | null => {
    const formArray = control as FormArray;
    if (control instanceof FormArray && formArray.length === 0) {
      return { atLeastOneRowRequired: true };
    }
    return null;
  };

  static validateAmountExceptAdvance: ValidatorFn = (
    control: AbstractControl
  ): ValidationErrors | null => {
    if (!(control instanceof FormGroup)) {
      return null;
    }
    const group = control as FormGroup;
    const invoiceType = group.get('invoiceType')?.value;
    const paymentArray = group.get('payment') as FormArray;

    const payments = paymentArray.controls;
    for (const payment of payments) {
      const amount = payment.get('amount')?.value;
      if (invoiceType === 'Advance') {
        if (amount < 0) {
          return { validateAmountExceptAdvance: true };
        }
      } else if (amount < 1) {
        return { validateAmountExceptAdvance: true };
      }
    }

    return null;
  };

  static referentDocumentNumberRequired: ValidatorFn = (
    control: AbstractControl
  ): ValidationErrors | null => {
    if (!(control instanceof FormGroup)) return null;
    const group = control as FormGroup;
    const transactionType = group.get('transactionType')?.value;
    const refDocNumber = group.get('referentDocumentNumber')?.value;
    const isEmpty =
      refDocNumber == null || refDocNumber == undefined || refDocNumber == '';
    if (
      (transactionType == 'Refund' || transactionType == 'Finalize') &&
      isEmpty
    )
      return { referentDocumentNumberRequired: true };
    return null;
  };

  static refDocDtRequired: ValidatorFn = (
    control: AbstractControl
  ): ValidationErrors | null => {
    if (!(control instanceof FormGroup)) return null;

    const group = control as FormGroup;
    const transactionType = group.get('transactionType')?.value;
    const refDocNumberDT = group.get('referentDocumentDT')?.value;

    const isEmpty =
      refDocNumberDT == null ||
      refDocNumberDT == undefined ||
      refDocNumberDT == '';

    if (transactionType == 'Finalize' && isEmpty)
      return { refDocDtRequired: true };
    return null;
  };

  static customerIndetificationRequired: ValidatorFn = (
    control: AbstractControl
  ): ValidationErrors | null => {
    if (!(control instanceof FormGroup)) {
      return null;
    }
    const group = control as FormGroup;
    const transactionType = group.get('transactionType')?.value;

    if (transactionType != 'Refund') return null;

    const paymentArray = group.get('payment') as FormArray;
    const payments = paymentArray.controls as FormGroup[];

    const buyerIdControl = group.get('buyerId');
    const buyerIDPrefixControl = group.get('buyerIDPrefix');

    if (buyerIdControl?.value && buyerIDPrefixControl?.value) return null;

    for (const payment of payments) {
      if (payment.controls['paymentType'].value == 'Cash')
        return { customerIndetificationRequired: true };
    }

    return null;
  };

  static buyerIdRequired: ValidatorFn = (
    control: AbstractControl
  ): ValidationErrors | null => {
    if (!(control instanceof FormGroup)) return null;
    const group = control as FormGroup;
    const buyerIdPrefix = group.get('buyerIDPrefix')?.value;
    const buyerIdSufix = group.get('buyerId')?.value;
    if (
      buyerIdPrefix != '' &&
      buyerIdPrefix != undefined &&
      (buyerIdSufix == '' || buyerIdSufix == undefined)
    )
      return { buyerIdRequired: true };
    return null;
  };

  static buyerCostIdRequired: ValidatorFn = (
    control: AbstractControl
  ): ValidationErrors | null => {
    if (!(control instanceof FormGroup)) return null;
    const group = control as FormGroup;
    const buyerCostIdPrefix = group.get('prefixBuyerCostCenterId')?.value;
    const buyerCostIdSufix = group.get('sufixBuyerCostCenterId')?.value;
    if (
      buyerCostIdPrefix != '' &&
      buyerCostIdPrefix != undefined &&
      (buyerCostIdSufix == '' || buyerCostIdSufix == undefined)
    )
      return { buyerCostIdRequired: true };
    return null;
  };

  static requiredMetaFieldsValidator: ValidatorFn = (
    control: AbstractControl
  ): ValidationErrors | null => {
    if (!(control instanceof FormGroup)) {
      return null;
    }

    const group = control as FormGroup;
    const metaFieldsFormArray = group.get('metaFields') as FormArray;

    if (!metaFieldsFormArray || metaFieldsFormArray.length === 0) {
      return null;
    }

    const metaFieldErrors: {[key: string]: string} = {};
    let hasErrors = false;

    metaFieldsFormArray.controls.forEach((control: AbstractControl, index) => {
      const fieldControl = control as FormGroup;
      const key = fieldControl.get('key')?.value;
      const value = fieldControl.get('value')?.value;
      const isRequired = fieldControl.get('isRequired')?.value;

      if (isRequired === true && (!value || value.trim() === '')) {
        metaFieldErrors[key] = 'required';
        hasErrors = true;
      }
    });

    return hasErrors ? { requiredMetaFields: metaFieldErrors } : null;
  };
}
