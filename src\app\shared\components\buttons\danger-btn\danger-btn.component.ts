import { Component, OnInit,Input } from '@angular/core';
import { TooltipPosition } from '@angular/material/tooltip';

@Component({
  selector: 'app-danger-btn',
  templateUrl: './danger-btn.component.html',
  styleUrls: ['./danger-btn.component.scss']
})
export class DangerBtnComponent implements OnInit {

  @Input() text: string = '';
  @Input() disabled: boolean = false;
  @Input() tooltip: string = '';
  @Input() position: TooltipPosition = 'above';
  constructor() { }

  ngOnInit(): void {
  }

}
