<h2 mat-dialog-title>Napravi novi tiket</h2>
<mat-dialog-content
  class="d-flex align-items-center justify-content-center"
  id="newTicketDialog"
>
  <div class="container-fluid">
    <form [formGroup]="createTicketForm">
      <div class="row">
        <h3>Tiket informacije</h3>
        <div class="col-6">
          <app-fiscomm-input
            placeholder="ime"
            [control]="getControl('name')"
            [readOnly]="true"
          ></app-fiscomm-input>
        </div>
        <div class="col-6">
          <app-fiscomm-input
            [control]="getControl('email')"
            type="email"
            placeholder="email"
            [readOnly]="true"
          ></app-fiscomm-input>
        </div>
        <div class="col-6">
          <app-fiscomm-select
            [control]="getControl('category')"
            [options]="[
              { value: '', label: 'Pitanje u vezi sa uslugama' },
              { value: '<PERSON><PERSON><PERSON><PERSON>', label: '<PERSON><PERSON><PERSON>nti' },
              { value: '<PERSON><PERSON>ni', label: '<PERSON><PERSON>ni' }
            ]"
          ></app-fiscomm-select>
        </div>
        <div class="col-6">
          <app-fiscomm-select
            [control]="getControl('priority')"
            [options]="priorityOptions"
          ></app-fiscomm-select>
        </div>
      </div>
      <div class="row">
        <h3>Poruka</h3>
        <div class="col-12">
          <app-fiscomm-input
            [control]="getControl('title')"
            type="text"
            placeholder="Naslov"
          ></app-fiscomm-input>
        </div>
        <div class="col-12">
          <textarea
            placeholder="Poruka"
            rows="4"
            [formControl]="getControl('body')"
          >
          </textarea>
        </div>
      </div>
      <div class="row">
        <!-- NEXT_VERSION -->
        <!-- <h3>Prilozi</h3>
        <div
          class="col-12 d-flex justify-content-between align-items-center mb-2"
          *ngFor="let fileControl of getFilesArray().controls; let i = index"
        >
          <label for="file-upload{{ i }}" class="custom-file-upload">
            Izaberi fajl
          </label>
          <input
            id="file-upload{{ i }}"
            type="file"
            (change)="fileSelected($event, i)"
            accept=" .txt, .jpg, .gif, .jpeg, .png, .rar, .zip, .pdf, .doc, .docx, .tiff, .csr, .xlsx, .xls, .pptx"
          />
          <input
            type="text"
            readOnly="true"
            class="file-text"
            id="file-text{{ i }}"
            [value]="getFileControl(i).value"
          />
          <mat-icon *ngIf="i > 0" (click)="removeFile(i)">delete</mat-icon>
        </div>
        <p class="file-info">
          Dozvoljene ekstenzije: .txt, .jpg, .gif, .jpeg, .png, .rar, .zip,
          .pdf, .doc, .docx, .tiff, .csr, .xlsx, .xls, .pptx (Maksimalna
          velicina fajla: 24MB)
        </p>
        <a
          (click)="$event.preventDefault(); addFile()"
          href=""
          class="d-flex align-items-center mt-2 text-decoration-none"
          ><mat-icon>add_circle</mat-icon> Dodaj jos jedan fajl</a
        > -->
      </div>
    </form>
  </div>
</mat-dialog-content>
<mat-dialog-actions class="buttons d-flex justify-content-between mt-2">
  <app-danger-btn [mat-dialog-close]="false" text="OTKAZI"></app-danger-btn>
  <app-success-btn
    text="POSALJI"
    (click)="createTicket()"
    [disabled]="!createTicketForm.valid"
  ></app-success-btn>
</mat-dialog-actions>
