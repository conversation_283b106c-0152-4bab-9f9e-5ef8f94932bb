import { ComponentFixture, TestBed } from '@angular/core/testing';

import { BulkFiscalizeDialogComponent } from './bulk-fiscalize-dialog.component';

describe('BulkFiscalizeDialogComponent', () => {
  let component: BulkFiscalizeDialogComponent;
  let fixture: ComponentFixture<BulkFiscalizeDialogComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ BulkFiscalizeDialogComponent ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(BulkFiscalizeDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
