import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Routes } from '@angular/router';
import { BillingComponent } from './containers/billing/billing.component';
import { BillingTableComponent } from './components/billing-table/billing-table.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { BilingHeaderComponent } from './components/biling-header/biling-header.component';
import { BillingDetailsDialogComponent } from './components/billing-details-dialog/billing-details-dialog.component';
import { DetailFiltersComponent } from './components/billing-details-dialog/detail-filters/detail-filters.component';
import { PaymentSlipComponent } from './components/payment-slip/payment-slip.component';

const routes: Routes = [
  {
    path:'',
    component: BillingComponent
  },
];

@NgModule({
  declarations: [
    BillingComponent,
    BillingTableComponent,
    BilingHeaderComponent,
    BillingDetailsDialogComponent,
    DetailFiltersComponent,
    PaymentSlipComponent,
  ],
  imports: [
    CommonModule,
    SharedModule,
    RouterModule.forChild(routes)
  ]
})
export class BillingModule { }
