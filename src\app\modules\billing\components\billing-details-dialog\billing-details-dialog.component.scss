.content {
  background-color: white;
  border-radius: 8px;
  margin-inline: 2em;
}

.table-container {
  width: 100%;
  overflow-y: auto;
  overflow-x: auto;
}

.itemsTable {
  border-top: 3px solid #e1e5f2;
  width: 100%;
  margin: 0px !important;
  th {
    font-family: "Inter" !important;
    font-weight: 600;
    padding-top: 8px;
    padding-bottom: 8px;
    padding-left: 20px;
    font-size: 16px;
    color: #022b3a;
    border-bottom: 3px solid #e1e5f2;
    text-wrap: nowrap !important;
  }
  tr:nth-last-of-type(even) {
    background-color: #edf5fd;
  }
  td {
    padding-top: 8px;
    padding-bottom: 8px;
    padding-left: 20px;
    font-size: 15px;
    color: #022b3a;
    text-wrap: nowrap !important;
  }
}

.x-button {
  position: relative;
  top: 0px;
  left: 20px;
  background: transparent;
  border: none;
}

#instructions {
  img {
    width: 100%;
    border: 2px solid #e5e8f3;
  }
  p {
    //color:#8bc6f6;
  }
}

::ng-deep {
  #billingDetails {
    .mat-tab-labels {
      display: flex;
      justify-content: space-around;
    }
    .mat-tab-label {
      font-size: 17px;
      padding: 28px 24px;
      transition: ease-in 0.2s;
      width: 33%;
    }
    .centered-links {
      display: flex;
      justify-content: center;
    }
    .mat-tab-label-active {
      color: #0099b7;
      font-size: 19px;
      opacity: 1;
    }
  }
}

@media only screen and (max-width: 768px) {
  ::ng-deep {
    #billingDetails {
      padding: 0;
      .mat-tab-label {
        font-size: 14px;
        width: 25%;
      }

      .mat-tab-label-active {
        color: #0099b7;
        font-size: 16px;
        opacity: 1;
      }
    }
  }
}

h4 {
  margin-bottom: 3px;
}
