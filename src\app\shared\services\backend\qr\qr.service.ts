import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class QRService {
  constructor(private http: HttpClient) {}

  url: string = 'https://nbs.rs/QRcode/api/qr/v1';
  header = new HttpHeaders().append('Fiscomm-No-Auth', 'true');

  generateQR(data: QrData) {
    return this.http.post(this.url + '/gen', data, {
      headers: this.header,
      responseType: 'blob',
    });
  }
}

export interface QrData {
  K: string;
  V: string;
  C: string;
  R: string;
  N: string;
  I: string;
  P?: string;
  SF: string;
  S?: string;
  RO: string;
}
