import {
  animate,
  state,
  style,
  transition,
  trigger,
} from '@angular/animations';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { TranslocoService, translate } from '@ngneat/transloco';
import { MerchantProService } from 'src/app/shared/services/backend/merchant-pro/merchant-pro.service';
import { Order } from 'src/app/shared/services/backend/merchant-pro/types/orders-response.dto';
import { BulkActionsService } from 'src/app/shared/services/bulk-actions.service';

@Component({
  selector: 'app-orders-table',
  templateUrl: './orders-table.component.html',
  styleUrls: ['./orders-table.component.scss'],
  animations: [
    trigger('detailExpand', [
      state('collapsed', style({ height: '0px', minHeight: '0' })),
      state('expanded', style({ height: '*' })),
      transition(
        'expanded <=> collapsed',
        animate('225ms cubic-bezier(0.4, 0.0, 0.2, 1)')
      ),
    ]),
  ],
})
export class OrdersTableComponent implements OnInit {
  @Output() actionEmitter: EventEmitter<any> = new EventEmitter<any>();

  @Input() data: Order[] = [];
  expandedElement: Order | null = null;
  displayColumns: string[] = [];
  allColumns: TableColumn[] = this.initAllColumns();

  @Input() systemColumns: string[] = [];

  checkboxStates: Map<string, boolean> = new Map();
  isMasterSelected: boolean = true;
  fiscMessage: Map<number, string> = new Map();

  constructor(
    private mpservice: MerchantProService,
    private bulkActionService: BulkActionsService,
    private translocoService: TranslocoService
  ) {}

  ngOnInit(): void {
    this.initalizeCheckboxes();
    this.initalizeColumnsState(this.systemColumns);
    this.filterDisplayColumns(this.systemColumns);
    this.translocoService.langChanges$.subscribe(() => {
      this.allColumns = this.initAllColumns();
      this.initalizeColumnsState(this.systemColumns);
    });
  }

  private initAllColumns() {
    return [
      {
        displayName: translate('orders_page.order_number'),
        key: 'orderID',
        enabled: false,
      },
      {
        displayName: translate('customer'),
        key: 'customerName',
        enabled: false,
      },
      {
        displayName: 'Email',
        key: 'customerEmail',
        enabled: false,
      },
      {
        displayName: translate('time_created'),
        key: 'dateCreated',
        enabled: false,
      },
      {
        displayName: translate('amount'),
        key: 'amount',
        enabled: false,
      },
      {
        displayName: translate('fiscalization'),
        key: 'mpSubstatus',
        enabled: false,
      },
      {
        displayName: 'Pdf Link',
        key: 'invoicePdfUrl',
        enabled: false,
      },
      {
        displayName: 'Link',
        key: 'verificationURL',
        enabled: false,
      },
    ];
  }

  private initalizeCheckboxes() {
    this.data.forEach((order) => {
      this.checkboxStates.set(order.id, false);
    });
  }

  private initalizeColumnsState(systemColumns: string[]) {
    this.allColumns.forEach((col) => {
      col.enabled = systemColumns.includes(col.key);
    });
  }

  private filterDisplayColumns(systemColumns: string[]) {
    this.displayColumns = [];
    this.displayColumns.push('Checkbox');
    let tmp = this.allColumns
      .filter((column) => column.enabled && systemColumns.includes(column.key))
      .map((column) => column.key);
    this.displayColumns.push(...tmp);
    this.displayColumns.push('Akcije', 'Expand');
  }

  toggleColumn(column: TableColumn) {
    column.enabled = !column.enabled;
    this.updateSystemColumns(column);
    this.filterDisplayColumns(this.systemColumns);
  }

  private updateSystemColumns(column: TableColumn) {
    column.enabled
      ? this.systemColumns.push(column.key)
      : (this.systemColumns = this.systemColumns.filter(
          (key) => key !== column.key
        ));
    this.mpservice.updateColumnSettings(this.systemColumns).subscribe();
  }

  getCheckbox(id: any) {
    return this.checkboxStates.get(id);
  }

  checkUncheckAll() {
    this.bulkActionService.emptyAllElements();
    this.isMasterSelected = !this.isMasterSelected;
    this.checkboxStates.forEach((value, key) =>
      this.checkboxStates.set(key, this.isMasterSelected)
    );
    const checkboxes = document.querySelectorAll('.checkbox');
    checkboxes.forEach((checkbox) => {
      checkbox.dispatchEvent(new Event('change'));
    });
  }

  toggleElement(element: any) {
    let currVal = this.getCheckbox(element.id);
    this.checkboxStates.set(element.id, !currVal);
    if (this.getCheckbox(element.id))
      this.bulkActionService.addSelectedElement(element);
    else this.bulkActionService.removeSelectedElement(element);
  }

  getOrderPdfLink(order: Order) {
    const receipt = this.getOrderReceipt(order);
    return receipt?.invoice_pdf_url;
  }

  getOrderVerLink(order: Order) {
    const receipt = this.getOrderReceipt(order);
    return receipt?.verification_url;
  }

  private getOrderReceipt(order: Order) {
    return order.receipts?.find(
      (receipt) =>
        receipt.invoice_type.invoice_type == 'Normal' &&
        receipt.transaction_type.transaction_type == 'Sale'
    );
  }

  isElementFiscalised(order: Order) {
    return order.payment_substatus_text == 'Fiskalizovan';
  }

  emitAction(key: any) {
    this.actionEmitter.emit(key);
  }

  allowFiscalization(element: any) {
    if (!this.isValidVAT(element.billing_company_vat)) {
      this.fiscMessage.set(element.id, 'PIB firme mora da sadrži samo brojeve');
      return false;
    }

    if (!this.isValidPaymentStatus(element.payment_status)) {
      this.fiscMessage.set(
        element.id,
        'Porudžbina nije u odgovarajućem statusu za fiskalizaciju'
      );
      return false;
    }

    if (this.isAlreadyFiscalized(element.receipts)) {
      this.fiscMessage.set(
        element.id,
        'Porudžbina je već fiskalizovana i nije moguće kreirati novi račun'
      );
      return false;
    }

    if (!this.isSubStatusValid(element.payment_substatus_text)) {
      this.fiscMessage.set(
        element.id,
        'Porudžbina nije u statusu "Potrebna fiskalizacija"'
      );
    }

    return true;
  }

  private isValidVAT(vat: string): boolean {
    return vat == null || RegExp('^\\d+$').test(vat);
  }

  private isValidPaymentStatus(status: string): boolean {
    const validStatuses = ['completed', 'paid', 'awaiting'];
    return validStatuses.includes(status);
  }

  private isAlreadyFiscalized(receipts: any[]): boolean {
    return receipts && receipts.length > 0;
  }

  private isSubStatusValid(subStatus: string): boolean {
    return subStatus == 'Potrebna fiskalizacija';
  }
}

interface TableColumn {
  displayName: string;
  key: string;
  enabled: boolean;
}
