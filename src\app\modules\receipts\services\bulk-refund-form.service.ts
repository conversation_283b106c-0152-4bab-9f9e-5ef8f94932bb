import { Injectable } from '@angular/core';
import { FormArray, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { Receipts } from 'src/app/shared/services/backend/receipts/types/receipts-response.dto';

@Injectable({
  providedIn: 'root'
})
export class BulkRefundFormService {

  private refundForm: FormGroup;

  constructor(private fb: FormBuilder) {
    this.refundForm = fb.group({
      identityArray:fb.array([])
    });
  }
  
  addBuyerRow(receipt: Receipts) {
    let parts = ['', ''];
    if (receipt.buyer_id)
      parts = receipt.buyer_id.split(':');

    let row;
    if (!this.hasCashPayment(receipt)) {
      row=this.fb.group({
        buyerIDPrefix: [parts[0]],
        buyerIDSufix: [parts[1]],
      });
    }
    else {
      row=this.fb.group({
        buyerIDPrefix: [parts[0],Validators.required],
        buyerIDSufix: [parts[1],Validators.required],
      });
    }
    this.getRefundFormArray().push(row);
  }

  getRefundForm() {
    return this.refundForm as FormGroup;
  }

  getRefundFormArray() {
    return this.refundForm.get('identityArray') as FormArray;
  }

  private hasCashPayment(receipt: Receipts) {
    return receipt.payments.find((payment: any) => payment.payment_type.payment_type == 'Cash') != undefined ? true : false;
  }

  getBuyerIDPrefix(index: number) {
    return this.getRefundFormArray().controls[index].get('buyerIDPrefix') as FormControl;
  }

  getBuyerIDSufix(index: number) {
    return this.getRefundFormArray().controls[index].get('buyerIDSufix') as FormControl;
  }
}
