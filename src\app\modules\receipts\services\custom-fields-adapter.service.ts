import { Injectable } from '@angular/core';
import { Observable, map } from 'rxjs';
import { Customer, MetaFieldsDto } from 'src/app/shared/services/backend/receipts/types/receipts-response.dto';
import { CustomField } from 'src/app/models/custom-field.model';
import { CustomFieldService } from 'src/app/shared/services/firestore/custom-field.service';

@Injectable({
  providedIn: 'root'
})
export class CustomFieldsAdapterService {

  constructor(private customFieldService: CustomFieldService) { }

  /**
   * Gets active custom fields and converts them to MetaFieldsDto format required by the receipt dialog
   */
  getMetaFields(): Observable<MetaFieldsDto[]> {
    return this.customFieldService.getActiveCustomFields().pipe(
      map(customFields => this.convertToMetaFieldsDto(customFields))
    );
  }

  /**
   * Converts CustomField[] to MetaFieldsDto[] format
   */
  private convertToMetaFieldsDto(customFields: CustomField[]): MetaFieldsDto[] {
    return customFields.map(field => {
      // Convert possible values based on field type
      let values: any[] = [];

      if (field.fieldType === 'select' && field.possibleValues) {
        // For select fields, include all possible values
        values = field.possibleValues;
      }

      return {
        _id: field.id || '',
        uid: field.uid || '',
        key: field.id || '',
        metaFieldType: field.fieldType,
        enabled: field.isActive,
        values: values,
        name: field.name,
        isRequired: field.isRequired
      };
    });
  }
}
