import { Component } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';
import { TranslocoService } from '@ngneat/transloco';
import { CURRENCY_ORDER } from 'src/app/shared/constants/available_currencies.const';

@Component({
  selector: 'app-import-template-dialog',
  templateUrl: './import-template-dialog.component.html',
  styleUrls: ['./import-template-dialog.component.scss']
})
export class ImportTemplateDialogComponent {
  displayedColumns: string[] = ['name', 'description'];
  columns: { name: string; description: string }[] = [];
  supportedCurrencies: string[] = CURRENCY_ORDER;

  constructor(
    public dialogRef: MatDialogRef<ImportTemplateDialogComponent>,
    private translocoService: TranslocoService
  ) {
    this.columns = [
      {
        name: 'name',
        description: this.translocoService.translate('items.import_template_columns.name')
      },
      {
        name: 'price',
        description: this.translocoService.translate('items.import_template_columns.price')
      },
      {
        name: 'gtin',
        description: this.translocoService.translate('items.import_template_columns.gtin')
      },
      {
        name: 'productCode',
        description: this.translocoService.translate('items.import_template_columns.productCode')
      }
    ];

    CURRENCY_ORDER.forEach(currency => {
      if (currency !== 'RSD') {
        this.columns.push({
          name: `price_${currency}`,
          description: this.translocoService.translate('items.import_template_columns.price_currency', { currency })
        });
      }
    });
  }

  onClose(): void {
    this.dialogRef.close();
  }
}
