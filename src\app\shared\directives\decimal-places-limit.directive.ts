import { Directive, HostListener, ElementRef, Renderer2, Input } from '@angular/core';
import { NgControl } from '@angular/forms';

@Directive({
  selector: '[appDecimalPlacesLimit]'
})
export class DecimalPlacesLimitDirective {
  @Input('appDecimalPlacesLimit') decimalPlaces: number = 2;

  constructor(
    private el: ElementRef,
    private renderer: Renderer2,
    private ngControl: NgControl
  ) { }

  @HostListener('input', ['$event.target.value'])
  onInput(value: string): void {
    const parts = value.split('.');
    if (parts[1] && parts[1].length > this.decimalPlaces) {
      const truncatedValue = `${parts[0]}.${parts[1].substr(0, this.decimalPlaces)}`;
      this.renderer.setProperty(this.el.nativeElement, 'value', truncatedValue);
      this.ngControl?.control?.setValue(truncatedValue);
    }
  }
}
