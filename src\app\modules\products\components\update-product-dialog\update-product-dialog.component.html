<ng-container *transloco="let t">

    <h2 mat-dialog-title >{{t('items.new')}} </h2>
    <mat-dialog-content class="d-flex align-items-center justify-content-center" >
        <form>
            <app-fiscomm-input placeholder="{{t('items.input.name')}}" [control]="name"></app-fiscomm-input>
            <app-fiscomm-input placeholder="{{t('items.input.price')}}" type="number" [control]="price"></app-fiscomm-input>
            <app-fiscomm-input placeholder="GTIN" [control]="gtin"></app-fiscomm-input>
            <app-fiscomm-input placeholder="Šifra proizvoda" [control]="productCode"></app-fiscomm-input>

            <!-- Tax label dropdown -->
            <app-fiscomm-select
                [control]="taxLabel"
                [options]="taxRateOptions"
                placeholder="{{t('tax_type')}}"
                [compareWith]="compareTaxLabels"
            ></app-fiscomm-select>

            <div class="additional-currencies">
                <h3>{{t('items.additional_prices')}}</h3>
                <app-currency-price-input
                    [existingPrices]="prices"
                    (pricesChanged)="onPricesChanged($event)">
                </app-currency-price-input>
            </div>
        </form>
    </mat-dialog-content>
    <mat-dialog-actions class="buttons mt-4">
        <app-success-btn [mat-dialog-close]="true" [disabled]="!this.name.valid || !this.price.valid"
        (click)="updateProduct()" text="{{t('confirm')}}" class="me-5">
        </app-success-btn>
        <app-danger-btn [mat-dialog-close]="false" text="{{t('give_up')}}"></app-danger-btn>
    </mat-dialog-actions>

</ng-container>

