import { Injectable } from '@angular/core';
import {
  HttpRequest,
  HttpHandler,
  HttpEvent,
  HttpInterceptor,
  HttpErrorResponse,
  HttpResponse,
} from '@angular/common/http';
import { Observable, catchError, tap, throwError } from 'rxjs';
import { MatSnackBar } from '@angular/material/snack-bar';
import { ErrorSnackbarMessageComponent } from 'src/app/shared/components/error-snackbar-message/error-snackbar-message.component';

@Injectable()
export class ResponseInterceptor implements HttpInterceptor {
  constructor(private snackBar: MatSnackBar) {}

  intercept(req: HttpRequest<any>, next: HttpHandler) {
    if (req.headers.get('intercept-msg')) {
      return next.handle(req).pipe(
        tap((event) => {
          if (
            event instanceof HttpResponse &&
            (event.status === 200 || event.status === 201)
          ) {
            if (event.body?.statusCode == 400) {
              this.errorMessage(event.body.requestId);
              throwError(() => new Error(event.body.message));
            } else {
              let msg = decodeURIComponent(
                req.headers.get('intercept-msg') as string
              );
              this.snackBar.open(msg, 'Zatvori', {
                duration: 3000,
                verticalPosition: 'top',
                horizontalPosition: 'right',
                panelClass: 'good-toast',
              });
            }
          }
        }),
        catchError((error: HttpErrorResponse) => {
          if (error.status > 210) this.errorMessage(error.message);
          return throwError(() => new Error(error.message));
        })
      );
    } else return next.handle(req);
  }

  private errorMessage(requestId: string) {
    this.snackBar.openFromComponent(ErrorSnackbarMessageComponent, {
      data: {
        message:
          'Desila se nepoznata greska, molimo pokušajte ponovo. Ako se problem nastavi, napravite tiket sa kodom ' +
          requestId +
          '.',
        requestId: requestId,
      },
      duration: 7000,
      verticalPosition: 'top',
      horizontalPosition: 'right',
      panelClass: 'bad-toast',
    });
  }
}
