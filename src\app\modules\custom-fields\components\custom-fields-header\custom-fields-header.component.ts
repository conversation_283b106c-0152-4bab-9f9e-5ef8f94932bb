import { Component, EventEmitter, OnInit, Output } from '@angular/core';

@Component({
  selector: 'app-custom-fields-header',
  templateUrl: './custom-fields-header.component.html',
  styleUrls: ['./custom-fields-header.component.scss']
})
export class CustomFieldsHeaderComponent implements OnInit {
  @Output() actionEmitter = new EventEmitter<any>();

  constructor() { }

  ngOnInit(): void {
  }

  onAddClick(): void {
    this.actionEmitter.emit({action: 'add'});
  }

  onDeleteAllClick(): void {
    this.actionEmitter.emit({ action: 'deleteAll' });
  }
}
