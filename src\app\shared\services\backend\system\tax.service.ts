import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, map, mergeMap, shareReplay, tap } from 'rxjs';
import { environment } from 'src/environments/environment';
import { stringify } from 'uuid';
import { TaxRatesDto } from '../receipts/types/receipts-response.dto';

@Injectable({
  providedIn: 'root',
})
export class TaxService {
  taxRates$?: Observable<any>;
  taxLabelsExtracted: any[] = [];

  baseURL: string = environment.mainFiscomm.baseUrl;
  constructor(private http: HttpClient) {}

  getTaxRates$() {
    if (!this.taxRates$) {
      this.taxRates$ = this.http
        .get<TaxRatesDto>(this.baseURL + '/system/tax-rates')
        .pipe(
          shareReplay({ bufferSize: 1, refCount: true }),
          map((response) => {
            const taxRates = response.currentTaxRates.taxCategories.flatMap(
              (taxGroup) => taxGroup.taxRates
            );
            return taxRates.map((el) => ({
              value: { label: el.label, rate: el.rate },
              label: this.makeTaxLabel(el.label, el.rate),
            }));
          })
        );
    }
    return this.taxRates$;
  }

  private makeTaxLabel(label: string, rate: number): string {
    return `${label} (${rate}%)`;
  }
}
