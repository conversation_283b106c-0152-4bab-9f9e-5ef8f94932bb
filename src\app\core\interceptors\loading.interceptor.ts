import { Injectable } from '@angular/core';
import {
  <PERSON>ttp<PERSON><PERSON><PERSON>,
  <PERSON>ttpH<PERSON><PERSON>,
  HttpEvent,
  HttpInterceptor
} from '@angular/common/http';
import { Observable, finalize } from 'rxjs';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { DialogService } from 'src/app/shared/dialogs/dialog.service';


@Injectable()
export class LoadingInterceptor implements HttpInterceptor {

  private requestsCount = 0;
  dialogRef?: MatDialogRef<any>;

  constructor(private dialogService:DialogService) {}

  intercept(request: HttpRequest<unknown>, next: HttpHandler): Observable<HttpEvent<unknown>> {
    if (!request.headers.has('Fiscomm-No-Loading')) 
      this.showLoader();
    return next.handle(request).pipe(
      finalize(() => {
        if (!request.headers.has('Fiscomm-No-Loading'))
          this.hideLoader();     
      })
    );
  }

  private showLoader() {
    this.requestsCount++;
    if (this.requestsCount == 1)
      this.dialogRef = this.dialogService.openLoadingDialog();    
  }

  private hideLoader() {
    this.requestsCount--;
    if (this.requestsCount == 0)
      this.dialogRef?.close();
  }

  
}
