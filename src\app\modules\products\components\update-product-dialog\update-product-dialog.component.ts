import { Component, Inject, OnInit } from '@angular/core';
import { ProductsService } from 'src/app/shared/services/backend/products/products.service';
import { FormControl, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
import { Product } from 'src/app/shared/models/product.model';
import { TaxService } from 'src/app/shared/services/backend/system/tax.service';

@Component({
  selector: 'app-update-product-dialog',
  templateUrl: './update-product-dialog.component.html',
  styleUrls: ['./update-product-dialog.component.scss']
})
export class UpdateProductDialogComponent implements OnInit {

  product = this.data;
  constructor(
    @Inject(MAT_DIALOG_DATA) public data: Product,
    private productService: ProductsService,
    private taxService: TaxService
  ) {
    this.name.setValue(data.name);
    this.price.setValue(data.price);
    this.gtin.setValue(data.gtin || '');
    this.productCode.setValue(data.productCode || '');
    this.taxLabel.setValue(data.taxLabel || {
      label: '',
      rate: 0
    });
    // Initialize prices if they exist
    if (data.prices) {
      this.prices = { ...data.prices };
    }

    console.log(data);
  }

  name: FormControl = new FormControl('', Validators.required);
  price: FormControl = new FormControl('', Validators.required);
  gtin: FormControl = new FormControl('', [Validators.pattern('^[0-9]{8,13}$')]);
  productCode: FormControl = new FormControl('');
  taxLabel: FormControl = new FormControl('');

  // Store multi-currency prices
  prices: { [currencyCode: string]: number } = {};

  // Tax rate options for the dropdown
  taxRateOptions: any[] = [];

  ngOnInit(): void {
    // Get tax rate options
    this.taxService.getTaxRates$().subscribe(taxRates => {
      console.log(taxRates);
      this.taxRateOptions = taxRates;
    });
  }

  updateProduct() {
    const updatedProduct: Product = {
      _id: this.product._id,
      name: this.name.value,
      price: this.price.value,
      gtin: this.gtin.value,
      productCode: this.productCode.value,
      taxLabel: this.taxLabel.value,
      prices: this.prices
    };

    this.productService.updateProduct(updatedProduct).subscribe();
  }

  onPricesChanged(prices: { [currencyCode: string]: number }): void {
    this.prices = prices;
  }

  compareTaxLabels(option: any, value: any): boolean {
    console.log(option, value);
    return option.label === value.label && option.rate === value.rate;
  }
}
