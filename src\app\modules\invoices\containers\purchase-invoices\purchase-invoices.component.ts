import { Component, OnInit } from '@angular/core';
import { EInvoiceService } from 'src/app/shared/services/eInvoices/e-invoice.service';

@Component({
  selector: 'app-purchase-invoices',
  templateUrl: './purchase-invoices.component.html',
  styleUrls: ['./purchase-invoices.component.scss'],
})
export class PurchaseInvoicesComponent implements OnInit {
  columns = [
    'checkbox',
    'Broj fakture',
    'Tip fakture',
    '<PERSON>li<PERSON>nt',
    '<PERSON>z<PERSON>',
    'Datum fakture',
    'Datum slanja',
    'Status',
    'Akcija',
    'Details',
  ];

  data: any[] = [];

  searchParams: any = {
    dateFrom: null,
    dateTo: null,
  };

  constructor(private eInvoiceService: EInvoiceService) {}

  ngOnInit(): void {}
}
