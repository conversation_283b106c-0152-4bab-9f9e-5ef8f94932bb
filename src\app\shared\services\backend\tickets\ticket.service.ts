import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from 'src/environments/environment';
import { CreateTicketRequestDto } from './types/tickets-request.type';

@Injectable({
  providedIn: 'root',
})
export class TicketService {
  baseUrl = environment.mainSelfcare.baseUrl;
  headers: any;
  noLoadingHeader = new HttpHeaders().set('Fiscomm-No-Loading', 'true');
  constructor(private http: HttpClient) {}

  getTickets(searchParams?: any) {
    return this.http.get(this.baseUrl + '/tickets', { params: searchParams });
  }

  getTicket(id: number) {
    return this.http.get(this.baseUrl + '/tickets/' + id);
  }

  getAnsweredTicketCount() {
    return this.http.get(this.baseUrl + '/tickets/answered/count', {
      headers: this.noLoadingHeader,
    });
  }

  postTicket(ticket: CreateTicketRequestDto) {
    return this.http.post(this.baseUrl + '/tickets', ticket, {
      headers: {
        ...this.headers,
        'intercept-msg': encodeURIComponent('Tiket uspešno dodat.'),
      },
    });
  }

  updateTicketStatus(id: number, status?: Status, priority?: Priority) {
    let patchValue = {};
    if (status)
      patchValue = {
        status: status,
      };
    if (priority) patchValue = { ...patchValue, priority: priority };
    return this.http.patch(this.baseUrl + '/tickets/' + id, patchValue, {
      headers: {
        ...this.headers,
        'intercept-msg': encodeURIComponent('Status tiketa uspešno izmenjen.'),
      },
    });
  }

  postComment(id: number, comment: string) {
    return this.http.post(
      this.baseUrl + `/tickets/${id}/comments`,
      {
        body: comment,
      },
      {
        headers: {
          ...this.headers,
          'intercept-msg': encodeURIComponent('Komentar uspešno dodat.'),
        },
      }
    );
  }
}

enum Status {
  NEW = 1,
  OPEN = 2,
  PENDING = 3,
  SOLVED = 4,
  CLOSED = 5,
  MERGED = 6,
  SPAM = 7,
}

enum Priority {
  LOW = 1,
  NORMAL = 2,
  HIGH = 3,
  BLOCKER = 4,
}
