import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, of, tap, map, switchMap, from, take, throwError } from 'rxjs';
import { CustomField } from '../../../models/custom-field.model';
import { AngularFirestore } from '@angular/fire/compat/firestore';
import { AuthService } from '../../../core/services/auth.service';

@Injectable({
  providedIn: 'root'
})
export class CustomFieldService {
  private customFieldsSubject = new BehaviorSubject<CustomField[]>([]);
  public customFields$ = this.customFieldsSubject.asObservable();
  private readonly COLLECTION_NAME = 'metaFields';
  private _isAddingField = false; // Flag to prevent recursive calls

  constructor(
    private firestore: AngularFirestore,
    private authService: AuthService
  ) {

  }

  getCustomFields(): Observable<CustomField[]> {
    return this.authService.getUserId().pipe(
      switchMap(uid => {
        if (!uid) return of([]);
        return this.firestore.collection<CustomField>(this.COLLECTION_NAME,
          ref => ref.where('uid', '==', uid).orderBy('order', 'asc')
        ).snapshotChanges().pipe(
          map(actions => actions.map(a => {
            const data = a.payload.doc.data();
            const id = a.payload.doc.id;

            // Convert Firestore Timestamp to JavaScript Date
            let createdAt = undefined;
            if (data.createdAt) {
              // For Firestore Timestamp
              if (data.createdAt.toDate) {
                createdAt = data.createdAt.toDate();
              } else {
                // For regular Date objects
                createdAt = new Date(data.createdAt);
              }
            }

            return {
              ...data,
              id,
              createdAt
            } as CustomField;
          })),
          tap(customFields => this.customFieldsSubject.next(customFields))
        );
      })
    );
  }

  getActiveCustomFields(): Observable<CustomField[]> {
    return this.authService.getUserId().pipe(
      switchMap(uid => {
        if (!uid) return of([]);
        return this.firestore.collection<CustomField>(this.COLLECTION_NAME,
          ref => ref.where('uid', '==', uid)
                   .where('isActive', '==', true)
                   .orderBy('order', 'asc')
        ).snapshotChanges().pipe(
          map(actions => actions.map(a => {
            const data = a.payload.doc.data();
            const id = a.payload.doc.id;

            // Convert Firestore Timestamp to JavaScript Date
            let createdAt = undefined;
            if (data.createdAt) {
              if (data.createdAt.toDate) {
                createdAt = data.createdAt.toDate();
              } else {
                createdAt = new Date(data.createdAt);
              }
            }

            return {
              ...data,
              id,
              createdAt
            } as CustomField;
          }))
        );
      })
    );
  }

  deleteAllCustomFields(): Observable<void> {
    return this.authService.getUserId().pipe(
      switchMap(uid => {
        if (!uid) return of(void 0);
        return from(this.firestore.collection(this.COLLECTION_NAME).ref.where('uid', '==', uid).get()).pipe(
          map(snapshot => snapshot.docs.forEach(doc => doc.ref.delete()))
        );
      })
    );
  }

  addCustomField(customField: CustomField): Observable<CustomField> {
    // Guard against recursive calls
    if (this._isAddingField) {
      console.warn('Preventing recursive addCustomField call');
      return of(customField);
    }

    this._isAddingField = true;
    console.log('Adding custom field', customField);

    return this.authService.getUserId().pipe(
      switchMap(uid => {
        if (!uid) {
          this._isAddingField = false;
          return of(customField);
        }

        // Get the max order to place new field at the end
        return this.getMaxOrder().pipe(
          take(1), // Important: only take 1 emission to prevent multiple executions
          switchMap(maxOrder => {
            // Add uid, createdAt and order to customField
            let newCustomField: CustomField = {
              ...customField,
              uid,
              createdAt: new Date(),
              order: maxOrder + 1
            };
            // remove any undefined values
            newCustomField = Object.fromEntries(
              Object.entries(newCustomField).filter(([_, value]) => value !== undefined)
            ) as CustomField;


            return from(this.firestore.collection(this.COLLECTION_NAME).add(newCustomField)).pipe(
              map(docRef => {
                this._isAddingField = false; // Reset flag
                return { ...newCustomField, id: docRef.id };
              }),
              tap({
                error: (err) => {
                  console.error('Error adding custom field:', err);
                  this._isAddingField = false; // Reset flag on error
                }
              })
            );
          })
        );
      })
    );
  }

  updateCustomField(customField: CustomField): Observable<CustomField> {
    if (!customField.id) {
      return of(customField);
    }

    // Create a copy without the id field (as it's not part of the document data)
    let { id, ...customFieldData } = customField;

    customFieldData = Object.fromEntries(
      Object.entries(customFieldData).filter(([_, value]) => value !== undefined)
    ) as CustomField;

    return from(this.firestore.collection(this.COLLECTION_NAME).doc(id).update(customFieldData)).pipe(
      map(() => customField)
    );
  }

  deleteCustomField(id: string): Observable<void> {
    // First check if the field has been used in receipts
    return from(this.firestore.collection(this.COLLECTION_NAME).doc(id).get()).pipe(
      switchMap(doc => {
        if (!doc.exists) {
          return throwError(() => new Error('Field not found'));
        }

        const data = doc.data() as CustomField;
        if (data.isReceiptCreated) {
          return throwError(() => new Error('Cannot delete a field that has been used in receipts'));
        }

        // If not used in receipts, proceed with deletion
        return from(this.firestore.collection(this.COLLECTION_NAME).doc(id).delete());
      })
    );
  }

  updateFieldOrder(customFields: CustomField[]): Observable<void> {
    // Create a batch update for multiple documents
    const batch = this.firestore.firestore.batch();

    customFields.forEach((field, index) => {
      if (field.id) {
        const docRef = this.firestore.collection(this.COLLECTION_NAME).doc(field.id).ref;
        batch.update(docRef, { order: index + 1 }); // Ensures order starts from 1
      }
    });

    return from(batch.commit());
  }

  private getMaxOrder(): Observable<number> {
    return this.authService.getUserId().pipe(
      switchMap(uid => {
        if (!uid) return of(0);

        return this.firestore.collection<CustomField>(this.COLLECTION_NAME,
          ref => ref.where('uid', '==', uid)
                   .orderBy('order', 'desc')
                   .limit(1)
        ).get().pipe(
          map(snapshot => {
            if (snapshot.empty) return 0;
            return snapshot.docs[0].data().order || 0;
          })
        );
      })
    );
  }
}
