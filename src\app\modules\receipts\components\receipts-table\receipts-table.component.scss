.table-container {
  width: 100%;
  overflow-y: auto;
  overflow-x: auto; /* Add horizontal scrolling */
}

.receipts-table {
  margin: 0px !important;
  th {
    font-family: "Inter" !important;
    font-weight: 600;
    padding-left: 20px;
    font-size: 14px;
    color: #022b3a;
    border-top: 3px solid #e1e5f2;
    border-bottom: 3px solid #e1e5f2;
    width: 250px !important;
    text-wrap: nowrap !important;
  }
  td {
    padding-left: 20px;
    font-size: 14px;
    color: #022b3a;
    border-bottom: 1px solid #e1e5f2;
    width: 250px !important;
    text-wrap: nowrap !important;
  }
  .icons {
    display: flex;
    height: 100%;
    cursor: pointer;
  }
}

tr.detail-row {
  height: 0;
}

tr.element-row:not(.expanded-row):hover {
  background: #edf5fd;
}

tr.element-row:not(.expanded-row):active {
  background: #efefef;
}

.element-row td {
  border-bottom-width: 0;
}

.element-detail {
  overflow: hidden;
  width: 100%;
}

.expanded-row {
  background-color: #edf5fd;
}
.detail-row {
  background-color: #edf5fd;
}

tr.detail-row {
  height: 0;
  background-color: #edf5fd;
}

.mat-table {
  table-layout: fixed;
  min-width: 300px;
}

.mat-header-cell.stickyEnd {
  position: sticky;
  left: 0;
  z-index: 1;
  background-color: red;
}

.icon {
  cursor: pointer;
  font-size: 20px;
  margin-top: 9px;
}

button {
  width: 100%;
  display: block;
  text-align: left;
}

::ng-deep.mat-menu-panel {
  max-height: 200px !important;
}

@media (max-width: 576px) {
  td a {
    font-size: 13px !important;
  }
  td,
  th {
    font-size: 13px !important;
  }
}

.column-menu-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0 5px;
}

.column-order-buttons {
  display: flex;
  align-items: center;

  button {
    width: 28px;
    height: 28px;
    line-height: 28px;

    mat-icon {
      font-size: 18px;
      line-height: 18px;
      height: 18px;
      width: 18px;
    }
  }
}

.cdk-drag-preview {
  border-radius: 4px;
  box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2),
              0 8px 10px 1px rgba(0, 0, 0, 0.14),
              0 3px 14px 2px rgba(0, 0, 0, 0.12);
  background-color: white;
  padding: 10px;
  font-family: "Inter" !important;
  font-weight: 600;
  font-size: 14px;
  color: #022b3a;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 48px;
  overflow: hidden;
  white-space: nowrap;
  width: 250px !important;
  opacity: 0.9;
  z-index: 1000;
}

.cdk-drag-placeholder {
  opacity: 0.3;
  background: #e1e5f2;
  border: 1px dashed #aaa;
  transition: transform 250ms ease;
}

.cdk-drag-animating {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.cdk-drop-list-dragging th:not(.cdk-drag-placeholder) {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

th[cdkDrag] {
  cursor: grab;

  &:hover {
    background-color: #f5f5f5;
  }

  &:active {
    cursor: grabbing;
  }
}

.drag-handle {
  color: #ccc;
  margin-left: 8px;

  &:hover {
    color: #555;
  }
}

.drag-header-cell {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

tr.mat-header-row {
  position: relative;
  height: 48px;
}
