import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Form<PERSON>uilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { AVAILABLE_CURRENCIES, CURRENCY_ORDER, CURRENCY_TRANSLATIONS } from '../../constants/available_currencies.const';
import { ProductPrice } from '../../models/product.model';

interface CurrencyInfo {
  symbol: string;
  name: string;
  symbol_native: string;
  code: string;
  name_plural: string;
  translation_key?: string;
  [key: string]: any;
}

@Component({
  selector: 'app-currency-price-input',
  templateUrl: './currency-price-input.component.html',
  styleUrls: ['./currency-price-input.component.scss']
})
export class CurrencyPriceInputComponent implements OnInit {
  @Input() existingPrices: { [currencyCode: string]: number } = {};
  @Output() pricesChanged = new EventEmitter<{ [currencyCode: string]: number }>();

  availableCurrencies: { [key: string]: CurrencyInfo } = AVAILABLE_CURRENCIES;
  currencyOrder = CURRENCY_ORDER;
  currencyTranslations: { [key: string]: string } = CURRENCY_TRANSLATIONS;
  currencyPrices: ProductPrice[] = [];
  unusedCurrencies: string[] = [];

  priceForm: FormGroup;
  currencyCodeControl: FormControl = new FormControl('', Validators.required);
  priceControl: FormControl = new FormControl('', [Validators.required, Validators.min(0)]);

  constructor(private fb: FormBuilder) {
    this.priceForm = this.fb.group({
      currencyCode: this.currencyCodeControl,
      price: this.priceControl
    });
  }

  ngOnInit(): void {
    this.loadExistingPrices();
    this.updateUnusedCurrencies();
  }

  private loadExistingPrices(): void {
    this.currencyPrices = [];
    if (this.existingPrices) {
      for (const [currencyCode, price] of Object.entries(this.existingPrices)) {
        if (this.currencyOrder.includes(currencyCode)) {
          this.currencyPrices.push({
            currencyCode,
            price
          });
        }
      }
    }
  }

  private updateUnusedCurrencies(): void {
    const usedCurrencies = this.currencyPrices.map(cp => cp.currencyCode);
    this.unusedCurrencies = this.currencyOrder
      .filter(code => !usedCurrencies.includes(code) && code !== 'RSD')
      .sort((a, b) => {
        const indexA = this.currencyOrder.indexOf(a);
        const indexB = this.currencyOrder.indexOf(b);
        return indexA - indexB;
      });

    if (this.unusedCurrencies.length > 0 && !this.currencyCodeControl.value) {
      this.currencyCodeControl.setValue(this.unusedCurrencies[0]);
    }
  }

  addCurrencyPrice(): void {
    if (this.priceForm.valid) {
      const newPrice: ProductPrice = {
        currencyCode: this.currencyCodeControl.value,
        price: this.priceControl.value
      };

      this.currencyPrices.push(newPrice);

      this.priceControl.reset();
      this.updateUnusedCurrencies();
      this.emitUpdatedPrices();
    } else {
      this.currencyCodeControl.markAsTouched();
      this.priceControl.markAsTouched();
    }
  }

  removeCurrencyPrice(index: number): void {
    this.currencyPrices.splice(index, 1);
    this.updateUnusedCurrencies();
    this.emitUpdatedPrices();
  }

  private emitUpdatedPrices(): void {
    const prices: { [currencyCode: string]: number } = {};
    this.currencyPrices.forEach(cp => {
      prices[cp.currencyCode] = cp.price;
    });
    this.pricesChanged.emit(prices);
  }

  getCurrencySymbol(code: string): string {
    return this.availableCurrencies[code]?.symbol || code;
  }

  getCurrencyName(code: string): string {
    return this.availableCurrencies[code]?.name || code;
  }

  getCurrencyTranslationKey(code: string): string {
    return this.currencyTranslations[code] || this.availableCurrencies[code]?.translation_key || '';
  }
}
