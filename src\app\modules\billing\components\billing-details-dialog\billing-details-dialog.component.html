<div class="container-fluid" id="billingDetails">
  <div class="row">
    <div class="col-md-12 d-flex justify-content-end">
      <button class="x-button" mat-dialog-close>
        <mat-icon>close</mat-icon>
      </button>
    </div>
  </div>
  <div class="row content" *transloco="let t">
    <mat-tab-group class="centered-links">
      <mat-tab label="{{ t('items_menu') }}" class="tab">
        <div
          class="container-fluid p-4 pt-2"
          *ngIf="filteredItems; else noData"
        >
          <app-detail-filters
            (filterEmitter)="filterItems($event)"
          ></app-detail-filters>
          <div class="row">
            <div class="col-12 table-container">
              <table class="itemsTable">
                <thead>
                  <th>{{ t("name") }}</th>
                  <th>{{ t("price") }}</th>
                  <th>{{ t("amounts") }}</th>
                </thead>
                <tbody>
                  <tr *ngFor="let item of filteredItems">
                    <td>{{ item.invoiceItemName }}</td>
                    <td>{{ item.invoiceItemUnitPrice }}</td>
                    <td>{{ item.invoiceItemQuantity }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
        <ng-template #noData>
          <div class="container-fluid p-4 pt-2 d-flex justify-content-center">
            <h4>{{ t("billing.no_items") }}</h4>
          </div>
        </ng-template>
      </mat-tab>

      <!-- NEXT_VERSION -->
      <!-- <mat-tab label="{{ t('history') }}" class="tab">
        <div class="container-fluid p-4 pt-2">
          <div class="row">
            <div class="col-12 table-container">
              <table class="itemsTable">
                <thead>
                  <th>{{ t("billing.payment_date") }}</th>
                  <th>Status</th>
                </thead>
                <tbody>
                  <tr *ngFor="let history of data.history">
                    <td>{{ history.date | fiscommDate }}</td>
                    <td>
                      <app-pill
                        [text]="getStatus(history.status).text"
                        [color]="getStatus(history.status).color"
                      >
                        {{ history.status }}
                      </app-pill>
                    </td>
                  </tr> 
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </mat-tab> -->

      <mat-tab label="{{ t('billing.instructions') }}" class="tab">
        <div class="container-fluid p-3" id="instructions">
          <div class="row">
            <div class="col-md-3">
              <h4>{{ t("billing.invoice_link") }}:</h4>
              <a href="{{ data.pdfUrl }}" target="_blank" class="mt-0">Link</a>
              <h4 class="mt-3">IPS QR:</h4>
              <img [src]="imageUrl" />
            </div>
            <div class="col-md-9">
              <app-payment-slip [data]="paymentSlipData"></app-payment-slip>
            </div>
          </div>
        </div>
      </mat-tab>
    </mat-tab-group>
  </div>
</div>
