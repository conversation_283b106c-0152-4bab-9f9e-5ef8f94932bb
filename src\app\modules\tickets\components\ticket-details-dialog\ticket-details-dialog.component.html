<h2 mat-dialog-title>{{ data.title }}</h2>
<mat-dialog-content class="d-flex align-items-center justify-content-center">
  <div class="container-fluid">
    <div
      *ngIf="data.status_name.sr == 'Closed'"
      class="row d-flex justify-content-between statusRow tag mb-4"
    >
      <div class="col-5">Tiket je zatvoren</div>
      <div class="col-5 d-flex justify-content-end">
        <a href="#" class="text-decoration-none">Otvori tiket ponovo</a>
      </div>
    </div>
    <div
      *ngIf="data.status_name.sr != 'Closed'"
      class="row d-flex justify-content-between statusRow tag-blue mb-4"
    >
      <div class="col-5">Status tiketa: {{ data.status_name.sr }}</div>
      <div class="col-5 d-flex justify-content-end">
        <a href="#" class="text-decoration-none">Zatvor<PERSON> tiket</a>
      </div>
    </div>
    <div class="row" *ngIf="requester">
      <h4>Komentari</h4>
      <div class="col-12 comment-container">
        <div class="row d-flex justify-content-between align-items-center">
          <div class="col-8 d-flex align-items-center">
            <app-initals-avatar
              name="{{ requester.name }}"
            ></app-initals-avatar>
            <p>{{ requester.name | emailSlice }}</p>
            <p class="tag">Vlasnik</p>
          </div>
          <div class="col-4 d-flex align-items-center justify-content-end">
            <p class="date_text">{{ data.created_at | fiscommDate }}</p>
          </div>
          <div class="col-12">
            <p class="smaller">{{ requester.email }}</p>
          </div>
        </div>
        <div class="row comment">
          <pre [innerHTML]="findAndLinkifyUrls(data.body)"></pre>
        </div>
      </div>
    </div>
    <div class="row mb-3 mt-3">
      <div class="col-12 p-0">
        <textarea
          [formControl]="textControl"
          rows="3"
          placeholder="Napisite neki komentar..."
        ></textarea>
      </div>
      <div class="col-6 p-0 mt-2">
        <button (click)="addComment()">Dodaj komentar</button>
      </div>
    </div>

    <div class="row" *ngIf="comments && comments.length > 0">
      <div
        class="col-12 comment-container mt-4"
        *ngFor="let comment of comments"
      >
        <div class="row d-flex justify-content-between align-items-center">
          <div class="col-8 d-flex align-items-center">
            <app-initals-avatar
              name="{{ comment.author.name }}"
            ></app-initals-avatar>
            <p>{{ comment.author.name | emailSlice }}</p>
            <p class="tag-blue">Operator</p>
          </div>
          <div class="col-4 d-flex align-items-center justify-content-end">
            <p class="date_text">{{ comment.created_at | fiscommDate }}</p>
          </div>
        </div>
        <div class="row comment">
          <pre [innerHTML]="comment.body"></pre>
        </div>
        <!-- <div
        class="row d-flex justify-content-between footer align-items-center"
      >
        <div class="col-6 d-flex">
          <p>Prilog:</p>
          <span class="img-info d-flex align-items-center">
            <p>3823.png</p>
            <mat-icon class="download-icon">download_circle</mat-icon></span
          >
        </div>
        <div class="col-3 d-flex justify-content-end">
          <mat-icon class="material-icons-outlined me-2">thumb_up</mat-icon>
          <mat-icon class="material-icons-outlined">thumb_down</mat-icon>
        </div>
      </div> -->
      </div>
    </div>
  </div>
</mat-dialog-content>
<mat-dialog-actions class="buttons d-flex justify-content-between mt-2">
  <app-danger-btn [mat-dialog-close]="false" text="Otkazi"></app-danger-btn>
</mat-dialog-actions>
