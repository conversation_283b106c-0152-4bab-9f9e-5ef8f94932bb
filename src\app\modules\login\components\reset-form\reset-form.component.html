<div class="col-12 col-md-12 d-flex flex-column justify-content-center align-items-center" id="resetPassForm" *transloco="let t">
    <form *ngIf="resetForm" [formGroup]="resetForm" class="form w-100 d-flex flex-column  justify-content-center align-items-center" (submit)="reset()">
        <app-fiscomm-input appearance="outline" class="w-100 input" type="password" placeholder="{{t('login_page.new_password')}}" [control]="getPasswordControl()"></app-fiscomm-input>
        <div class="col-12 error" *ngIf="getPasswordControl().invalid && getPasswordControl().errors && 
                (getPasswordControl().dirty || getPasswordControl().touched)">
            <small class="text-danger"
                *ngIf="getPasswordControl().hasError('required')">
                {{t('login_page.required_field')}}
            </small>
            <small class="text-danger"
                *ngIf="getPasswordControl().hasError('minlength')">
                {{t('login_page.min_lenght')}}
            </small>
        </div>
        <app-fiscomm-input appearance="outline" class="mt-3 w-100 input" type="password" placeholder="{{t('login_page.new_password_confirm')}}" [control]="getConfirmPasswordControl()"></app-fiscomm-input>
        <div class="col-12 error" *ngIf="getConfirmPasswordControl().invalid && getConfirmPasswordControl().errors && 
        (getConfirmPasswordControl().dirty || getConfirmPasswordControl().touched)">
            <small class="text-danger"
                *ngIf="getConfirmPasswordControl().hasError('required')">
                {{t('login_page.required_field')}}
            </small>
            <small class="text-danger"
                *ngIf="getConfirmPasswordControl().hasError('minlength')">
                {{t('login_page.min_lenght')}}
            </small>
        </div>
        <div class="col-12 error mt-2" *ngIf="!doPasswordsMatch()">
            <small class="text-danger">
                {{t('login_page.password_match')}}
            </small>
        </div>
        <button [disabled]="isDisabled()" type="submit" class="w-50 m-auto mt-4 btn-primary" mat-raised-button> {{t('login_page.change')}}</button>
    </form>
</div>

