import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SettingsComponent } from './containers/settings/settings.component';
import { RouterModule, Routes } from '@angular/router';
import { SharedModule } from 'src/app/shared/shared.module';
import { ReportSettingsComponent } from './containers/report-settings/report-settings.component';
import { ReceiptSettingsComponent } from './containers/receipt-settings/receipt-settings.component';
import { SettingsNavbarComponent } from './components/settings-navbar/settings-navbar.component';
import { EInvoiceSettingsComponent } from './containers/e-invoice-settings/e-invoice-settings.component';
import { SettingsContainerComponent } from './components/settings-container/settings-container.component';
import { ReceiptPreviewComponent } from './components/receipt-preview/receipt-preview.component';
import { CashRegisterSettingsComponent } from './containers/cash-register-settings/cash-register-settings.component';
import { PasswordSettingsComponent } from './containers/password-settings/password-settings.component';

const routes: Routes = [
  {
    path: '',
    component: SettingsComponent,
  },
  {
    path: 'izvestaji',
    component: ReportSettingsComponent,
  },
  {
    path: 'racuni',
    component: ReceiptSettingsComponent,
  },
  {
    path: 'e-fakture',
    component: EInvoiceSettingsComponent,
  },
  {
    path: 'kasa',
    component: CashRegisterSettingsComponent,
  },
  {
    path: 'password',
    component: PasswordSettingsComponent,
  },
];

@NgModule({
  declarations: [
    SettingsComponent,
    ReportSettingsComponent,
    ReceiptSettingsComponent,
    SettingsNavbarComponent,
    EInvoiceSettingsComponent,
    SettingsContainerComponent,
    ReceiptPreviewComponent,
    CashRegisterSettingsComponent,
    PasswordSettingsComponent,
  ],
  imports: [CommonModule, SharedModule, RouterModule.forChild(routes)],
})
export class SettingsModule {}
